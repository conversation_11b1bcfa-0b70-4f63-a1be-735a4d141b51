import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * QR 코드 전체 통계를 가져옵니다.
 * @returns {Promise<object>} 총 QR 코드 수와 스캔 수 정보
 */
export const getQrTotalStatistics = async () => {
  try {
    // 공통 API 호출 함수를 사용하여 요청 처리
    // 인증 토큰은 인터셉터에서 자동으로 추가됨
    const response = await executeApiCall(
      () => apiClient.get('/statistics/qr/total'),
      'QR 코드 통계를 가져오는데 실패했습니다.'
    );

    return response.data; // executeApiCall이 이미 response.data.data를 반환함
  } catch (error) {
    const errorMessage = handleApiError(error, 'QR 코드 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 기간별 QR 코드 통계를 가져옵니다.
 * @param {string} startDate 시작일 (YYYY-MM-DD 형식)
 * @param {string} endDate 종료일 (YYYY-MM-DD 형식)
 * @param {number} [projectId] 프로젝트 ID (선택적)
 * @returns {Promise<object>} 기간별 QR 코드 생성 및 스캔 통계
 */
export const getQrStatisticsByPeriod = async (startDate, endDate, projectId = null) => {
  try {
    // 직접 API 호출하여 응답 구조 확인
    let url = '/statistics/qr/chart';
    let params = {
      startDate: startDate,
      endDate: endDate
    };

    // 프로젝트 ID가 있는 경우 프로젝트별 QR 통계 API 사용
    if (projectId) {
      url = '/statistics/qr/project/chart';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, { params });

    // 응답 구조 확인 및 데이터 추출
    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        // 표준 응답 구조: { success: true, data: { ... } }
        return response.data;
      } else {
        // 다른 응답 구조
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('기간별 QR 코드 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '기간별 QR 코드 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 기간별 관리자 통계를 가져옵니다.
 * @param {string} startDate 시작일 (YYYY-MM-DD 형식)
 * @param {string} endDate 종료일 (YYYY-MM-DD 형식)
 * @param {number} [projectId] 프로젝트 ID (선택적)
 * @returns {Promise<object>} 기간별 관리자 통계
 *
 * 응답 형식:
 * {
 *   success: true,
 *   data: {
 *     dates: ["2025-03-25", ...],
 *     createdCounts: {
 *       SUPER_ADMIN: [0, 1, ...],
 *       PROJECT_ADMIN: [0, 0, ...],
 *       SUB_ADMIN: [0, 0, ...],
 *       VIEWER: [0, 0, ...]
 *     }
 *   }
 * }
 */
export const getAdminStatisticsByPeriod = async (startDate, endDate, projectId = null) => {
  try {
    let url = '/statistics/admin/chart';
    let params = {
      startDate: startDate,
      endDate: endDate
    };

    // 프로젝트 ID가 있는 경우 프로젝트별 관리자 통계 API 사용
    if (projectId) {
      url = '/statistics/admin/project/chart';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, { params });

    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        // 표준 응답 구조: { success: true, data: { ... } }
        return response.data;
      } else {
        // 다른 응답 구조
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('기간별 관리자 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '기간별 관리자 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 기간별 이벤트 통계를 가져옵니다.
 * @param {string} startDate 시작일 (YYYY-MM-DD 형식)
 * @param {string} endDate 종료일 (YYYY-MM-DD 형식)
 * @param {number} [projectId] 프로젝트 ID (선택적)
 * @returns {Promise<object>} 기간별 이벤트 통계
 *
 * 응답 형식:
 * {
 *   success: true,
 *   data: {
 *     dates: ["2025-03-25", ...],
 *     createdCounts: [0, 1, ...]
 *   },
 *   error: {
 *     code: "string",
 *     message: "string",
 *     details: {
 *       additionalProp1: "string",
 *       additionalProp2: "string",
 *       additionalProp3: "string"
 *     }
 *   }
 * }
 */
export const getEventStatisticsByPeriod = async (startDate, endDate, projectId = null) => {
  try {
    // 직접 API 호출하여 응답 구조 확인
    let url = '/statistics/event/chart';
    let params = {
      startDate: startDate,
      endDate: endDate
    };

    // 프로젝트 ID가 있는 경우 프로젝트별 이벤트 통계 API 사용
    if (projectId) {
      url = '/statistics/event/project/chart';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, { params });

    // 응답 구조 확인 및 데이터 추출
    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        // 표준 응답 구조: { success: true, data: { ... } }
        return response.data;
      } else {
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('기간별 이벤트 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '기간별 이벤트 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트별 QR 코드 통계를 가져옵니다.
 * @param {number} projectId 프로젝트 ID
 * @returns {Promise<object>} 프로젝트별 QR 코드 통계
 *
 * 응답 형식:
 * {
 *   success: true,
 *   data: {
 *     totalQrCodes: 10,
 *     totalScans: 50
 *   },
 *   error: {
 *     code: "string",
 *     message: "string",
 *     details: {
 *       additionalProp1: "string",
 *       additionalProp2: "string",
 *       additionalProp3: "string"
 *     }
 *   }
 * }
 */
export const getProjectQrStatistics = async (projectId) => {
  try {
    // API 엔드포인트 수정
    let url = '/statistics/qr/total'; // 전체 QR 코드 통계 API
    let params = {};

    // 프로젝트 ID가 있는 경우 프로젝트별 QR 통계 API 사용
    if (projectId) {
      url = '/statistics/qr/project/total';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, {
      params: params
    });

    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        return response.data;
      } else {
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('프로젝트별 QR 코드 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '프로젝트별 QR 코드 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트별 이벤트 통계를 가져옵니다.
 * @param {number} projectId 프로젝트 ID
 * @returns {Promise<object>} 프로젝트별 이벤트 통계
 *
 * 응답 형식:
 * {
 *   success: true,
 *   data: {
 *     additionalProp1: 0,
 *     additionalProp2: 0,
 *     additionalProp3: 0
 *   },
 *   error: {
 *     code: "string",
 *     message: "string",
 *     details: {
 *       additionalProp1: "string",
 *       additionalProp2: "string",
 *       additionalProp3: "string"
 *     }
 *   }
 * }
 */
export const getProjectEventStatistics = async (projectId) => {
  try {
    // API 엔드포인트 수정
    let url = '/statistics/events/total'; // 전체 이벤트 통계 API
    let params = {};

    // 프로젝트 ID가 있는 경우 프로젝트별 이벤트 통계 API 사용
    if (projectId) {
      url = '/statistics/events/project/total';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, {
      params: params
    });

    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        return response.data;
      } else {
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('프로젝트별 이벤트 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '프로젝트별 이벤트 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트별 참가자 통계를 가져옵니다.
 * @param {number} projectId 프로젝트 ID
 * @returns {Promise<object>} 프로젝트별 참가자 통계
 *
 * 응답 형식:
 * {
 *   success: true,
 *   data: {
 *     additionalProp1: 0,
 *     additionalProp2: 0,
 *     additionalProp3: 0
 *   },
 *   error: {
 *     code: "string",
 *     message: "string",
 *     details: {
 *       additionalProp1: "string",
 *       additionalProp2: "string",
 *       additionalProp3: "string"
 *     }
 *   }
 * }
 */
export const getProjectAttendeeStatistics = async (projectId) => {
  try {
    // API 엔드포인트 수정
    let url = '/statistics/attendees/total'; // 전체 참가자 통계 API
    let params = {};

    // 프로젝트 ID가 있는 경우 프로젝트별 참가자 통계 API 사용
    if (projectId) {
      url = '/statistics/attendees/project/total';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, {
      params: params
    });

    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        return response.data;
      } else {
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('프로젝트별 참가자 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '프로젝트별 참가자 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 기간별 참가자 통계를 가져옵니다.
 * @param {string} startDate 시작일 (YYYY-MM-DD 형식)
 * @param {string} endDate 종료일 (YYYY-MM-DD 형식)
 * @param {number} [projectId] 프로젝트 ID (선택적)
 * @returns {Promise<object>} 기간별 참가자 통계
 *
 * 응답 형식:
 * {
 *   success: true,
 *   data: {
 *     dates: ["2025-03-25", ...],
 *     registeredCounts: [0, 3, ...]
 *   },
 *   error: {
 *     code: "string",
 *     message: "string",
 *     details: {
 *       additionalProp1: "string",
 *       additionalProp2: "string",
 *       additionalProp3: "string"
 *     }
 *   }
 * }
 */
export const getAttendeeStatisticsByPeriod = async (startDate, endDate, projectId = null) => {
  try {
    // 직접 API 호출하여 응답 구조 확인
    let url = '/statistics/attendee/chart';
    let params = {
      startDate: startDate,
      endDate: endDate
    };

    // 프로젝트 ID가 있는 경우 프로젝트별 참가자 통계 API 사용
    if (projectId) {
      url = '/statistics/attendee/project/chart';
      params.projectId = projectId;
    }

    const response = await apiClient.get(url, { params });

    // 응답 구조 확인 및 데이터 추출
    if (response && response.data) {

      if (response.data.success === true && response.data.data) {
        // 표준 응답 구조: { success: true, data: { ... } }
        return response.data;
      } else {
        // 다른 응답 구조
        return response.data;
      }
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('기간별 참가자 통계 API 오류:', error);
    const errorMessage = handleApiError(error, '기간별 참가자 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 관리자 통계를 가져옵니다.
 * @returns {Promise<object>} 관리자 통계 정보
 */
export const getAdminStatistics = async () => {
  try {
    // 공통 API 호출 함수를 사용하여 요청 처리
    // 인증 토큰은 인터셉터에서 자동으로 추가됨
    const response = await executeApiCall(
      () => apiClient.get('/statistics/admins/total'),
      '관리자 통계를 가져오는데 실패했습니다.'
    );

    return response.data; // executeApiCall이 이미 response.data.data를 반환함
  } catch (error) {
    const errorMessage = handleApiError(error, '관리자 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 이벤트 통계를 가져옵니다.
 * @returns {Promise<object>} 이벤트 통계 정보
 */
export const getEventStatistics = async () => {
  try {
    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get('/statistics/events/total'),
      '이벤트 통계를 가져오는데 실패했습니다.'
    );

    return response.data;
  } catch (error) {
    const errorMessage = handleApiError(error, '이벤트 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 참가자 통계를 가져옵니다.
 * @returns {Promise<object>} 참가자 통계 정보
 */
export const getAttendeeStatistics = async () => {
  try {
    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get('/statistics/attendees/total'),
      '참가자 통계를 가져오는데 실패했습니다.'
    );

    return response.data;
  } catch (error) {
    const errorMessage = handleApiError(error, '참가자 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 사용자(관리자) 통계를 가져옵니다.
 * @param {Object} options - 조회 옵션
 * @param {string} [options.startDate] - 시작일 (yyyy-MM-dd)
 * @param {string} [options.endDate] - 종료일 (yyyy-MM-dd)
 * @param {number} [options.projectId] - 프로젝트 ID
 * @param {string} [options.includeTypes] - 포함할 통계 유형 (쉼표로 구분된 문자열: basic,qr,event,landing,exchange,time)
 * @param {number} [options.topN] - 상위 몇 명의 사용자 통계를 조회할지 (기본값: 5)
 * @returns {Promise<Object>} 사용자 통계 데이터
 */
export const getUserStatistics = async (options = {}) => {
  try {
    const params = {};

    // 옵션 파라미터 설정
    if (options.startDate) params.startDate = options.startDate;
    if (options.endDate) params.endDate = options.endDate;
    if (options.projectId) params.projectId = options.projectId;
    if (options.includeTypes) params.includeTypes = options.includeTypes;
    if (options.topN) params.topN = options.topN;

    const response = await executeApiCall(
      () => apiClient.get('/statistics/user/stats', { params }),
      '사용자(관리자) 통계를 가져오는데 실패했습니다.'
    );

    return response;
  } catch (error) {
    const errorMessage = handleApiError(error, '사용자(관리자) 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * QR 코드 종합 통계를 조회합니다.
 * @param {Object} options - 조회 옵션
 * @param {string} [options.startDate] - 시작일 (yyyy-MM-dd)
 * @param {string} [options.endDate] - 종료일 (yyyy-MM-dd)
 * @param {number} [options.projectId] - 프로젝트 ID
 * @param {string} [options.includeTypes] - 포함할 통계 유형 (쉼표로 구분된 문자열: basic,type,status,device,time,exchange)
 * @returns {Promise<Object>} 종합 통계 데이터
 */
export const getQrComprehensiveStats = async (options = {}) => {
  try {
    const params = {};

    // 옵션 파라미터 설정
    if (options.startDate) params.startDate = options.startDate;
    if (options.endDate) params.endDate = options.endDate;
    if (options.projectId) params.projectId = options.projectId;
    if (options.includeTypes) params.includeTypes = options.includeTypes;

    // API 요청 시작 시간 기록
    const startTime = new Date();

    const response = await apiClient.get('/statistics/qr/stats', { params });

    // API 요청 완료 시간 기록
    const endTime = new Date();

    if (response.data && response.data.success) {
      return response.data;
    } else {
      console.error('API 응답 오류:', response.data);
      throw new Error(response.data?.error?.message || 'QR 코드 종합 통계를 가져오는데 실패했습니다.');
    }
  } catch (error) {
    console.error('QR 코드 종합 통계 API 오류 상세:', error);
    if (error.response) {
      console.error('오류 응답 데이터:', error.response.data);
      console.error('오류 응답 상태:', error.response.status);
      console.error('오류 응답 헤더:', error.response.headers);
    } else if (error.request) {
      console.error('요청은 전송되었으나 응답이 없음:', error.request);
    } else {
      console.error('요청 설정 중 오류 발생:', error.message);
    }

    const errorMessage = handleApiError(error, 'QR 코드 종합 통계를 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 사용량 통계를 가져옵니다.
 * @param {string} startDate 시작일 (YYYY-MM-DD 형식)
 * @param {string} endDate 종료일 (YYYY-MM-DD 형식)
 * @returns {Promise<object>} 사용량 통계 데이터
 */
export const getUsageStatistics = async (startDate, endDate) => {
  try {
    const response = await executeApiCall(
      () => apiClient.get('/statistics/usage', {
        params: {
          startDate,
          endDate,
        },
      }),
      '사용량 통계를 가져오는데 실패했습니다.'
    );
    return response;
  } catch (error) {
    const errorMessage = handleApiError(error, '사용량 통계를 가져오는데 실패했습니다.');
    throw new Error(errorMessage);
  }
};

// 이전 API 함수들은 유지하되 deprecated 표시
/**
 * @deprecated 새로운 getQrComprehensiveStats 함수를 사용하세요.
 * QR 코드 타입별 분포 통계를 조회합니다.
 * @param {number} [projectId] - 프로젝트 ID (선택적)
 * @returns {Promise<Object>} 타입별 분포 통계 데이터
 */
export const getQrTypeDistribution = async (projectId = null) => {
  console.warn('getQrTypeDistribution 함수는 더 이상 사용되지 않습니다. getQrComprehensiveStats 함수를 사용하세요.');
  const response = await getQrComprehensiveStats({
    projectId,
    includeTypes: 'type'
  });
  return {
    success: response.success,
    data: response.data.typeDistribution,
    message: response.message
  };
};

/**
 * @deprecated 새로운 getQrComprehensiveStats 함수를 사용하세요.
 * QR 코드 상태별 분포 통계를 조회합니다.
 * @param {number} [projectId] - 프로젝트 ID (선택적)
 * @returns {Promise<Object>} 상태별 분포 통계 데이터
 */
export const getQrStatusDistribution = async (projectId = null) => {
  console.warn('getQrStatusDistribution 함수는 더 이상 사용되지 않습니다. getQrComprehensiveStats 함수를 사용하세요.');
  const response = await getQrComprehensiveStats({
    projectId,
    includeTypes: 'status'
  });
  return {
    success: response.success,
    data: response.data.statusDistribution,
    message: response.message
  };
};

/**
 * @deprecated 새로운 getQrComprehensiveStats 함수를 사용하세요.
 * QR 코드 스캔 기기 통계를 조회합니다.
 * @param {number} [projectId] - 프로젝트 ID (선택적)
 * @returns {Promise<Object>} 스캔 기기 통계 데이터
 */
export const getQrDeviceStats = async (projectId = null) => {
  console.warn('getQrDeviceStats 함수는 더 이상 사용되지 않습니다. getQrComprehensiveStats 함수를 사용하세요.');
  const response = await getQrComprehensiveStats({
    projectId,
    includeTypes: 'device'
  });
  return {
    success: response.success,
    data: response.data.deviceStats,
    message: response.message
  };
};

/**
 * @deprecated 새로운 getQrComprehensiveStats 함수를 사용하세요.
 * QR 코드 스캔 시간 통계를 조회합니다.
 * @param {number} [projectId] - 프로젝트 ID (선택적)
 * @returns {Promise<Object>} 스캔 시간 통계 데이터
 */
export const getQrTimeStats = async (projectId = null) => {
  console.warn('getQrTimeStats 함수는 더 이상 사용되지 않습니다. getQrComprehensiveStats 함수를 사용하세요.');
  const response = await getQrComprehensiveStats({
    projectId,
    includeTypes: 'time'
  });
  return {
    success: response.success,
    data: response.data.timeStats,
    message: response.message
  };
};

/**
 * @deprecated 새로운 getQrComprehensiveStats 함수를 사용하세요.
 * 교환권 QR 코드 통계를 조회합니다.
 * @param {number} [projectId] - 프로젝트 ID (선택적)
 * @returns {Promise<Object>} 교환권 통계 데이터
 */
export const getQrExchangeStats = async (projectId = null) => {
  console.warn('getQrExchangeStats 함수는 더 이상 사용되지 않습니다. getQrComprehensiveStats 함수를 사용하세요.');
  const response = await getQrComprehensiveStats({
    projectId,
    includeTypes: 'exchange'
  });
  return {
    success: response.success,
    data: response.data.exchangeStats,
    message: response.message
  };
};
