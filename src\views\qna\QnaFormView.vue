<template>
  <div class="qna-form-container">
    <h2>{{ isEdit ? '질문 수정' : '새 질문 등록' }}</h2>
    
    <!-- 로딩 및 에러 처리 -->
    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <p>{{ isEdit ? '질문 정보를 불러오는 중...' : '처리 중...' }}</p>
    </div>
    
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button v-if="isEdit" @click="fetchQuestionData" class="retry-button">다시 시도</button>
    </div>

    <form v-else @submit.prevent="handleSubmit" class="question-form">
      <div class="form-group">
        <label for="title">제목 <span class="required">*</span></label>
        <input 
          type="text" 
          id="title" 
          v-model="formData.title" 
          required
          placeholder="질문 제목을 입력하세요"
          class="form-control"
        />
      </div>
      
      <div class="form-group">
        <label for="content">내용 <span class="required">*</span></label>
        <textarea 
          id="content" 
          v-model="formData.content" 
          required
          placeholder="질문 내용을 자세히 적어주세요"
          class="form-control"
          rows="8"
        ></textarea>
      </div>
      
      <div class="form-actions">
        <button type="button" @click="goBack" class="cancel-button">취소</button>
        <button type="submit" class="submit-button">{{ isEdit ? '수정' : '등록' }}</button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQnaStore } from '@/stores/qnaStore';
import { useAuthStore } from '@/stores/auth';

const route = useRoute();
const router = useRouter();
const qnaStore = useQnaStore();
const authStore = useAuthStore();

// 상태 변수
const loading = ref(false);
const error = ref(null);
const formData = ref({
  title: '',
  content: '',
  projectId: null
});

// 수정 모드인지 여부 확인
const isEdit = computed(() => {
  return !!route.params.questionId;
});

// 컴포넌트 마운트 시 필요한 데이터 로딩
onMounted(async () => {
  // 현재 프로젝트 ID 설정
  if (authStore.currentProject && authStore.currentProject.projectId) {
    formData.value.projectId = authStore.currentProject.projectId;
  } else {
    // 프로젝트 ID가 없을 경우 오류 표시
    error.value = '프로젝트를 선택해주세요.';
    return;
  }
  
  // 수정 모드인 경우 기존 질문 데이터 조회
  if (isEdit.value) {
    await fetchQuestionData();
  }
});

// 질문 데이터 조회 (수정 모드)
const fetchQuestionData = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const questionId = route.params.questionId;
    await qnaStore.fetchQuestionByIdAction(questionId);
    
    if (qnaStore.currentQuestion) {
      // 기존 데이터로 폼 초기화
      formData.value = {
        title: qnaStore.currentQuestion.title || '',
        content: qnaStore.currentQuestion.content || '',
        projectId: qnaStore.currentQuestion.projectId || authStore.currentProject?.projectId
      };
    } else {
      error.value = '질문 정보를 불러올 수 없습니다.';
    }
  } catch (err) {
    error.value = '질문 정보를 불러오는 중 오류가 발생했습니다.';
    console.error('질문 조회 오류:', err);
  } finally {
    loading.value = false;
  }
};

// 폼 제출 처리
const handleSubmit = async () => {
  loading.value = true;
  error.value = null;

  try {
    // 유효성 검사
    if (!formData.value.title.trim()) {
      error.value = '제목을 입력해주세요.';
      return;
    }
    
    if (!formData.value.content.trim()) {
      error.value = '내용을 입력해주세요.';
      return;
    }
    
    // 현재 사용자 정보 추가
    const questionData = {
      ...formData.value,
      createUserEmail: authStore.user?.email
    };
    
    let result;
    
    if (isEdit.value) {
      // 질문 수정
      const questionId = route.params.questionId;
      result = await qnaStore.updateQuestionAction(questionId, questionData);
    } else {
      // 새 질문 생성
      result = await qnaStore.createQuestionAction(questionData);
    }
    
    if (result.success) {
      alert(isEdit.value ? '질문이 수정되었습니다.' : '질문이 등록되었습니다.');
      router.push({ name: 'qna-management' });
    } else {
      error.value = result.error || '처리 중 오류가 발생했습니다.';
    }
  } catch (err) {
    error.value = '처리 중 오류가 발생했습니다.';
    console.error('질문 저장 오류:', err);
  } finally {
    loading.value = false;
  }
};

// 취소 또는 뒤로 가기
const goBack = () => {
  try {
    // 절대 경로를 사용하여 라우팅 문제 해결
    router.push('/qna');
  } catch (err) {
    console.error('라우팅 오류:', err);
    // 페이지 이동이 실패한 경우 기본 페이지로 돌아가기
    window.location.href = '/qna';
  }
};
</script>

<style scoped>
.qna-form-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.required {
  color: #dc3545;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
}

textarea.form-control {
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.cancel-button, .submit-button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button {
  background-color: #6c757d;
  color: white;
}

.submit-button {
  background-color: #4a6cf7;
  color: white;
}

.loading, .error {
  padding: 40px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a6cf7;
  width: 30px;
  height: 30px;
  margin: 0 auto 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  padding: 8px 16px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.error {
  color: #dc3545;
}
</style>
