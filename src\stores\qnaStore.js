// QnA 상태 관리 스토어
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import * as qnaApi from '@/api/qnaApi';

export const useQnaStore = defineStore('qna', () => {
  // 상태
  const questions = ref([]);
  const currentQuestion = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const totalItems = ref(0);
  const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록
  
  // 페이지네이션, 필터 상태
  const currentPage = ref(0); // 서버에서는 페이지 번호가 0부터 시작함
  const pageSize = ref(10);
  const searchKeyword = ref('');
  const searchType = ref('title'); // 'title', 'content', 'author' 등
  const answerType = ref(''); // 답변 유형 필터
  const answerTypes = ref([]); // 가능한 답변 유형 목록
  
  // 서버 응답 구조에서 ID 필드명
  const QUESTION_ID_FIELD = 'qnaQuestionId';

  // 계산된 속성
  const hasQuestions = computed(() => questions.value.length > 0);
  const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));
  
  // UI용 현재 페이지 (1부터 시작하는 표시용 번호)
  const displayPage = computed(() => currentPage.value + 1);
  
  // 액션
  // 질문 목록 조회
  const fetchQuestionsAction = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const params = {
        page: currentPage.value, // 서버에서는 0부터 시작하는 페이지 번호 사용
        size: pageSize.value,
      };
      
      // 검색어가 있는 경우 검색 조건 추가
      if (searchKeyword.value) {
        params.searchKeyword = searchKeyword.value;
        params.searchType = searchType.value;
      }
      
      // 답변 유형 필터가 있는 경우 추가
      if (answerType.value) {
        params.answerType = answerType.value;
      }
      
      const response = await qnaApi.getQuestions(params);
      
      if (response.success) {
        // 질문 목록 및 페이지네이션 정보 처리
        if (response.data.data && Array.isArray(response.data.data.content)) {
          questions.value = response.data.data.content;
          totalItems.value = response.data.data.totalElements || questions.value.length;
          
          // 현재 페이지 번호 업데이트 (서버에서는 0부터 시작하는 번호 사용)
          if (response.data.data.number !== undefined) {
            currentPage.value = response.data.data.number;
          }
          
          // 검색 유형 목록 업데이트
          if (response.data.data.availableSearchTypes && Array.isArray(response.data.data.availableSearchTypes)) {
            availableSearchTypes.value = response.data.data.availableSearchTypes;
          }
          
          // 답변 유형 목록 업데이트
          if (response.data.data.answerSearchTypes) {
            
            // 데이터 형식 확인 및 적절한 처리
            const rawData = response.data.data.answerSearchTypes;
            
            if (Array.isArray(rawData)) {
              // 이미 배열인 경우 (각 항목이 {value, label} 형태인지 확인)
              answerTypes.value = rawData
                .map(item => {
                  if (typeof item === 'object' && item.value !== undefined && item.label !== undefined) {
                    return item; // 이미 알맞은 형태
                  } else {
                    return { value: item, label: item }; // 기본 형태로 변환
                  }
                })
                // 값이 비어있거나 undefined인 항목 제외
                .filter(item => item.value !== '' && item.value !== undefined && item.label !== '' && item.label !== undefined);
            } else if (typeof rawData === 'object') {
              // 객체인 경우 - Object.entries로 변환
              answerTypes.value = Object.entries(rawData)
                .map(([key, value]) => {
                  return { value: key, label: value };
                })
                // 값이 비어있거나 undefined인 항목 제외
                .filter(item => item.value !== '' && item.value !== undefined && item.label !== '' && item.label !== undefined);
            } else {
              // 그 외의 경우, 빈 배열로 초기화
              answerTypes.value = [];
              console.error('서버에서 받은 답변 유형 데이터 형식이 유효하지 않음:', rawData);
            }
            
          }
        
        } else {
          // 예상치 못한 응답 구조일 경우 (개발용 로그)
          console.error('예상치 못한 응답 구조:', response.data.data);
          questions.value = [];
          totalItems.value = 0;
        }
      } else {
        error.value = response.data.error.message;
        console.error('질문 목록 실패:', response.error);
      }
    } catch (err) {
      error.value = '질문 목록을 불러오는 도중 오류가 발생했습니다';
      console.error('질문 목록 조회 오류:', err);
    } finally {
      loading.value = false;
    }
  };
  
  // 특정 질문 상세 조회
  const fetchQuestionByIdAction = async (questionId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await qnaApi.getQuestionById(questionId);
      
      if (response.success) {
        currentQuestion.value = response.data;
      } else {
        error.value = '질문 상세 정보를 불러오는 데 실패했습니다';
      }
    } catch (err) {
      error.value = '질문 상세 정보를 불러오는 도중 오류가 발생했습니다';
      console.error('질문 상세 조회 오류:', err);
    } finally {
      loading.value = false;
    }
  };
  
  // 새 질문 생성
  const createQuestionAction = async (questionData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await qnaApi.createQuestion(questionData);
      
      if (response.success) {
        return { success: true, data: response.data };
      } else {
        error.value = '질문을 생성하는 데 실패했습니다';
        return { success: false, error: error.value };
      }
    } catch (err) {
      error.value = '질문을 생성하는 도중 오류가 발생했습니다';
      console.error('질문 생성 오류:', err);
      return { success: false, error: error.value };
    } finally {
      loading.value = false;
    }
  };
  
  // 특정 질문 수정
  const updateQuestionAction = async (questionId, questionData) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await qnaApi.updateQuestion(questionId, questionData);
      
      if (response.success) {
        if (currentQuestion.value && currentQuestion.value[QUESTION_ID_FIELD] === questionId) {
          currentQuestion.value = response.data;
        }
        return { success: true, data: response.data };
      } else {
        error.value = '질문을 수정하는 데 실패했습니다';
        return { success: false, error: error.value };
      }
    } catch (err) {
      error.value = '질문을 수정하는 도중 오류가 발생했습니다';
      console.error('질문 수정 오류:', err);
      return { success: false, error: error.value };
    } finally {
      loading.value = false;
    }
  };
  
  // 특정 질문 삭제
  const deleteQuestionAction = async (questionId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await qnaApi.deleteQuestion(questionId);
      
      if (response.success) {
        questions.value = questions.value.filter(q => q[QUESTION_ID_FIELD] !== questionId);
        if (currentQuestion.value && currentQuestion.value[QUESTION_ID_FIELD] === questionId) {
          currentQuestion.value = null;
        }
        return { success: true };
      } else {
        error.value = '질문을 삭제하는 데 실패했습니다';
        return { success: false, error: error.value };
      }
    } catch (err) {
      error.value = '질문을 삭제하는 도중 오류가 발생했습니다';
      console.error('질문 삭제 오류:', err);
      return { success: false, error: error.value };
    } finally {
      loading.value = false;
    }
  };
  
  // 질문에 답변 추가하기
  const createAnswerAction = async (questionId, answerContent) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await qnaApi.createAnswer(questionId, answerContent);
      
      if (response.success) {
        // 해당 질문 찾기
        const question = questions.value.find(q => q[QUESTION_ID_FIELD] === questionId);
        if (question) {
          // 답변이 없으면 배열 생성, 있으면 기존 배열에 추가
          if (!question.answer) {
            question.answer = response.data;
          } else {
            question.answer = response.data;
          }
        }
        return { success: true, data: response.data };
      } else {
        error.value = '답변을 추가하는 데 실패했습니다';
        return { success: false, error: error.value };
      }
    } catch (err) {
      error.value = '답변을 추가하는 도중 오류가 발생했습니다';
      console.error('답변 추가 오류:', err);
      return { success: false, error: error.value };
    } finally {
      loading.value = false;
    }
  };
  
  // 답변 수정하기
  const updateAnswerAction = async (answerId, answerContent, questionId) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await qnaApi.updateAnswer(answerId, answerContent);
      
      if (response.success) {
        // 해당 질문 찾기
        const question = questions.value.find(q => q[QUESTION_ID_FIELD] === questionId);
        if (question && question.answer) {
          // 답변 업데이트
          question.answer = response.data;
        }
        return { success: true, data: response.data };
      } else {
        error.value = '답변을 수정하는 데 실패했습니다';
        return { success: false, error: error.value };
      }
    } catch (err) {
      error.value = '답변을 수정하는 도중 오류가 발생했습니다';
      console.error('답변 수정 오류:', err);
      return { success: false, error: error.value };
    } finally {
      loading.value = false;
    }
  };
  
  // 페이지 변경
  const setPage = (page) => {
    // 클라이언트에서는 1부터 시작하는 페이지번호 UI를 사용하지만,
    // 서버에는 0부터 시작하는 값을 전송해야 함
    currentPage.value = page - 1;
    fetchQuestionsAction();
  };
  
  // 검색 필터 설정
  const setSearchFilter = (query, type = 'title', answer = '') => {
    searchKeyword.value = query;
    searchType.value = type;
    answerType.value = answer;
    currentPage.value = 0; // 검색 시 첫 페이지로 이동
    fetchQuestionsAction();
  };
  
  // 페이지 크기 설정
  const setPageSize = (size) => {
    pageSize.value = size;
    currentPage.value = 0; // 페이지 크기 변경 시 첫 페이지로 이동
  };
  
  // 스토어 상태 초기화
  const resetStore = () => {
    questions.value = [];
    currentQuestion.value = null;
    loading.value = false;
    error.value = null;
    totalItems.value = 0;
    currentPage.value = 0;
    searchKeyword.value = '';
    searchType.value = 'title';
    answerType.value = '';
  };

  return {
    // 상태
    questions,
    currentQuestion,
    loading,
    error,
    totalItems,
    currentPage,
    pageSize,
    searchKeyword,
    searchType,
    answerType,
    answerTypes,
    availableSearchTypes,
    
    // 계산된 속성
    hasQuestions,
    totalPages,
    displayPage,
    
    // 액션
    fetchQuestionsAction,
    fetchQuestionByIdAction,
    createQuestionAction,
    updateQuestionAction,
    deleteQuestionAction,
    createAnswerAction,
    updateAnswerAction,
    setPage,
    setSearchFilter,
    setPageSize,
    resetStore
  };
});
