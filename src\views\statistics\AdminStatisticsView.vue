<template>
  <div class="statistics-view">
    <!-- 날짜 필터 추가 -->
    <div class="filters">
      <div class="filter-group">
        <label for="startDate">시작 날짜:</label>
        <input
          id="startDate"
          type="date"
          v-model="startDate"
          class="date-input"
          @change="validateDates"
        />
        
        <label for="endDate">종료 날짜:</label>
        <input
          id="endDate"
          type="date"
          v-model="endDate"
          class="date-input"
          :min="startDate"
          @change="validateDates"
        />
        
        <button @click="searchStatistics" class="search-btn" :disabled="!isValidDateRange">검색</button>
      </div>
      <div v-if="dateError" class="date-error-message">
        {{ dateError }}
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>통계 데이터를 불러오는 중입니다...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="loadAdminStatistics" class="retry-button">다시 시도</button>
    </div>

    <div v-if="!isLoading && !error && isChartLoading" class="loading-container chart-loading">
      <div class="loading-spinner"></div>
      <p>차트를 생성하는 중입니다...</p>
      <div class="loading-progress">잠시만 기다려주세요</div>
    </div>

    <div v-if="!isLoading && !error" class="statistics-container">
      <!-- 관리자 역할별 분포 -->
      <div class="stat-card">
        <h2>관리자 역할별 분포</h2>
        <div class="chart-container" v-show="adminRoleDistribution && !isChartLoading">
          <div class="chart-wrapper" id="adminRoleChartContainer">
            <canvas ref="adminRoleChart" id="adminRoleCanvas"></canvas>
          </div>
          <div class="data-table" v-if="adminRoleDistribution">
            <table>
              <thead>
                <tr>
                  <th>역할</th>
                  <th>인원수</th>
                  <th>비율</th>
                </tr>
              </thead>
              <tbody>
                <template v-if="adminRoleDistribution && Object.keys(adminRoleDistribution.counts).length > 0">
                  <tr v-for="(count, role) in adminRoleDistribution.counts" :key="role">
                    <td>{{ getRoleName(role) }}</td>
                    <td>{{ count }}</td>
                    <td>{{ adminRoleDistribution.percentage[role] || '0' }}%</td>
                  </tr>
                </template>
                <template v-else>
                  <tr>
                    <td colspan="3" class="no-data-cell">표시할 데이터가 없습니다</td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 로딩 표시 -->
        <div v-if="isChartLoading && adminRoleDistribution" class="chart-placeholder">
          <div class="mini-spinner"></div>
          <span>차트를 생성하는 중입니다...</span>
        </div>

        <!-- 데이터 없음 표시 -->
        <div v-if="!adminRoleDistribution && !isLoading" class="no-data">
          <div class="no-data-icon">👥</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">이 차트에 표시할 관리자 역할 분포 데이터가 없습니다.</div>
        </div>
      </div>

      <!-- 관리자별 최다 URI 요청 -->
      <div class="stat-card">
        <h2>관리자별 최다 URI 요청</h2>
        <div v-if="adminActivityStats && Object.keys(adminActivityStats).length > 0 && !isChartLoading">
          <div class="data-table">
            <table>
              <thead>
                <tr>
                  <th>관리자 이메일</th>
                  <th>최다 요청 URI</th>
                  <th>요청 횟수</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(stats, adminEmail) in adminActivityStats" :key="adminEmail">
                  <td>{{ adminEmail }}</td>
                  <td>{{ stats.requestUri }}</td>
                  <td>{{ stats.count }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <!-- 데이터 없음 표시 -->
        <div v-if="(!adminActivityStats || Object.keys(adminActivityStats).length === 0) && !isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">📊</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">표시할 관리자별 API 요청 데이터가 없습니다.</div>
        </div>
        <!-- 로딩 표시 (차트 로딩과 같이 표시될 수 있음) -->
        <div v-if="isChartLoading && adminActivityStats" class="chart-placeholder">
          <div class="mini-spinner"></div>
          <span>데이터를 불러오는 중입니다...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { useAuthStore } from '@/stores/auth';
import Chart from 'chart.js/auto';
import { getUserStatistics } from '@/api/statistics';

const authStore = useAuthStore();

// 상태 변수
const isLoading = ref(true);
const isChartLoading = ref(false);
const error = ref(null);
const adminRoleDistribution = ref(null);
const adminActivityStats = ref(null); // adminStat 데이터를 저장
// 데이터 로드 상태 추적 - 초기화
const dataLoaded = ref(false);

// 날짜 관련 상태 추가
const startDate = ref('');
const endDate = ref('');
const dateError = ref('');
const isValidDateRange = ref(true);

// formatDate 함수 추가
const formatDate = (date) => {
  if (!date) return null;
  const d = new Date(date);
  const year = d.getFullYear();
  const month = (`0${d.getMonth() + 1}`).slice(-2);
  const day = (`0${d.getDate()}`).slice(-2);
  return `${year}-${month}-${day}`;
};

// 날짜를 비교가능한 형식으로 변환 (시간 정보 제거)
const getComparableDate = (dateStr) => {
  const date = new Date(dateStr);
  date.setHours(0, 0, 0, 0);
  return date;
};

// 날짜 유효성 검사 함수 추가
const validateDates = () => {
  dateError.value = '';
  isValidDateRange.value = true;

  if (!startDate.value || !endDate.value) {
    dateError.value = '시작일과 종료일을 모두 입력해주세요.';
    isValidDateRange.value = false;
    return;
  }

  const start = getComparableDate(startDate.value);
  const end = getComparableDate(endDate.value);
  
  if (isNaN(start.getTime())) {
    dateError.value = '유효한 시작 날짜를 입력해주세요.';
    isValidDateRange.value = false;
    return;
  }
  
  if (isNaN(end.getTime())) {
    dateError.value = '유효한 종료 날짜를 입력해주세요.';
    isValidDateRange.value = false;
    return;
  }
  
  if (start > end) {
    dateError.value = '시작일은 종료일보다 이전이어야 합니다.';
    isValidDateRange.value = false;
    return;
  }

  // 모든 검증 통과
  isValidDateRange.value = true;
};

// 검색 버튼 클릭 시 유효성 검사 후 데이터 로드 함수 추가
const searchStatistics = () => {
  validateDates();
  if (isValidDateRange.value) {
    // 데이터 로드 상태 초기화
    dataLoaded.value = false; 
    loadAdminStatistics();
  }
};

// 차트 참조
const adminRoleChart = ref(null);

// 차트 인스턴스 저장
const charts = ref({});

// 현재 프로젝트 ID
const currentProjectId = computed(() => {
  return authStore.currentProject?.projectId;
});

// 역할 이름 변환 함수
const getRoleName = (role) => {
  const roleMap = {
    'SUPER_ADMIN': '슈퍼 관리자',
    'PROJECT_ADMIN': '프로젝트 관리자',
    'SUB_ADMIN': '보조 관리자',
    'VIEWER': '뷰어'
  };
  return roleMap[role] || role;
};

// 한글 역할 이름을 영문 키로 변환하는 맵
const roleNameToKeyMap = {
  '수퍼 관리자': 'SUPER_ADMIN',
  '프로젝트 관리자': 'PROJECT_ADMIN',
  '팀 멤버 - 서브 관리자': 'SUB_ADMIN',
  '팀 멤버 - 뷰어': 'VIEWER'
};

// 관리자 통계 데이터 로드 - 날짜 파라미터 추가
const loadAdminStatistics = async () => {
  isLoading.value = true;
  isChartLoading.value = true;
  error.value = null;
  // 데이터 로드 전 상태 초기화
  dataLoaded.value = false;

  try {
    const projectId = currentProjectId.value;
    const options = {
      includeTypes: 'basic,qr,event,landing,exchange,time',
      topN: 5
    };

    if (projectId) {
      options.projectId = projectId;
    }

    // 날짜 범위 추가
    if (startDate.value) {
      options.startDate = startDate.value;
    }

    if (endDate.value) {
      options.endDate = endDate.value;
    }

    const response = await getUserStatistics(options);

    if (response.success && response.data && response.data.basic) {
      const usersByRoleFromServer = response.data.basic.usersByRole || {};
      const counts = {};
      let totalAdmins = 0;

      for (const roleNameInKorean in usersByRoleFromServer) {
        const roleKey = roleNameToKeyMap[roleNameInKorean];
        if (roleKey) {
          const count = usersByRoleFromServer[roleNameInKorean];
          counts[roleKey] = count;
          totalAdmins += count;
        }
      }

      const percentage = {};
      if (totalAdmins > 0) {
        for (const roleKey in counts) {
          percentage[roleKey] = ((counts[roleKey] / totalAdmins) * 100).toFixed(1);
        }
      }

      adminRoleDistribution.value = {
        counts: counts,
        percentage: percentage
      };

      adminActivityStats.value = response.data.adminStat || null;

      // 데이터 로드 완료 표시
      dataLoaded.value = true;
      
      // DOM 업데이트를 기다린 후 직접 차트 렌더링 호출
      nextTick(() => {
        renderCharts();
      });
    } else {
      console.error('API 응답 오류:', response);
      throw new Error('관리자 통계 데이터가 올바른 형식이 아닙니다.');
    }
  } catch (err) {
    console.error('관리자 통계 데이터 로드 중 오류 발생:', err);
    error.value = '관리자 통계 데이터를 불러오는 중 오류가 발생했습니다: ' + err.message;
  } finally {
    isLoading.value = false;
    isChartLoading.value = false;
  }
};

// 차트 렌더링 함수
const renderCharts = () => {
  if (adminRoleDistribution.value && adminRoleDistribution.value.counts) {
    const keysLength = Object.keys(adminRoleDistribution.value.counts).length;
  } else {
    console.warn('[renderCharts] adminRoleDistribution.value 또는 adminRoleDistribution.value.counts가 정의되지 않았습니다.');
  }

  isChartLoading.value = true;

  // 기존 차트 제거
  Object.values(charts.value).forEach(chart => {
    if (chart) {
      try {
        chart.destroy();
      } catch (e) {
        console.error('[renderCharts] 차트 제거 중 오류 발생:', e);
      }
    }
  });

  charts.value = {}; // 새 차트 객체 초기화

  // 관리자 역할별 분포 차트
  // adminRoleDistribution.value.counts가 존재하고, 그 안에 키가 하나 이상 있을 때만 차트 생성
  if (adminRoleDistribution.value && 
      adminRoleDistribution.value.counts && 
      Object.keys(adminRoleDistribution.value.counts).length > 0 && 
      adminRoleChart.value) {
    const canvas = adminRoleChart.value;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      try {
        const roles = Object.keys(adminRoleDistribution.value.counts);
        const countsData = Object.values(adminRoleDistribution.value.counts);
        const roleNames = roles.map(getRoleName);

        charts.value.adminRole = new Chart(ctx, {
          type: 'pie',
          data: {
            labels: roleNames,
            datasets: [{
              data: countsData, // counts 대신 countsData 사용
              backgroundColor: [
                '#4BC0C0', '#FF6384', '#FFCE56', '#36A2EB'
              ]
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'right',
                display: true
              },
              tooltip: {
                callbacks: {
                  label: (context) => {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    // percentage 객체와 roles 배열의 인덱스를 안전하게 접근
                    const roleKey = roles[context.dataIndex];
                    const percentageValue = (adminRoleDistribution.value.percentage && adminRoleDistribution.value.percentage[roleKey]) || 0;
                    return `${label}: ${value}명 (${percentageValue}%)`;
                  }
                }
              }
            }
          }
        });
      } catch (e) {
        console.error('[renderCharts] 차트 생성 중 오류 발생:', e);
      }
    } else {
      console.error('[renderCharts] Canvas context를 가져올 수 없습니다.');
    }
  } else {
    console.warn('[renderCharts] 관리자 역할분포 차트 생성 조건 미충족:');
    if (!adminRoleDistribution.value) {
        console.warn('- adminRoleDistribution 데이터가 없습니다.');
    } else if (!adminRoleDistribution.value.counts) {
        console.warn('- adminRoleDistribution.value.counts가 정의되지 않았습니다.');
    } else if (Object.keys(adminRoleDistribution.value.counts).length === 0) {
        console.warn('- adminRoleDistribution.value.counts가 비어있습니다. counts:', JSON.parse(JSON.stringify(adminRoleDistribution.value.counts)));
    }
    if (!adminRoleChart.value) {
        console.warn('- adminRoleChart ref가 유효하지 않습니다.');
    }
  }

  isChartLoading.value = false;
};

// 컴포넌트 마운트 시 통계 데이터 로드 및 날짜 초기화
onMounted(() => {
  
  // 날짜 초기화 (오늘부터 6일 전까지)
  const endDateValue = new Date();
  const startDateValue = new Date();
  startDateValue.setDate(endDateValue.getDate() - 6);
  
  startDate.value = formatDate(startDateValue);
  endDate.value = formatDate(endDateValue);
  
  // 유효성 검사 초기화
  dateError.value = '';
  isValidDateRange.value = true;
  
  loadAdminStatistics();
});

// 데이터 로드 완료 시 차트 렌더링 (DOM 업데이트 후 실행되도록 nextTick 사용)
watch(dataLoaded, async (isDataLoaded) => {
  if (isDataLoaded) {
    await nextTick(); // DOM 업데이트 기다림
    renderCharts();
  }
});

// 프로젝트 변경 시 통계 데이터 다시 로드
watch(currentProjectId, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    dataLoaded.value = false; // 데이터 로드 상태 초기화
    loadAdminStatistics();
  }
});
</script>

<style scoped>
.statistics-view {
  padding: 0;
}

/* 필터 스타일 추가 */
.filters {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  gap: 10px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.date-input:invalid {
  border-color: #ff4d4f;
  background-color: #fff1f0;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover:not(:disabled) {
  background-color: #0b7dda;
}

.search-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.date-error-message {
  color: #ff4d4f;
  font-size: 14px;
  margin-top: 5px;
  padding: 5px 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
}

.loading-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  padding: 20px;
  background-color: #ffebee;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.error-message {
  color: #d32f2f;
  margin-bottom: 15px;
}

.retry-button {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.statistics-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.stat-card h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-wrapper {
  height: 300px;
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.data-table {
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.no-data {
  padding: 40px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 150px; /* 테이블과 유사한 높이를 갖도록 최소 높이 설정 */
  border: 1px dashed #ddd;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #ccc;
}

.no-data-text {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.no-data-subtext {
  font-size: 0.9rem;
  color: #999;
  max-width: 80%;
  line-height: 1.4;
}

.chart-placeholder {
  height: 300px; /* 기존 차트 플레이스홀더와 동일한 높이 */
  display: flex;
  flex-direction: column; /* 텍스트를 스피너 아래로 옮기기 위해 */
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 20px;
}

.chart-placeholder .mini-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px; /* 스피너와 텍스트 사이 간격 */
}
</style>
