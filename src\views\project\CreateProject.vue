<template>
  <div class="create-project-container">
    <!-- 제목을 동적으로 변경 -->
    <h1>{{ pageTitle }}</h1>
    <form @submit.prevent="submitForm" class="project-form">
      <div class="form-group">
        <label for="projectName">프로젝트명:</label>
        <input type="text" id="projectName" v-model="formData.projectName" required>
      </div>
      <div class="form-group">
        <label for="description">설명:</label>
        <textarea id="description" v-model="formData.description" rows="4"></textarea>
      </div>
      <div class="form-group">
        <label for="projectAdminUserEmail">프로젝트 관리자:</label>
        <select id="projectAdminUserEmail" v-model="formData.projectAdminUserEmail">
          <option value="">프로젝트 관리자 미선택</option>
          <option v-for="admin in projectAdmins" :key="admin.userEmail" :value="admin.userEmail">
            {{ admin.name }} ({{ admin.userEmail }})
          </option>
        </select>
        <div v-if="isLoadingAdmins" class="loading-message">
          관리자 목록을 불러오는 중...
        </div>
        <div v-if="projectAdmins.length === 0 && !isLoadingAdmins" class="warning-message">
          할당 가능한 프로젝트 관리자가 없습니다.
        </div>
        <div v-if="projectAdmins.length > 0 && !isLoadingAdmins" class="info-message">
          프로젝트 관리자를 선택하지 않으려면 '프로젝트 관리자 미선택'을 선택하세요.
        </div>
        <div v-if="isEditMode && projectAdmins.length > 0 && !isLoadingAdmins" class="info-message">
          현재 프로젝트의 관리자와 할당 가능한 관리자만 목록에 표시됩니다.
        </div>
      </div>

      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div class="form-actions">
        <!-- 버튼 텍스트를 동적으로 변경 -->
        <button type="submit" :disabled="isLoading">{{ isLoading ? (isEditMode ? '수정 중...' : '생성 중...') : submitButtonText }}</button>
        <button type="button" @click="goToList" :disabled="isLoading">취소</button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { createProject, getProjectById, updateProject } from '@/api/project';
import { getProjectAdmins, getAvailableProjectAdmins, getAvailableProjectAdminsForEdit } from '@/api/user';

const router = useRouter();
const route = useRoute();

// 프로젝트 정보 폼 데이터
const formData = ref({
  projectName: '',
  description: '',
  projectAdminUserEmail: ''
});

const isLoading = ref(false);
const errorMessage = ref('');
const projectAdmins = ref([]);
const isLoadingAdmins = ref(false);

// 프로젝트 관리자 목록 불러오기
const fetchProjectAdmins = async () => {
  isLoadingAdmins.value = true;
  try {

    // 백엔드 API 사용
    if (isEditMode.value) {
      projectAdmins.value = await getProjectAdmins();
      // 수정 모드일 경우: 현재 프로젝트에 할당된 관리자와 할당 가능한 관리자 목록 가져오기
      const projectId = route.params.projectId;
    } else {
      projectAdmins.value = await getProjectAdmins();
    }

  } catch (error) {
    console.error('프로젝트 관리자 목록 불러오기 실패:', error);
    errorMessage.value = `프로젝트 관리자 목록을 불러오는 중 오류가 발생했습니다: ${error.message}`;
  } finally {
    isLoadingAdmins.value = false;
  }
};

// 수정 모드 여부 확인 (라우트 파라미터 projectId 유무)
const isEditMode = computed(() => !!route.params.projectId);

// 페이지 제목 계산
const pageTitle = computed(() => isEditMode.value ? '프로젝트 수정' : '새 프로젝트 생성');

// 제출 버튼 텍스트 계산
const submitButtonText = computed(() => isEditMode.value ? '수정하기' : '프로젝트 생성');

const submitForm = async () => {
  isLoading.value = true;
  errorMessage.value = '';

  try {
    // 프로젝트 데이터 객체 생성 (생성/수정 모드 공통)
    const projectPayload = {
      projectName: formData.value.projectName,
      description: formData.value.description || '',
      projectAdminUserEmail: formData.value.projectAdminUserEmail || '' // 빈 값도 서버에 전송
    };

    if (isEditMode.value) {
      // 수정 모드: updateProject 호출
      const projectId = route.params.projectId;
      await updateProject(projectId, projectPayload);
      alert('프로젝트가 성공적으로 수정되었습니다.');
    } else {
      // 생성 모드: createProject 호출
      await createProject(projectPayload);
      alert('새 프로젝트가 성공적으로 생성되었습니다.');
    }

    // 성공 시 목록 페이지로 이동
    router.push({ name: 'project-management' });
  } catch (error) {
    // 에러 메시지를 좀 더 구체적으로 표시
    errorMessage.value = `${isEditMode.value ? '수정' : '생성'} 실패: ${error.message}`;
    console.error(`Project ${isEditMode.value ? 'update' : 'create'} error:`, error);
  } finally {
    isLoading.value = false;
  }
};

const goToList = () => {
  router.push({ name: 'project-management' });
};

// 프로젝트 상세 정보 불러오기 (수정 모드일 경우)
const fetchProjectDetails = async () => {
  if (!isEditMode.value) return;

  const projectId = route.params.projectId;
  isLoading.value = true;
  errorMessage.value = '';

  try {
    const response = await getProjectById(projectId);
    
    // API 응답 구조 처리: { success: true, data: {...} }
    let projectData;
    if (response && response.success && response.data) {
      projectData = response.data;
    } else if (response && response.data) {
      // 과거 호환성을 위한 처리
      projectData = response.data;
    } else {
      // 응답이 직접 프로젝트 데이터인 경우
      projectData = response;
    }
    
    formData.value = {
      projectName: projectData.projectName,
      description: projectData.description || '',
      projectAdminUserEmail: projectData.projectAdminUserEmail || '' // null이나 undefined인 경우 미선택(빈 문자열)로 처리
    };

  } catch (error) {
    console.error('프로젝트 상세 정보 불러오기 실패:', error);
    errorMessage.value = `프로젝트 상세 정보를 불러오는 중 오류가 발생했습니다: ${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 컴포넌트 마운트 시 프로젝트 관리자 목록 및 프로젝트 상세 정보 로드
onMounted(async () => {
  await fetchProjectAdmins();
  await fetchProjectDetails();
});
</script>

<style scoped>
.create-project-container {
  max-width: 600px;
  margin: 30px auto;
  padding: 25px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.project-form .form-group {
  margin-bottom: 20px;
}

.project-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

.project-form input[type="text"],
.project-form input[type="email"],
.project-form textarea,
.project-form select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1em;
}

.project-form textarea {
  min-height: 80px;
  resize: vertical;
}

.error-message {
  color: #e74c3c;
  background-color: #fdd;
  border: 1px solid #e74c3c;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  text-align: center;
}

.loading-message {
  color: #3498db;
  font-size: 0.9em;
  margin-top: 5px;
}

.warning-message {
  color: #e67e22;
  font-size: 0.9em;
  margin-top: 5px;
}

.info-message {
  color: #2980b9;
  font-size: 0.9em;
  margin-top: 5px;
  font-style: italic;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.form-actions button {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 1em;
  cursor: pointer;
  transition: background-color 0.3s;
}

.form-actions button[type="submit"] {
  background-color: #2196F3;
  color: white;
}

.form-actions button[type="submit"]:hover {
  background-color: #0b7dda;
}

.form-actions button[type="button"] {
  background-color: #f5f5f5;
  color: #333;
}

.form-actions button[type="button"]:hover {
  background-color: #e0e0e0;
}

.form-actions button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
</style>
