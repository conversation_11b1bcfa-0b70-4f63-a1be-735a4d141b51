import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 이미지 파일을 업로드합니다.
 * @param {File} file - 업로드할 이미지 파일
 * @param {string} type - 이미지 타입 (landing, qr, logo 등)
 * @param {number} projectId - 프로젝트 ID
 * @returns {Promise<object>} 업로드된 이미지 정보 (URL 등)
 */
export const uploadImage = async (file, type = 'landing', projectId) => {
  try {
    if (!file) {
      throw new Error('업로드할 파일이 필요합니다.');
    }

    // FormData 생성
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    if (projectId) {
      formData.append('projectId', projectId);
    }

    // 서버에서 요구하는 정확한 Content-Type 헤더 설정
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      transformRequest: [(data, headers) => {
        // 추가 파라미터(boundary, charset 등)가 없는 정확한 Content-Type 헤더 설정
        headers['Content-Type'] = 'multipart/form-data';
        return data;
      }]
    };

    // 백엔드 API 엔드포인트 사용
    const response = await apiClient.post('/upload/images', formData, config);

    // API 응답 구조 확인 및 반환
    if (response.data && response.data.success) {
      // 백엔드 API 응답 구조에 맞게 처리
      return {
        imageUrl: response.data.data.imageUrl, // 서버에서 반환한 URI를 이미지 URL로 사용
        imageName: response.data.data.filename // 원본 파일명
      };
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.error?.message || '이미지 업로드에 실패했습니다.');
    }
  } catch (error) {
    console.error('이미지 업로드 오류:', error);
    const errorMessage = handleApiError(error, '이미지 업로드 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 이미지 목록을 가져옵니다.
 * 참고: 현재 백엔드에서 이미지 목록 조회 API가 명확하게 제공되지 않을 수 있습니다.
 * 추후 백엔드 API 업데이트에 따라 수정이 필요할 수 있습니다.
 * @param {string} type - 이미지 타입 (landing, qr, logo 등)
 * @param {number} projectId - 프로젝트 ID
 * @returns {Promise<Array>} 이미지 목록
 */
export const getImages = async (type = 'landing', projectId) => {
  try {
    // 쿼리 파라미터 설정
    const params = { type };
    if (projectId) {
      params.projectId = projectId;
    }

    return [];
  } catch (error) {
    console.error('이미지 목록 조회 오류:', error);
    const errorMessage = handleApiError(error, '이미지 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 이미지를 삭제합니다.
 * 참고: 현재 백엔드에서 이미지 삭제 API가 명확하게 제공되지 않을 수 있습니다.
 * 추후 백엔드 API 업데이트에 따라 수정이 필요할 수 있습니다.
 * @param {string} imageId - 삭제할 이미지 ID
 * @returns {Promise<object>} 성공 시 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const deleteImage = async (imageId) => {
  try {
    if (!imageId) {
      throw new Error('삭제할 이미지 ID가 필요합니다.');
    }

    return { success: true, message: '이미지가 삭제되었습니다.' };

  } catch (error) {
    console.error('이미지 삭제 오류:', error);
    const errorMessage = handleApiError(error, '이미지 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};
