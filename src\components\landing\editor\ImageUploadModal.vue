<template>
  <div v-if="show" class="image-upload-modal-overlay" @click="closeModal">
    <div class="image-upload-modal-content" @click.stop>
      <div class="modal-header">
        <h3>배경 이미지 업로드</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <ImageUploader 
          type="background" 
          @upload-success="handleUploadSuccess" 
          @change="handleImageChange"
        />
      </div>
      
      <div class="modal-footer">
        <button class="cancel-btn" @click="closeModal">취소</button>
        <button class="apply-btn" :disabled="!selectedImage" @click="applyImage">적용</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ImageUploader from '@/components/common/ImageUploader.vue';

// 프롭스
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// 이벤트
const emit = defineEmits(['close', 'select-image']);

// 상태
const selectedImage = ref(null);

// 이미지 업로드 성공 핸들러
const handleUploadSuccess = (result) => {
  selectedImage.value = result.imageUrl;
};

// 이미지 변경 핸들러
const handleImageChange = (imageUrl) => {
  selectedImage.value = imageUrl;
};

// 이미지 적용 핸들러
const applyImage = () => {
  if (selectedImage.value) {
    emit('select-image', selectedImage.value);
    closeModal();
  }
};

// 모달 닫기
const closeModal = () => {
  emit('close');
};
</script>

<style scoped>
.image-upload-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.image-upload-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #eee;
  gap: 10px;
}

.cancel-btn, .apply-btn {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-btn {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
}

.apply-btn {
  background-color: #4CAF50;
  border: none;
  color: white;
}

.apply-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
</style>
