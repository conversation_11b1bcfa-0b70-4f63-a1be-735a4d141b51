<template>
  <div class="button-properties">
    <h4>버튼 속성</h4>

    <div class="property-row">
      <label>버튼 텍스트:</label>
      <input
        type="text"
        :value="element.content.text"
        @input="updateProperty('content.text', $event.target.value)"
        placeholder="버튼 텍스트"
      />
    </div>

    <div class="property-section">
      <h5>텍스트 스타일</h5>

      <div class="property-row">
        <label>폰트 크기:</label>
        <div class="input-with-unit">
          <input
            type="number"
            :value="element.content.textStyle.fontSize"
            @input="updateProperty('content.textStyle.fontSize', parseInt($event.target.value))"
            min="8"
            max="72"
          />
          <span class="unit">px</span>
        </div>
      </div>

      <div class="property-row">
        <label>폰트:</label>
        <select
          :value="element.content.textStyle.fontFamily"
          @change="updateProperty('content.textStyle.fontFamily', $event.target.value)"
        >
          <option value="Arial, sans-serif">Arial</option>
          <option value="'Noto Sans KR', sans-serif">Noto Sans KR</option>
          <option value="'Malgun Gothic', sans-serif">맑은 고딕</option>
          <option value="'Courier New', monospace">Courier New</option>
          <option value="Georgia, serif">Georgia</option>
          <option value="'Times New Roman', serif">Times New Roman</option>
        </select>
      </div>

      <div class="property-row">
        <label>글자 색상:</label>
        <input
          type="color"
          :value="element.content.textStyle.color"
          @input="updateProperty('content.textStyle.color', $event.target.value)"
        />
      </div>

      <div class="property-row">
        <label>글자 두께:</label>
        <select
          :value="element.content.textStyle.fontWeight"
          @change="updateProperty('content.textStyle.fontWeight', $event.target.value)"
        >
          <option value="normal">보통</option>
          <option value="bold">굵게</option>
          <option value="lighter">얇게</option>
          <option value="100">100</option>
          <option value="200">200</option>
          <option value="300">300</option>
          <option value="400">400</option>
          <option value="500">500</option>
          <option value="600">600</option>
          <option value="700">700</option>
          <option value="800">800</option>
          <option value="900">900</option>
        </select>
      </div>
    </div>

    <div class="property-section">
      <h5>버튼 동작</h5>

      <div class="property-row">
        <label>동작 유형:</label>
        <select
          :value="element.content.action.type"
          @change="updateProperty('content.action.type', $event.target.value)"
        >
          <option value="link">링크</option>
          <option value="scroll">스크롤</option>
          <option value="popup">팝업</option>
        </select>
      </div>

      <div class="property-row">
        <label>{{ actionTargetLabel }}:</label>
        <!-- 스크롤 동작일 때 요소 선택 드롭다운 표시 -->
        <select
          v-if="element.content.action.type === 'scroll' && allElements.length > 0"
          :value="element.content.action.target"
          @change="updateProperty('content.action.target', $event.target.value)"
        >
          <option value="">요소 선택...</option>
          <option v-for="el in allElements" :key="el.id" :value="el.id">
            {{ el.name }} ({{ getElementTypeLabel(el.type) }})
          </option>
        </select>
        <!-- 다른 동작일 때 텍스트 입력 필드 표시 -->
        <div v-else class="input-container">
          <input
            type="text"
            :value="element.content.action.target"
            @input="updateProperty('content.action.target', $event.target.value)"
            :placeholder="actionTargetPlaceholder"
            :class="{ 'invalid-url': isLinkWithInvalidUrl }"
          />
          <div v-if="isLinkWithInvalidUrl" class="url-validation-error">
            URL은 http:// 또는 https://로 시작해야 합니다.
          </div>
        </div>
      </div>

      <div class="property-row" v-if="element.content.action.type === 'link'">
        <label>새 탭에서 열기:</label>
        <div class="checkbox-container">
          <input
            type="checkbox"
            :checked="element.content.action.newTab"
            @change="updateProperty('content.action.newTab', $event.target.checked)"
            id="newTabCheckbox"
          />
          <label for="newTabCheckbox" class="checkbox-label"></label>
        </div>
      </div>
    </div>

    <div class="property-section">
      <h5>호버 효과</h5>

      <div class="property-row">
        <label>배경 색상:</label>
        <input
          type="color"
          :value="element.content.hoverStyle.backgroundColor"
          @input="updateProperty('content.hoverStyle.backgroundColor', $event.target.value)"
        />
      </div>

      <div class="property-row">
        <label>텍스트 색상:</label>
        <input
          type="color"
          :value="element.content.hoverStyle.color"
          @input="updateProperty('content.hoverStyle.color', $event.target.value)"
        />
      </div>
    </div>

    <div class="property-section">
      <h5>아이콘 (선택사항)</h5>

      <div class="property-row upload-row">
        <label>아이콘 이미지:</label>
        <div class="upload-container">
          <ImageUploader
            :value="element.content.icon.src"
            type="icon"
            @change="handleIconChange"
          />
        </div>
      </div>

      <div class="property-row">
        <label>아이콘 위치:</label>
        <select
          :value="element.content.icon.position"
          @change="updateProperty('content.icon.position', $event.target.value)"
        >
          <option value="left">왼쪽</option>
          <option value="right">오른쪽</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useLandingEditorStore } from '@/stores/landingEditor';
import ImageUploader from '@/components/common/ImageUploader.vue';

// 프롭스
const props = defineProps({
  element: {
    type: Object,
    required: true
  }
});

// 이벤트
const emit = defineEmits(['update']);

// 스토어
const authStore = useAuthStore();
const landingEditorStore = useLandingEditorStore();

// 현재 프로젝트 ID
const currentProjectId = computed(() => {
  return authStore.currentProject?.projectId;
});

// 현재 랜딩 페이지의 모든 요소 목록 (현재 요소 제외)
const allElements = computed(() => {
  if (!landingEditorStore.currentLandingPage) return [];

  // 현재 요소를 제외한 모든 요소 반환
  return landingEditorStore.currentLandingPage.elements.filter(el => el.id !== props.element.id);
});

// 요소 타입에 따른 레이블 반환
const getElementTypeLabel = (type) => {
  const typeMap = {
    'text': '텍스트',
    'image': '이미지',
    'button': '버튼',
    'link': '링크',
    'div': '블록'
  };
  return typeMap[type] || type;
};

// URL 유효성 검사 함수
const isValidUrl = (url) => {
  return url && (url.startsWith('http://') || url.startsWith('https://'));
};

// 동작 유형에 따른 레이블 및 플레이스홀더
const actionTargetLabel = computed(() => {
  const typeMap = {
    'link': 'URL',
    'scroll': '스크롤 대상',
    'popup': '팝업 내용'
  };
  return typeMap[props.element.content.action.type] || '대상';
});

const actionTargetPlaceholder = computed(() => {
  const typeMap = {
    'link': 'https://example.com',
    'scroll': '요소 ID 또는 픽셀 값 (예: 200)',
    'popup': '팝업에 표시할 내용'
  };
  return typeMap[props.element.content.action.type] || '';
});

// 링크 타입이고 URL이 유효하지 않은 경우 확인
const isLinkWithInvalidUrl = computed(() => {
  return props.element.content.action.type === 'link' &&
         props.element.content.action.target &&
         !isValidUrl(props.element.content.action.target);
});

// 속성 업데이트 함수
const updateProperty = (propertyPath, value) => {
  emit('update', propertyPath, value);
};

// 아이콘 이미지 변경 핸들러
const handleIconChange = (imageUrl) => {
  updateProperty('content.icon.src', imageUrl);
};
</script>

<style scoped>
.button-properties {
  margin-bottom: 16px;
}

h4, h5 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  color: #555;
}

h5 {
  font-size: 13px;
  margin-top: 16px;
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.property-section {
  margin-bottom: 16px;
}

.property-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.property-row label {
  flex: 0 0 100px;
  font-size: 13px;
  color: #666;
}

.property-row input,
.property-row select {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.property-row input[type="color"] {
  width: 40px;
  height: 24px;
  padding: 0;
  border: 1px solid #ddd;
}

.input-with-unit {
  display: flex;
  align-items: center;
  flex: 1;
}

.input-with-unit input {
  flex: 1;
  margin-right: 4px;
}

.unit {
  font-size: 12px;
  color: #666;
  width: 20px;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-container input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.checkbox-label {
  font-size: 13px;
  color: #666;
  cursor: pointer;
}

.upload-row {
  align-items: flex-start;
}

.upload-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.input-container input.invalid-url {
  border-color: #f44336;
  background-color: #ffebee;
}

.url-validation-error {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
}
</style>
