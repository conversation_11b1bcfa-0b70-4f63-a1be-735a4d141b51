<template>
  <div class="landing-view">
    <div class="view-header">
      <h1>랜딩 페이지 상세 정보</h1>

      <div class="header-actions">
        <button @click="editLandingPage" class="edit-btn">수정</button>
        <button @click="goBack" class="back-btn">목록으로</button>
      </div>
    </div>

    <div v-if="isLoading" class="loading">
      데이터를 불러오는 중...
    </div>

    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-else-if="landingPage" class="landing-details">
      <div class="info-section">
        <h2>기본 정보</h2>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">랜딩 페이지 ID:</span>
            <span class="value">{{ landingPage.landingPageId }}</span>
          </div>

          <div class="info-item">
            <span class="label">랜딩 페이지 이름:</span>
            <span class="value">{{ landingPage.pageTitle }}</span>
          </div>

          <div class="info-item">
            <span class="label">상태:</span>
            <span class="value status-badge" :class="statusClass">
              {{ formatStatus(landingPage.status) }}
            </span>
          </div>

          <div class="info-item">
            <span class="label">접속 URL:</span>
            <a v-if="landingPage.url" :href="landingPage.url" target="_blank" class="value url">
              {{ landingPage.url }}
            </a>
            <span v-else class="value">-</span>
          </div>

          <div class="info-item">
            <span class="label">생성일:</span>
            <span class="value">{{ formatDate(landingPage.createDate) }}</span>
          </div>

          <div class="info-item">
            <span class="label">유효 시작일:</span>
            <span class="value">{{ formatDate(landingPage.validFromDate) || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">유효 종료일:</span>
            <span class="value">{{ formatDate(landingPage.validToDate) || '-' }}</span>
          </div>
        </div>
      </div>

      <div class="description-section" v-if="landingPage.description">
        <h2>설명</h2>
        <p class="description">{{ landingPage.description }}</p>
      </div>

      <div class="preview-section">
        <h2>랜딩 페이지 미리보기</h2>
        <div class="preview-container">
          <div class="preview-device-frame">
            <div class="preview-scroll-container">
              <LandingPreview
                v-if="parsedLandingPage"
                :landing-page="parsedLandingPage"
                :scale="previewScale"
              />
              <div v-else class="preview-placeholder">
                <p>미리보기 데이터를 불러올 수 없습니다.</p>
              </div>
            </div>
          </div>
          <div class="preview-controls">
            <div class="preview-info">
              <span>모바일 ({{ canvasWidth }}px x {{ canvasHeight }}px)</span>
              <span>스케일: {{ Math.round(previewScale * 100) }}%</span>
            </div>
            <div class="preview-zoom-controls">
              <button @click="decreaseZoom" class="zoom-btn" :disabled="previewScale <= 0.5">-</button>
              <span class="zoom-level">{{ Math.round(previewScale * 100) }}%</span>
              <button @click="increaseZoom" class="zoom-btn" :disabled="previewScale >= 1.5">+</button>
              <button @click="resetZoom" class="zoom-reset-btn">리셋</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-data">
      <p>랜딩 페이지 데이터를 찾을 수 없습니다.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { getLandingPageById } from '@/api/landing';
import LandingPreview from '@/components/landing/preview/LandingPreview.vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const landingPage = ref(null);
const isLoading = ref(false);
const error = ref(null);
const previewScale = ref(1); // 미리보기 스케일

// 미리보기용 랜딩 페이지 데이터 파싱
const parsedLandingPage = computed(() => {
  if (!landingPage.value) return null;

  try {
    // contentJson이 문자열인 경우 파싱
    if (landingPage.value.contentJson && typeof landingPage.value.contentJson === 'string') {
      const contentData = JSON.parse(landingPage.value.contentJson);
      return {
        ...landingPage.value,
        canvas: contentData.canvas,
        elements: contentData.elements
      };
    }
    // contentJson이 이미 객체인 경우
    else if (landingPage.value.contentJson && typeof landingPage.value.contentJson === 'object') {
      return {
        ...landingPage.value,
        canvas: landingPage.value.contentJson.canvas,
        elements: landingPage.value.contentJson.elements
      };
    }
    // 이전 버전 호환성 (content_json 필드)
    else if (landingPage.value.content_json) {
      const contentData = typeof landingPage.value.content_json === 'string'
        ? JSON.parse(landingPage.value.content_json)
        : landingPage.value.content_json;

      return {
        ...landingPage.value,
        canvas: contentData.canvas,
        elements: contentData.elements
      };
    }

    return null;
  } catch (e) {
    console.error('랜딩 페이지 데이터 파싱 오류:', e);
    return null;
  }
});

// 캔버스 크기 계산
const canvasWidth = computed(() => {
  if (!parsedLandingPage.value || !parsedLandingPage.value.canvas) return 375;
  return parsedLandingPage.value.canvas.width;
});

const canvasHeight = computed(() => {
  if (!parsedLandingPage.value || !parsedLandingPage.value.canvas) return 667;
  return parsedLandingPage.value.canvas.height;
});

// 줌 컨트롤 핸들러
const increaseZoom = () => {
  if (previewScale.value < 1.5) {
    previewScale.value = Math.min(1.5, previewScale.value + 0.1);
  }
};

const decreaseZoom = () => {
  if (previewScale.value > 0.5) {
    previewScale.value = Math.max(0.5, previewScale.value - 0.1);
  }
};

const resetZoom = () => {
  previewScale.value = 1;
};

// 상태 클래스 계산
const statusClass = computed(() => {
  if (!landingPage.value) return '';

  const statusMap = {
    'ACTIVE': 'status-active',
    'INACTIVE': 'status-inactive',
    'EXPIRED': 'status-expired',
    'DELETED': 'status-deleted'
  };

  return statusMap[landingPage.value.status] || '';
});

// 상태 포맷팅
const formatStatus = (status) => {
  const statusMap = {
    'PUBLISHED': '활성',
    'DRAFT': '비활성',
    'EXPIRED': '만료됨',
    'DELETED': '삭제됨'
  };
  return statusMap[status] || status;
};

// 날짜 포맷팅 - 초 단위까지만 표시
const formatDate = (dateString) => {
  if (!dateString) return null;

  try {
    // 날짜 문자열이 'T'를 포함하는 ISO 형식인 경우 (2025-04-26T14:27:00)
    if (dateString.includes('T')) {
      const [datePart, timePart] = dateString.split('T');
      // 밀리초 제거하고 초까지만 표시
      const timeWithSeconds = timePart.split('.')[0];
      return `${datePart} ${timeWithSeconds}`;
    }

    // 이미 공백으로 구분된 형식인 경우 (2025-04-16 14:13:15)
    if (dateString.includes(' ')) {
      const [datePart, timePart] = dateString.split(' ');

      // 밀리초 부분이 있는 경우 제거
      if (timePart.includes('.')) {
        const timeWithoutMillis = timePart.split('.')[0];
        return `${datePart} ${timeWithoutMillis}`;
      }

      // 이미 초 단위까지만 있는 경우
      return dateString;
    }

    // 날짜만 있는 경우 (2025-04-16)
    if (dateString.includes('-') && !dateString.includes(' ') && !dateString.includes('T')) {
      return `${dateString} 00:00:00`;
    }

    // 그 외의 경우 그대로 반환
    return dateString;
  } catch (e) {
    console.warn('날짜 형식 변환 오류:', e);
    return dateString; // 오류 발생 시 원본 문자열 반환
  }
};

// 랜딩 페이지 수정 페이지로 이동
const editLandingPage = () => {
  router.push({
    name: 'landing-form',
    params: { landingPageId: route.params.landingPageId },
    query: { projectId: route.query.projectId }
  });
};

// 목록으로 돌아가기
const goBack = () => {
  router.push({ name: 'landing-management' });
};

// 랜딩 페이지 데이터 로드
const loadLandingPage = async (landingPageId) => {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await getLandingPageById(landingPageId);

    // 서버 응답 구조 확인 (success, data 필드)
    if (response && response.success === true && response.data) {
      landingPage.value = response.data;
    } else {
      throw new Error('서버 응답 구조가 올바르지 않습니다.');
    }
  } catch (err) {
    console.error('랜딩 페이지 로드 오류:', err);
    error.value = err.message || '랜딩 페이지 데이터를 불러오는 중 오류가 발생했습니다.';
    landingPage.value = null;
  } finally {
    isLoading.value = false;
  }
};

// 컴포넌트 마운트 시 필요한 데이터 로드
onMounted(() => {
  if (route.params.landingPageId) {
    loadLandingPage(route.params.landingPageId);
  } else {
    error.value = '랜딩 페이지 ID가 유효하지 않습니다.';
  }
});
</script>

<style scoped>
.landing-view {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.edit-btn, .back-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.edit-btn {
  background-color: #FFC107;
  color: black;
}

.edit-btn:hover {
  background-color: #e6af07;
}

.back-btn {
  background-color: #2196F3;
  color: white;
}

.back-btn:hover {
  background-color: #0b7dda;
}

.loading, .error-message, .no-data {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
}

.error-message {
  color: #f44336;
  background-color: #ffebee;
}

.no-data {
  color: #757575;
}

.landing-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-section, .description-section, .preview-section {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

.info-section h2, .description-section h2, .preview-section h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

.value {
  font-size: 16px;
  color: #333;
}

.url {
  color: #2196F3;
  text-decoration: none;
  word-break: break-all;
}

.url:hover {
  text-decoration: underline;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
}

.status-active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #757575;
}

.status-expired {
  background-color: #fff8e1;
  color: #ff8f00;
}

.status-deleted {
  background-color: #ffebee;
  color: #c62828;
}

.description {
  white-space: pre-line;
  line-height: 1.5;
}

.preview-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-device-frame {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 500px;
  width: 392px; /* 375px + 스크롤바 너비(17px) */
  margin: 0 auto;
  border-radius: 16px;
  border: 12px solid #333;
  border-top-width: 40px;
  border-bottom-width: 40px;
  position: relative;
  box-sizing: content-box; /* 테두리와 패딩이 너비에 포함되지 않도록 설정 */
}

.preview-device-frame::before {
  content: '';
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 10px;
  background-color: #222;
  border-radius: 5px;
}

.preview-scroll-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: white;
  /* 스크롤바 스타일 수정 - 오버레이 모드로 설정하여 컨텐츠 영역을 침범하지 않도록 함 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Firefox */
}

/* Webkit 브라우저용 스크롤바 스타일 */
.preview-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.preview-scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-scroll-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.preview-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #e0e0e0;
  color: #757575;
  font-style: italic;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.preview-info {
  display: flex;
  gap: 16px;
  color: #666;
  font-size: 14px;
}

.preview-zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-btn {
  width: 28px;
  height: 28px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
}

.zoom-btn:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-level {
  font-size: 14px;
  color: #666;
  min-width: 50px;
  text-align: center;
}

.zoom-reset-btn {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.zoom-reset-btn:hover {
  background-color: #e0e0e0;
}
</style>
