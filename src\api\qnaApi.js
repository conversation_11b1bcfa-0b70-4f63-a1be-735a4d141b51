// QnA API 관련 함수들
import apiClient from './index';

// API 경로 접두사
const API_PREFIX = '/qna';

/**
 * 새 질문 생성
 * @param {Object} questionData - 질문 데이터 (제목, 내용, 작성자 등)
 * @returns {Promise} API 응답
 */
export const createQuestion = async (questionData) => {
  try {
    const response = await apiClient.post(`${API_PREFIX}/questions`, questionData);
    // 응답 구조가 { data: { success: true, data: {...} } } 형태임
    // apiClient는 자동으로 .data를 한 번 추출하므로 그대로 반환
    return { success: true, data: response.data };
  } catch (error) {
    console.error('질문 생성 중 오류 발생:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 질문 목록 조회 (페이징, 검색 조건 포함)
 * @param {Object} params - 검색 조건 (페이지, 크기, 검색어, 필터 등)
 * @returns {Promise} API 응답
 */
export const getQuestions = async (params = {}) => {
  try {
    const response = await apiClient.get(`${API_PREFIX}/questions`, { params });
    return { success: true, data: response.data };
  } catch (error) {
    console.error('질문 목록 조회 중 오류 발생:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 특정 질문 상세 조회 (답변 포함)
 * @param {string|number} questionId - 질문 ID
 * @returns {Promise} API 응답
 */
export const getQuestionById = async (questionId) => {
  try {
    const response = await apiClient.get(`${API_PREFIX}/questions/${questionId}`);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`질문 ID ${questionId} 상세 조회 중 오류 발생:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * 특정 질문 수정
 * @param {string|number} questionId - 질문 ID
 * @param {Object} questionData - 수정할 질문 데이터
 * @returns {Promise} API 응답
 */
export const updateQuestion = async (questionId, questionData) => {
  try {
    const response = await apiClient.put(`${API_PREFIX}/questions/${questionId}`, questionData);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`질문 ID ${questionId} 수정 중 오류 발생:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * 특정 질문 삭제
 * @param {string|number} questionId - 질문 ID
 * @returns {Promise} API 응답
 */
export const deleteQuestion = async (questionId) => {
  try {
    const response = await apiClient.delete(`${API_PREFIX}/questions/${questionId}`);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`질문 ID ${questionId} 삭제 중 오류 발생:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * 질문에 답변 추가
 * @param {string|number} questionId - 질문 ID
 * @param {string} answerContent - 답변 내용
 * @returns {Promise} API 응답
 */
export const createAnswer = async (questionId, answerContent) => {
  try {
    const response = await apiClient.post(`${API_PREFIX}/answers`, {
      questionId: questionId,
      answerContent: answerContent
    });
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`질문 ID ${questionId}에 답변 추가 중 오류 발생:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * 답변 수정
 * @param {string|number} answerId - 답변 ID
 * @param {string} answerContent - 수정할 답변 내용
 * @returns {Promise} API 응답
 */
export const updateAnswer = async (answerId, answerContent) => {
  try {
    const response = await apiClient.put(`${API_PREFIX}/answers/${answerId}`, {
      answerContent: answerContent
    });
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`답변 ID ${answerId} 수정 중 오류 발생:`, error);
    return { success: false, error: error.message };
  }
};
