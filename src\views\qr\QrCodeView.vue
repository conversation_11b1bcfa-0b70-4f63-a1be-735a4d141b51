<template>
  <div class="qr-code-view">
    <h1>QR 코드 상세 정보</h1>

    <div v-if="isLoading" class="loading">
      데이터를 불러오는 중...
    </div>

    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-else-if="qrCode" class="qr-code-details">
      <div class="qr-code-header">
        <h2>{{ qrCode.qrName }}</h2>
        <div class="qr-code-actions">
          <button @click="editQrCode" class="edit-btn">수정</button>
          <button @click="goBack" class="back-btn">목록으로</button>
        </div>
      </div>

      <div class="qr-code-info">
        <div class="info-row">
          <div class="info-label">QR 코드 ID</div>
          <div class="info-value">{{ qrCode.qrCodeId }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">프로젝트</div>
          <div class="info-value">
            {{ qrCode.projectName || qrCode.project?.projectName || '알 수 없음' }}
            <span v-if="qrCode.projectId || qrCode.project?.projectId">
              (ID: {{ qrCode.projectId || qrCode.project?.projectId }})
            </span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-label">QR 타입</div>
          <div class="info-value">{{ formatQrType(qrCode.qrType) }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">상태</div>
          <div class="info-value">{{ formatStatus(qrCode.status) }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">타겟 콘텐츠</div>
          <div class="info-value content-value">{{ qrCode.targetContent }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">스캔 수</div>
          <div class="info-value">{{ qrCode.scanCount || 0 }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">생성일</div>
          <div class="info-value">{{ formatDate(qrCode.createDate) }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">유효 시작일</div>
          <div class="info-value">{{ formatDate(qrCode.validFromDate) }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">유효 종료일</div>
          <div class="info-value">{{ formatDate(qrCode.validToDate) }}</div>
        </div>
        <div class="info-row" v-if="qrCode.qrInstalledImagePath">
          <div class="info-label">QR코드 설치 위치 이미지</div>
          <div class="info-value">
            <img 
              v-if="qrInstalledImageUrl && !installedImageLoadError" 
              :src="qrInstalledImageUrl" 
              alt="QR 설치 위치 이미지" 
              class="installed-image" 
              @error="handleInstalledImageError"
            />
            <div v-else class="image-placeholder">
              {{ installedImageLoadError ? '설치 위치 이미지를 불러올 수 없습니다.' : '설치 위치 이미지를 사용할 수 없습니다.' }}
            </div>
          </div>
        </div>
        
        <!-- QR 코드 통계 섹션 -->
        <div class="qr-code-statistics info-section">
          <h3>QR코드 통계</h3>
          
          <!-- 기간 선택 영역 -->
          <div class="statistics-date-selector">
            <div class="date-picker-container">
              <label for="startDate">시작일:</label>
              <input 
                type="date" 
                id="startDate" 
                v-model="statisticsStartDate" 
                :max="today"
                @change="validateDates"
              />
            </div>
            <div class="date-picker-container">
              <label for="endDate">종료일:</label>
              <input 
                type="date" 
                id="endDate" 
                v-model="statisticsEndDate" 
                :max="today"
                @change="validateDates"
              />
            </div>
            <button 
              @click="fetchQrCodeStatistics" 
              class="statistics-btn"
              :disabled="isStatisticsLoading"
            >
              {{ isStatisticsLoading ? '조회 중...' : '통계 조회' }}
            </button>
          </div>
          
          <!-- 통계 로딩 표시 -->
          <div v-if="isStatisticsLoading" class="loading-statistics">
            통계 데이터를 불러오는 중...
          </div>
          
          <!-- 통계 오류 표시 -->
          <div v-else-if="statisticsError" class="error-statistics">
            {{ statisticsError }}
          </div>
          
          <!-- 통계 데이터 표시 -->
          <div v-else-if="statisticsData" class="statistics-data">
            <div class="statistics-summary">
              <!-- 총 스캔 수 -->
              <div class="statistic-card">
                <div class="statistic-title">총 스캔 수</div>
                <div class="statistic-value">{{ statisticsData.totalScanCount || 0 }}</div>
              </div>
            </div>
            
            <!-- 차트 표시 영역 -->
            <div v-if="statisticsData.dailyScanCounts && statisticsData.dailyScanCounts.length > 0" class="chart-container">
              <h4>일별 스캔 통계</h4>
              <div class="chart-wrapper">
                <Bar
                  :data="chartData"
                  :options="chartOptions"
                />
              </div>
            </div>
            
            <!-- OS 통계 -->
            <div v-if="statisticsData.deviceOsStats && statisticsData.deviceOsStats.length > 0" class="statistics-table-container">
              <h4>기기 OS 통계</h4>
              <table class="statistics-table">
                <thead>
                  <tr>
                    <th>OS</th>
                    <th>스캔 수</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(stat, index) in statisticsData.deviceOsStats" :key="index">
                    <td>{{ stat.os }}</td>
                    <td>{{ stat.count }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- 최다 스캔 시간대 -->
            <div v-if="statisticsData.topScanTimes && statisticsData.topScanTimes.length > 0" class="statistics-table-container">
              <h4>스캔 시간대 통계</h4>
              <table class="statistics-table">
                <thead>
                  <tr>
                    <th>시간대</th>
                    <th>스캔 수</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(stat, index) in statisticsData.topScanTimes" :key="index">
                    <td>{{ stat.hour }}:00 ~ {{ stat.hour }}:59</td>
                    <td>{{ stat.count }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- 통계 데이터가 없는 경우 -->
            <div v-if="!statisticsData.dailyScanCounts || statisticsData.dailyScanCounts.length === 0" class="no-statistics-data">
              선택한 기간에 스캔 데이터가 없습니다.
            </div>
          </div>
          
          <!-- 통계를 아직 조회하지 않은 경우 -->
          <div v-else class="no-statistics">
            통계를 조회하려면 위의 기간을 선택하고 '통계 조회' 버튼을 클릭하세요.
          </div>
        </div>

        <!-- 디자인 옵션 정보 (있는 경우) -->
        <div v-if="qrCode.designOptions" class="info-section">
          <h3>디자인 옵션</h3>
          <div class="info-row">
            <div class="info-label">디자인 옵션</div>
            <div class="info-value">{{ formatDesignOptions(qrCode.designOptions) }}</div>
          </div>
        </div>

        <!-- 연결된 이벤트 정보 (있는 경우) -->
        <div v-if="qrCode.linkedEventId || qrCode.linked_event_id" class="info-section">
          <h3>연결된 이벤트</h3>
          <div class="info-row">
            <div class="info-label">이벤트 ID</div>
            <div class="info-value">{{ qrCode.linkedEventId || qrCode.linked_event_id }}</div>
          </div>
        </div>
      </div>

      <!-- QR 코드 이미지 표시 -->
      <div class="qr-code-image">
        <h3>QR 코드 이미지</h3>
        <!-- qrCode 데이터가 있고 imageUrl이 존재하며 로드 오류가 없을 경우 이미지 표시 -->
        <img
          v-if="qrCode && (qrCode.imageUrl || qrCode.image_url) && !imageLoadError"
          :src="fullImageUrl"
          alt="QR Code Image"
          class="qr-image"
          @error="handleImageError"
        />
        <!-- imageUrl이 없거나 로드 오류가 발생한 경우 플레이스홀더 표시 -->
        <div v-else class="image-placeholder">
          {{ imageLoadError ? '이미지를 불러올 수 없습니다.' : 'QR 코드 이미지를 사용할 수 없습니다.' }}
        </div>

        <!-- 다운로드 버튼 추가 -->
        <div v-if="qrCode && (qrCode.imageUrl || qrCode.image_url)" class="image-actions">
          <button @click="downloadQrCode" class="download-btn">QR 코드 다운로드</button>
        </div>
      </div>
    </div>

    <div v-else class="no-data">
      QR 코드 정보를 찾을 수 없습니다.
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getQrCodeById } from '@/api/qrcode';
import { getQrCodeStatistics } from '@/api';
import { handleApiError } from '@/utils/errorHandler';
import { useAuthStore } from '@/stores/auth';
import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';

// ChartJS 등록
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const qrCode = ref(null);
const isLoading = ref(true);
const error = ref(null);
const imageLoadError = ref(false); // 이미지 로드 오류 상태
const installedImageLoadError = ref(false); // 설치 위치 이미지 로드 오류 상태

// QR 코드 통계 관련 변수
const statisticsData = ref(null); // 통계 데이터
const isStatisticsLoading = ref(false); // 통계 로딩 상태
const statisticsError = ref(null); // 통계 오류 메시지

// 차트 관련 변수
const chartData = ref({
  labels: [],
  datasets: [
    {
      label: '일별 스캔 수',
      backgroundColor: '#2196F3',
      data: []
    }
  ]
});

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true
    },
    tooltip: {
      mode: 'index',
      intersect: false
    },
    title: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        precision: 0
      }
    }
  }
});

// 초기 기간 설정: 오늘부터 7일 전
const today = computed(() => {
  const now = new Date();
  return now.toISOString().substring(0, 10); // YYYY-MM-DD 포맷
});

// 초기 시작일: 한 달 전
const date = new Date();
date.setMonth(date.getMonth() - 1);
const statisticsStartDate = ref(date.toISOString().substring(0, 10));

// 초기 종료일: 오늘
const statisticsEndDate = ref(today.value);

// 서버 기본 URL (환경 변수에서 가져옴)
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// 현재 선택된 프로젝트
const currentProject = computed(() => {
  return authStore.currentProject;
});

// 이미지 URL 처리를 위한 유틸리티 함수
const getFullImageUrl = (imagePath) => {
  if (!imagePath) return '';

  // 이미지 URL이 이미 전체 URL인 경우 그대로 사용
  if (imagePath.startsWith('http')) {
    return imagePath;
  }

  // API_BASE_URL이 정의되어 있는 경우
  if (API_BASE_URL) {
    // API_BASE_URL에서 '/api/way' 부분 제거 (필요한 경우)
    let baseUrl = API_BASE_URL;
    if (baseUrl.endsWith('/api/way')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - 8);
    } else if (baseUrl.endsWith('/api/way/')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - 9);
    }

    // 중복 슬래시 방지
    baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    let imageUrlPart = imagePath;

    // 선행 슬래시 제거
    if (imageUrlPart.startsWith('/')) {
      imageUrlPart = imageUrlPart.slice(1);
    }

    return `${baseUrl}/${imageUrlPart}`;
  }

  // API_BASE_URL이 없는 경우 그대로 반환
  return imagePath;
};

// 전체 이미지 URL 계산
const fullImageUrl = computed(() => {
  if (!qrCode.value) return '';
  const imageUrl = qrCode.value.imageUrl || qrCode.value.image_url;
  return getFullImageUrl(imageUrl);
});

// QR 설치 위치 이미지 URL 계산
const qrInstalledImageUrl = computed(() => {
  if (!qrCode.value || !qrCode.value.qrInstalledImagePath) return '';
  return getFullImageUrl(qrCode.value.qrInstalledImagePath);
});

// 이미지 로드 에러 핸들러
const handleImageError = () => {
  console.error('QR 코드 이미지 로드 실패:', fullImageUrl.value);
  imageLoadError.value = true;
};

// 설치 위치 이미지 로드 에러 핸들러
const handleInstalledImageError = () => {
  console.error('QR 설치 위치 이미지 로드 실패:', qrInstalledImageUrl.value);
  installedImageLoadError.value = true;
};

// QR 코드 타입 포맷팅
const formatQrType = (type) => {
  const typeMap = {
    'URL': 'URL',
    'TEXT': '텍스트',
    'VCARD': '연락처',
    'EMAIL': '이메일',
    'SNS': 'SNS',
    'WIFI': 'Wi-Fi',
    'GEO': '위치',
    'LANDING_PAGE': '랜딩페이지',
    'EVENT_ATTENDANCE': '이벤트'
  };
  return typeMap[type] || type;
};

// 상태 포맷팅
const formatStatus = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'INACTIVE': '비활성',
    'EXPIRED': '만료됨',
    'DELETED': '삭제됨'
  };
  return statusMap[status] || status;
};

// 날짜 포맷팅
const formatDate = (dateString) => {
  if (!dateString) return '-';

  // 서버에서 받은 날짜 문자열을 그대로 반환
  return dateString;
};

// 디자인 옵션 포맷팅
const formatDesignOptions = (designOptions) => {
  if (!designOptions) return '-';

  // 문자열인 경우 JSON으로 파싱 시도
  if (typeof designOptions === 'string') {
    try {
      const parsedOptions = JSON.parse(designOptions);
      return JSON.stringify(parsedOptions, null, 2);
    } catch (e) {
      console.error('디자인 옵션 파싱 실패:', e);
      return designOptions; // 파싱 실패 시 원본 문자열 반환
    }
  }

  // 이미 객체인 경우 JSON 문자열로 변환
  return JSON.stringify(designOptions, null, 2);
};

// QR 코드 수정 페이지로 이동
const editQrCode = () => {
  router.push({
    name: 'qr-form',
    params: { qrCodeId: route.params.qrCodeId },
    query: { projectId: route.query.projectId }
  });
};

// 목록으로 돌아가기
const goBack = () => {
  router.push({ name: 'qr-management' });
};

// QR 코드 다운로드 함수
const downloadQrCode = async () => {
  if (!qrCode.value) return;

  const imageUrl = qrCode.value.imageUrl || qrCode.value.image_url;
  if (!imageUrl) {
    alert('다운로드할 QR 코드 이미지가 없습니다.');
    return;
  }

  try {
    // 이미지 URL에서 파일명 추출 ('/qrcodes/' 다음에 오는 부분)
    let fileName = '';
    const qrcodesIndex = imageUrl.indexOf('/qrcodes/');
    if (qrcodesIndex !== -1) {
      fileName = imageUrl.substring(qrcodesIndex + '/qrcodes/'.length);
    } else {
      // 기존 방식으로 폴백: 마지막 '/' 이후의 문자열을 파일명으로 사용
      fileName = imageUrl.split('/').pop();
    }

    if (!fileName) {
      alert('파일명을 추출할 수 없습니다.');
      return;
    }

    // 파일 다운로드 경로
    const downloadPath = `/download/${fileName}`;

    // API_BASE_URL에서 '/api/way' 부분 제거 (필요한 경우)
    let baseUrl = API_BASE_URL;
    if (baseUrl.endsWith('/api/way')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - 8);
    } else if (baseUrl.endsWith('/api/way/')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - 9);
    }

    const fullDownloadUrl = `${baseUrl}/api/way${downloadPath}`;

    // Fetch를 사용하여 이미지 다운로드
    const response = await fetch(fullDownloadUrl, {
      headers: {
        'Authorization': `Bearer ${authStore.accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`이미지 다운로드 실패: ${response.status} ${response.statusText}`);
    }

    // 응답을 Blob으로 변환
    const blob = await response.blob();

    // Content-Type 헤더에서 확장자 확인
    let fileExtension = fileName.split('.').pop() || 'png'; // 기본값
    const contentType = response.headers.get('content-type');
    if (contentType) {
      if (contentType.includes('image/svg+xml')) {
        fileExtension = 'svg';
      } else if (contentType.includes('image/png')) {
        fileExtension = 'png';
      } else if (contentType.includes('image/jpeg')) {
        fileExtension = 'jpg';
      } else if (contentType.includes('image/gif')) {
        fileExtension = 'gif';
      }
    }

    // 다운로드 링크 생성 및 클릭
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 다운로드 파일명 설정
    const downloadFileName = `${qrCode.value.qrName || 'qrcode'}.${fileExtension}`;
    link.setAttribute('download', downloadFileName);

    document.body.appendChild(link);
    link.click();

    // 링크 제거
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);

  } catch (error) {
    console.error('QR 코드 다운로드 실패:', error);
    alert(`QR 코드 다운로드 중 오류가 발생했습니다: ${error.message}`);
  }
};

// QR 코드 상세 정보 불러오기
const fetchQrCode = async () => {
  // route 객체가 아직 초기화되지 않았을 수 있음
  if (!route || !route.params) {
    console.error('Route 객체가 아직 초기화되지 않았습니다.');
    error.value = '페이지 로딩 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
    isLoading.value = false;
    return;
  }

  const qrCodeId = route.params.qrCodeId;
  const projectId = route.query.projectId || currentProject.value?.projectId;

  // SUPER_ADMIN 역할 확인
  const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';
  
  // SUPER_ADMIN이 아닌 경우에만 projectId 확인
  if (!qrCodeId || (!isSuperAdmin && !projectId)) {
    error.value = 'QR 코드 ID 또는 프로젝트 ID가 유효하지 않습니다.';
    isLoading.value = false;
    return;
  }

  isLoading.value = true;
  error.value = null;

  try {
    const response = await getQrCodeById(qrCodeId);

    // 서버 응답 구조 확인
    if (response && response.success === true) {
      // 서버 응답이 { success: true, data: {...} } 구조인 경우
      if (response.data) {
        qrCode.value = response.data;
      } else {
        // 서버 응답이 { success: true, ...qrCodeData } 구조인 경우
        qrCode.value = response;
      }
    } else {
      // 다른 구조의 응답인 경우
      qrCode.value = response;
    }

    if (qrCode.value) {
      
      // QR 코드 데이터 로드 성공 후 통계 데이터 자동 로드
      setTimeout(() => fetchQrCodeStatistics(), 300); // 약간의 지연을 주어 QR 코드 데이터가 완전히 로드되도록 함
    }
  } catch (err) {
    error.value = handleApiError(err, 'QR 코드 상세 정보를 불러오는 중 오류가 발생했습니다.');
    console.error('QR 코드 상세 정보 조회 실패:', err);
  } finally {
    isLoading.value = false;
  }
};

// 날짜 값 검증
const validateDates = () => {
  // 시작일이 종료일보다 나중이면 시작일을 종료일과 동일하게 설정
  if (statisticsStartDate.value > statisticsEndDate.value) {
    statisticsStartDate.value = statisticsEndDate.value;
    alert('시작일은 종료일보다 나중일 수 없습니다.');
  }

  // 종료일이 오늘보다 나중이면 오늘로 설정
  if (statisticsEndDate.value > today.value) {
    statisticsEndDate.value = today.value;
    alert('종료일은 오늘 이후로 설정할 수 없습니다.');
  }
};

// QR 코드 통계 조회 함수
const fetchQrCodeStatistics = async () => {
  // QR 코드 ID 확인
  if (!qrCode.value || !qrCode.value.qrCodeId) {
    statisticsError.value = 'QR 코드 정보를 먼저 불러와야 합니다.';
    return;
  }

  // 날짜 검증
  validateDates();

  const qrCodeId = qrCode.value.qrCodeId;
  isStatisticsLoading.value = true;
  statisticsError.value = null;

  try {
    const response = await getQrCodeStatistics(qrCodeId, statisticsStartDate.value, statisticsEndDate.value);

    // 서버 응답 구조 확인
    if (response && response.data) {
      if (response.data.success === true && response.data.data) {
        // 응답 구조가 { success: true, data: { ... } } 인 경우
        statisticsData.value = response.data.data;
      } else {
        // 다른 구조의 응답
        statisticsData.value = response.data;
      }
    } else {
      // 다른 형태의 응답
      statisticsData.value = response;
    }

    // 차트 데이터 설정
    if (statisticsData.value && statisticsData.value.dailyScanCounts && statisticsData.value.dailyScanCounts.length > 0) {
      // 날짜와 값 추출
      const labels = [];
      const data = [];
      
      statisticsData.value.dailyScanCounts.forEach(item => {
        labels.push(formatDate(item.date));
        data.push(item.count);
      });
      
      // 차트 데이터 업데이트
      chartData.value = {
        labels,
        datasets: [
          {
            label: '일별 스캔 수',
            backgroundColor: '#2196F3',
            data
          }
        ]
      };
      
    } 
  } catch (err) {
    statisticsError.value = handleApiError(err, 'QR 코드 통계를 불러오는 중 오류가 발생했습니다.');
    console.error('QR 코드 통계 조회 실패:', err);
  } finally {
    isStatisticsLoading.value = false;
  }
};

// 컴포넌트 마운트 시 QR 코드 상세 정보 로드
onMounted(() => {
  // 마운트 시 약간의 지연을 주어 라우터가 초기화될 시간을 확보
  setTimeout(() => {
    fetchQrCode();
  }, 100);
});

// QR 코드 ID나 프로젝트 ID 변경 시 데이터 다시 로드 (선택 사항, 예시)
// watch([() => route.params.qrCodeId, () => route.query.projectId], () => {
//   imageLoadError.value = false; // 상태 초기화
//   fetchQrCode();
// }, { immediate: false });

</script>

<style scoped>
.qr-code-view {
  padding: 20px;
}

.qr-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.qr-code-header h2 {
  margin: 0;
  color: #333;
}

.qr-code-actions {
  display: flex;
  gap: 10px;
}

.edit-btn, .back-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.edit-btn {
  background-color: #FFC107;
  color: black;
}

.edit-btn:hover {
  background-color: #e6af07;
}

.back-btn {
  background-color: #607D8B;
  color: white;
}

.back-btn:hover {
  background-color: #546E7A;
}

.qr-code-info {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  border-bottom: 1px solid #eee;
  padding-bottom: 12px;
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.info-label {
  width: 150px;
  font-weight: bold;
  color: #555;
}

.info-value {
  flex: 1;
}

.content-value {
  word-break: break-all;
}

.qr-code-image {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.qr-code-image h3 {
  margin-top: 0;
  color: #333;
}

.qr-image {
  display: block;
  max-width: 300px;
  max-height: 300px;
  width: auto;
  height: auto;
  margin: 10px auto;
  border: 1px solid #eee;
}

.image-placeholder {
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #ccc;
  border-radius: 4px;
  color: #999;
}

.loading, .error-message, .no-data {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
}

.error-message {
  color: #f44336;
  background-color: #ffebee;
}

.no-data {
  color: #757575;
}

.image-actions {
  margin-top: 15px;
  text-align: center;
}

.download-btn {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.download-btn:hover {
  background-color: #45a049;
}
.installed-image {
  max-width: 100%;
  max-height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 5px;
}

/* QR 코드 통계 섹션 스타일 */
.qr-code-statistics {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.qr-code-statistics h3 {
  margin-top: 0;
  color: #333;
  margin-bottom: 15px;
}

.statistics-date-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.date-picker-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-picker-container label {
  font-weight: bold;
  color: #555;
}

.date-picker-container input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.statistics-btn {
  padding: 8px 16px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.statistics-btn:hover {
  background-color: #0b7dda;
}

.statistics-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-statistics, .error-statistics, .no-statistics {
  padding: 15px;
  text-align: center;
  border-radius: 4px;
  margin-top: 10px;
}

.loading-statistics {
  color: #555;
  background-color: #f8f8f8;
}

.error-statistics {
  color: #f44336;
  background-color: #ffebee;
}

.no-statistics {
  color: #757575;
  background-color: #f5f5f5;
  padding: 20px;
}

.statistics-data {
  margin-top: 15px;
}

.statistic-card {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  text-align: center;
}

.statistic-title {
  font-size: 14px;
  color: #555;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #2196F3;
}

.statistics-table-container {
  margin-top: 20px;
}

.statistics-table-container h4 {
  margin-bottom: 10px;
}

.statistics-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e0e0e0;
}

.statistics-table th, .statistics-table td {
  padding: 10px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.statistics-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.statistics-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.statistics-table tr:hover {
  background-color: #f0f0f0;
}

.no-statistics-data {
  text-align: center;
  padding: 15px;
  color: #757575;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-top: 15px;
}

/* 차트 관련 스타일 */
.chart-container {
  margin-top: 25px;
  margin-bottom: 25px;
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.chart-wrapper {
  height: 300px;
  position: relative;
}

/* 통계 요약 영역 */
.statistics-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.statistics-summary .statistic-card {
  flex: 1;
  min-width: 200px;
}
</style>
