<template>
  <div class="statistics-view">
    <h1>QR 코드 통계</h1>
    
    <div v-if="!canViewStatistics" class="project-select-message">
      <div class="message-container">
        <p>프로젝트를 선택해주세요.</p>
      </div>
    </div>

    <div v-else>
      <!-- 날짜 필터링 섹션 시작 -->
      <div class="date-filter-container">
        <div class="date-inputs">
          <div class="date-input-group">
            <label for="start-date">시작일:</label>
            <input type="date" id="start-date" v-model="startDate" :max="endDate" />
          </div>
          <div class="date-input-group">
            <label for="end-date">종료일:</label>
            <input type="date" id="end-date" v-model="endDate" :min="startDate" />
          </div>
        </div>
        <button @click="applyDateFilter" class="search-btn">검색</button>
        <div v-if="dateError" class="date-error-message">{{ dateError }}</div>
      </div>
      <!-- 날짜 필터링 섹션 끝 -->

    <!-- 스크롤 버튼들 시작 -->
    <div class="scroll-buttons">
      <button @click="scrollToTop" class="scroll-button top-button" title="맨 위로 가기">▲</button>
      <button @click="scrollToBottom" class="scroll-button bottom-button" title="맨 아래로 가기">▼</button>
    </div>
    <!-- 스크롤 버튼들 끝 -->

    <!-- 기본 통계 정보 표시 시작 -->
    <div v-if="basicStats && !isLoading && !error" class="stat-card basic-stats-card-readded">
      <div class="card-header">
        <h2>기본 현황</h2>
        <button @click="toggleCard('basic')" class="toggle-button">
          {{ cardVisibility.basic ? '접기' : '펴기' }}
        </button>
      </div>
      <div v-show="cardVisibility.basic" class="card-content">
        <div class="summary-stats-readded">
          <div class="summary-item-readded">
            <span class="label-readded">총 QR 코드 수:</span>
            <span class="value-readded">{{ basicStats.totalQrCodes !== undefined ? basicStats.totalQrCodes : 'N/A' }}</span>
          </div>
          <div class="summary-item-readded">
            <span class="label-readded">총 스캔 수:</span>
            <span class="value-readded">{{ basicStats.totalScans !== undefined ? basicStats.totalScans : 'N/A' }}</span>
          </div>
        </div>
      </div>

      <!-- 일별 QR 활동 차트 컨테이너 시작 -->
      <div class="chart-container daily-activity-chart-container" style="margin-top: 20px;">
        <h3 style="text-align: center; margin-bottom: 15px; font-size: 1.1em; color: #555;">{{ dailyActivityChartTitle }}</h3>
        <div v-if="dailyActivityHasDisplayableData" class="chart-wrapper" id="dailyActivityChartWrapper">
          <canvas ref="dailyActivityChartRef" id="dailyActivityChart"></canvas>
        </div>
        <!-- 로딩 표시 (차트용) -->
        <div v-if="isLoading && !dailyActivityHasDisplayableData" class="chart-placeholder">
          <div class="mini-spinner"></div>
          <span>일별 활동 차트를 불러오는 중입니다...</span>
        </div>
        <!-- 데이터 없음 표시 (차트용) -->
        <div v-if="!isLoading && !isChartLoading && !dailyActivityHasDisplayableData" class="no-data" style="height: 250px;">
          <div class="no-data-icon">📈</div>
          <div class="no-data-text">일별 활동 데이터가 없습니다</div>
          <div class="no-data-subtext">{{ dailyActivityNoDataSubtext }}</div>
        </div>
      </div>
      <!-- 일별 QR 활동 차트 컨테이너 끝 -->
    </div>
    <!-- 기본 통계 정보 표시 끝 -->

    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>통계 데이터를 불러오는 중입니다...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="loadAllStatistics" class="retry-button">다시 시도</button>
    </div>

    <div v-if="!isLoading && !error && isChartLoading" class="loading-container chart-loading">
      <div class="loading-spinner"></div>
      <p>차트를 생성하는 중입니다...</p>
      <div class="loading-progress">잠시만 기다려주세요</div>
    </div>

    <div v-if="!isLoading && !error" class="statistics-container">
      <!-- QR 코드 타입별 분포 -->
      <div class="stat-card">
        <div class="card-header">
          <h2>QR 코드 타입별 분포</h2>
          <button @click="toggleCard('type')" class="toggle-button">
            {{ cardVisibility.type ? '접기' : '펴기' }}
          </button>
        </div>
        <div v-show="cardVisibility.type" class="card-content">
          <!-- 차트 컨테이너는 항상 존재하도록 함 (v-show 사용) -->
          <div v-if="typeDistribution && typeDistribution.distribution && Object.keys(typeDistribution.distribution).length > 0" class="chart-container">
            <div class="chart-wrapper" id="typeDistributionChartContainer">
              <canvas ref="typeDistributionChart" id="typeDistributionCanvas"></canvas>
            </div>
            <div class="data-table" v-if="typeDistribution">
              <table>
                <thead>
                  <tr>
                    <th>타입</th>
                    <th>개수</th>
                    <th>비율</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- API 응답 구조에 따라 적절한 데이터 소스 선택 -->
                  <template v-if="typeDistribution.distribution && Object.keys(typeDistribution.distribution).length > 0">
                    <tr v-for="(count, type) in typeDistribution.distribution" :key="type">
                      <td>{{ getQrTypeName(type) }}</td>
                      <td>{{ count }}</td>
                      <td>{{ typeDistribution.percentage && typeDistribution.percentage[type] ? typeDistribution.percentage[type] : '0' }}%</td>
                    </tr>
                  </template>
                  <template v-else>
                    <tr>
                      <td colspan="3" class="no-data-cell">표시할 데이터가 없습니다</td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 로딩 표시 -->
          <div v-if="isChartLoading && (!typeDistribution || Object.keys(typeDistribution.distribution || {}).length === 0)" class="chart-placeholder">
            <div class="mini-spinner"></div>
            <span>차트를 생성하는 중입니다...</span>
          </div>

          <!-- 데이터 없음 표시 -->
          <div v-if="!isChartLoading && !isLoading && (!typeDistribution || !typeDistribution.distribution || Object.keys(typeDistribution.distribution).length === 0)" class="no-data">
            <div class="no-data-icon">📊</div>
            <div class="no-data-text">데이터가 없습니다</div>
            <div class="no-data-subtext">이 차트에 표시할 QR 코드 타입 분포 데이터가 없습니다. QR 코드가 생성되면 여기에 통계가 표시됩니다.</div>
          </div>
        </div>
      </div>

      <!-- QR 코드 상태별 분포 -->
      <div class="stat-card">
        <div class="card-header">
          <h2>QR 코드 상태별 분포</h2>
          <button @click="toggleCard('status')" class="toggle-button">
            {{ cardVisibility.status ? '접기' : '펴기' }}
          </button>
        </div>
        <div v-show="cardVisibility.status" class="card-content">
          <div v-if="statusDistribution && statusDistribution.distribution && Object.keys(statusDistribution.distribution).length > 0" class="chart-container">
            <div class="chart-wrapper" id="statusDistributionChartContainer">
              <canvas ref="statusDistributionChart" id="statusDistributionCanvas"></canvas>
            </div>
            <div class="data-table" v-if="statusDistribution">
              <table>
                <thead>
                  <tr>
                    <th>상태</th>
                    <th>개수</th>
                    <th>비율</th>
                  </tr>
                </thead>
                <tbody>
                  <template v-if="statusDistribution.distribution && Object.keys(statusDistribution.distribution).length > 0">
                    <tr v-for="(count, status) in statusDistribution.distribution" :key="status">
                      <td>{{ getStatusName(status) }}</td>
                      <td>{{ count }}</td>
                      <td>{{ statusDistribution.percentage && statusDistribution.percentage[status] ? statusDistribution.percentage[status] : '0' }}%</td>
                    </tr>
                  </template>
                  <template v-else>
                    <tr>
                      <td colspan="3" class="no-data-cell">표시할 데이터가 없습니다</td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>

          <div v-if="isChartLoading && (!statusDistribution || Object.keys(statusDistribution.distribution || {}).length === 0)" class="chart-placeholder">
            <div class="mini-spinner"></div>
            <span>차트를 생성하는 중입니다...</span>
          </div>

          <div v-if="!isChartLoading && !isLoading && (!statusDistribution || !statusDistribution.distribution || Object.keys(statusDistribution.distribution).length === 0)" class="no-data">
            <div class="no-data-icon">🔄</div>
            <div class="no-data-text">데이터가 없습니다</div>
            <div class="no-data-subtext">이 차트에 표시할 QR 코드 상태 분포 데이터가 없습니다. QR 코드가 생성되면 여기에 통계가 표시됩니다.</div>
          </div>
        </div>
      </div>

      <!-- QR 코드 스캔 기기 통계 -->
      <div class="stat-card">
        <div class="card-header">
          <h2>QR 코드 스캔 기기 통계</h2>
          <button @click="toggleCard('device')" class="toggle-button">
            {{ cardVisibility.device ? '접기' : '펴기' }}
          </button>
        </div>
        <div v-show="cardVisibility.device" class="card-content">
          <div v-if="deviceStats && ( (deviceStats.device && deviceStats.device.counts && Object.keys(deviceStats.device.counts).length > 0) || (deviceStats.browser && deviceStats.browser.counts && Object.keys(deviceStats.browser.counts).length > 0) || (deviceStats.os && deviceStats.os.counts && Object.keys(deviceStats.os.counts).length > 0) )" class="chart-container">
            <div class="charts-row">
              <div class="chart-column">
                <h3>기기 유형</h3>
                <canvas ref="deviceTypeChart" id="deviceTypeCanvas"></canvas>
              </div>
              <div class="chart-column">
                <h3>브라우저</h3>
                <canvas ref="browserChart" id="browserCanvas"></canvas>
              </div>
              <div class="chart-column">
                <h3>운영체제</h3>
                <canvas ref="osChart" id="osCanvas"></canvas>
              </div>
            </div>
          </div>
          <div v-if="!isChartLoading && !isLoading && (!deviceStats || !( (deviceStats.device && deviceStats.device.counts && Object.keys(deviceStats.device.counts).length > 0) || (deviceStats.browser && deviceStats.browser.counts && Object.keys(deviceStats.browser.counts).length > 0) || (deviceStats.os && deviceStats.os.counts && Object.keys(deviceStats.os.counts).length > 0) ))" class="no-data">
            <div class="no-data-icon">📱</div>
            <div class="no-data-text">데이터가 없습니다</div>
            <div class="no-data-subtext">이 차트에 표시할 기기 통계 데이터가 없습니다. QR 코드가 스캔되면 여기에 통계가 표시됩니다.</div>
          </div>
        </div>
      </div>

      <!-- QR 코드 스캔 시간 통계 -->
      <div class="stat-card">
        <div class="card-header">
          <h2>QR 코드 스캔 시간 통계</h2>
          <button @click="toggleCard('time')" class="toggle-button">
            {{ cardVisibility.time ? '접기' : '펴기' }}
          </button>
        </div>
        <div v-show="cardVisibility.time" class="card-content">
          <div v-if="timeStatsDataExists" class="chart-container">
            <div class="charts-row">
              <div class="chart-column wide">
                <h3>시간대별 스캔 횟수</h3>
                <canvas ref="hourlyChart" id="hourlyCanvas"></canvas>
              </div>
              <div class="chart-column wide">
                <h3>요일별 스캔 횟수</h3>
                <canvas ref="weekdayChart" id="weekdayCanvas"></canvas>
              </div>
            </div>
          </div>
          <!-- 로딩 플레이스홀더 추가 -->
          <div v-if="(isLoading || isChartLoading) && !timeStatsDataExists" class="chart-placeholder">
            <div class="mini-spinner"></div>
            <span>시간 통계 정보를 불러오는 중입니다...</span>
          </div>
          <!-- 기존 no-data div의 v-if 조건 수정 -->
          <div v-if="!isLoading && !isChartLoading && !timeStatsDataExists" class="no-data">
            <div class="no-data-icon">⏰</div>
            <div class="no-data-text">데이터가 없습니다</div>
            <div class="no-data-subtext">이 차트에 표시할 시간 통계 데이터가 없습니다. QR 코드가 스캔되면 여기에 통계가 표시됩니다.</div>
          </div>
        </div>
      </div>

      <!-- 교환권 QR 코드 통계 -->
      <div class="stat-card">
        <div class="card-header">
          <h2>교환권 QR 코드 통계</h2>
          <button @click="toggleCard('exchange')" class="toggle-button">
            {{ cardVisibility.exchange ? '접기' : '펴기' }}
          </button>
        </div>
        <div v-show="cardVisibility.exchange" class="card-content">
          <div v-if="exchangeStats && exchangeStats.total && exchangeStats.total.totalExchangeQrCodes > 0" class="chart-container">
            <div class="summary-stats">
              <div class="summary-item">
                <span class="label">총 교환권 QR 코드 수</span>
                <span class="value">{{ exchangeStats.total.totalExchangeQrCodes }}</span>
              </div>
              <div class="summary-item">
                <span class="label">총 교환 가능 횟수</span>
                <span class="value">{{ exchangeStats.total.totalExchangeCount }}</span>
              </div>
              <div class="summary-item">
                <span class="label">총 사용 횟수</span>
                <span class="value">{{ exchangeStats.total.totalUsedCount }}</span>
              </div>
              <div class="summary-item">
                <span class="label">사용률</span>
                <span class="value">{{ exchangeStats.total.usageRate }}%</span>
              </div>
            </div>

            <div class="charts-row" v-if="(exchangeStats.daily && exchangeStats.daily.counts && exchangeStats.daily.counts.some(c => c > 0)) || (exchangeStats.approver && exchangeStats.approver.counts && Object.keys(exchangeStats.approver.counts).length > 0) || (exchangeStats.hourly && Object.keys(exchangeStats.hourly).length > 0)">
              <div class="chart-column" v-if="exchangeStats.daily && exchangeStats.daily.counts && exchangeStats.daily.counts.some(c => c > 0)">
                <h3>일별 승인 횟수</h3>
                <canvas ref="approvalCountsChart" id="approvalCountsCanvas"></canvas>
              </div>
              <div class="chart-column" v-if="exchangeStats.approver && exchangeStats.approver.counts && Object.keys(exchangeStats.approver.counts).length > 0">
                <h3>승인자별 승인 횟수</h3>
                <canvas ref="approverCountsChart" id="approverCountsCanvas"></canvas>
              </div>
              <div class="chart-column" v-if="exchangeStats.hourly && Object.keys(exchangeStats.hourly).length > 0">
                <h3>시간대별 승인 횟수</h3>
                <canvas ref="hourlyApprovalChart" id="hourlyApprovalCanvas"></canvas>
              </div>
            </div>
          </div>
          <div v-if="!isChartLoading && !isLoading && (!exchangeStats || !exchangeStats.total || exchangeStats.total.totalExchangeQrCodes === 0)" class="no-data">
            <div class="no-data-icon">🎟️</div>
            <div class="no-data-text">데이터가 없습니다</div>
            <div class="no-data-subtext">이 차트에 표시할 교환권 통계 데이터가 없습니다. 교환권 QR 코드가 생성되거나 사용되면 여기에 통계가 표시됩니다.</div>
          </div>
        </div>
      </div>

      <!-- QR 코드 스캔 위치 통계 시작 -->
      <div class="stat-card">
        <div class="card-header">
          <h2>QR 코드 스캔 위치</h2>
          <button @click="toggleCard('location')" class="toggle-button">
            {{ cardVisibility.location ? '접기' : '펴기' }}
          </button>
        </div>
        <div v-show="cardVisibility.location" class="card-content">
          <!-- 카카오맵 컨테이너 시작 -->
          <div v-if="scannedLocationDataExists" id="kakao-map-container" ref="mapContainerRef" style="width:100%;height:400px;"></div>
          <!-- 카카오맵 컨테이너 끝 -->

          <!-- 기존 테이블은 유지하거나 필요에 따라 제거/수정할 수 있습니다. -->
          <div v-if="scannedLocationDataExists" class="qr-list-controls-and-table">
            <!-- 페이지당 항목 수 선택 드롭다운 시작 -->
            <div class="list-controls">
              <div class="items-per-page-selector">
                <label for="itemsPerPage">페이지당 항목:</label>
                <select id="itemsPerPage" v-model.number="itemsPerPage">
                  <option v-for="option in itemsPerPageOptions" :key="option" :value="option">
                    {{ option }}
                  </option>
                </select>
              </div>
            </div>
            <!-- 페이지당 항목 수 선택 드롭다운 끝 -->

            <div class="data-table">
              <table>
                <thead>
                  <tr>
                    <th>QR 이름</th>
                    <th>QR 타입</th>
                    <th>위치</th>
                    <th>스캔 횟수</th>
                    <th>생성일</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="scan in paginatedQrCodes" :key="scan.qrCodeId" @click="panToMapLocation(scan)" class="qr-list-row">
                    <td>{{ scan.qrName || '-' }}</td>
                    <td>{{ getQrTypeName(scan.qrType) || '-' }}</td>
                    <td>{{ scan.location || '-' }}</td>
                    <td>{{ scan.scanCount !== undefined ? scan.scanCount : '-' }}</td>
                    <td>{{ scan.createdAt ? scan.createdAt : '-' }}</td>
                  </tr>
                  <tr v-if="paginatedQrCodes.length === 0">
                    <td :colspan="5" class="no-data-cell">현재 페이지에 표시할 QR 코드가 없습니다.</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 페이지네이션 컨트롤 시작 -->
            <div class="pagination-controls">
              <button @click="prevPage" :disabled="currentPage === 1">이전</button>
              <span>{{ currentPage }} / {{ totalPages }}</span>
              <button @click="nextPage" :disabled="currentPage === totalPages">다음</button>
            </div>
            <!-- 페이지네이션 컨트롤 끝 -->
          </div>
          <div v-if="(isLoading || isChartLoading) && !scannedLocationDataExists" class="chart-placeholder">
            <div class="mini-spinner"></div>
            <span>스캔 위치 정보를 불러오는 중입니다...</span>
          </div>
          <div v-if="!isLoading && !isChartLoading && !scannedLocationDataExists" class="no-data">
            <div class="no-data-icon">📍</div>
            <div class="no-data-text">데이터가 없습니다</div>
            <div class="no-data-subtext">스캔된 QR 코드 위치 정보가 없습니다. QR 코드가 스캔되면 여기에 통계가 표시됩니다.</div>
          </div>
        </div>
      </div>
      <!-- QR 코드 스캔 위치 통계 끝 -->
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { useAuthStore } from '@/stores/auth';
import Chart from 'chart.js/auto';
import { getQrComprehensiveStats } from '@/api/statistics';

const authStore = useAuthStore();

// SUPER_ADMIN이거나 현재 프로젝트가 선택되어 있으면 통계를 볼 수 있음
const canViewStatistics = computed(() => {
  return authStore.isSuperAdmin || authStore.currentProject;
});

const currentProjectId = computed(() => authStore.currentProject?.projectId);

// 상태 변수
const isLoading = ref(true);
const isChartLoading = ref(false); // 차트 렌더링 중 상태
const error = ref(null);
const basicStats = ref(null); // 'basic' 통계 데이터 ref 추가
const typeDistribution = ref(null);
const statusDistribution = ref(null);
const deviceStats = ref(null);
const timeStats = ref(null);
const exchangeStats = ref(null);
const scannedQrCodes = ref(null); // 스캔 위치 정보
const dailyChartData = ref(null); // 일별 QR 활동 데이터

// 날짜 필터링을 위한 ref
const formatDateForInput = (date) => {
  const d = new Date(date);
  let month = '' + (d.getMonth() + 1);
  let day = '' + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = '0' + month;
  if (day.length < 2) day = '0' + day;

  return [year, month, day].join('-');
};

const today = new Date();
const thirtyDaysAgo = new Date(new Date().setDate(today.getDate() - 30));

const startDate = ref(formatDateForInput(thirtyDaysAgo));
const endDate = ref(formatDateForInput(today));
const dateError = ref(null);

// 동적 차트 제목을 위한 computed 속성
const dailyActivityChartTitle = computed(() => {
  if (startDate.value && endDate.value) {
    return `${startDate.value} ~ ${endDate.value} QR 활동`;
  }
  return '일별 QR 활동'; // 기본 제목
});

// 동적 "데이터 없음" 보조 텍스트를 위한 computed 속성
const dailyActivityNoDataSubtext = computed(() => {
  if (startDate.value && endDate.value) {
    return `선택된 기간(${startDate.value} ~ ${endDate.value}) 동안 QR 코드 생성 또는 스캔 기록이 없습니다.`;
  }
  return 'QR 코드 생성 또는 스캔 기록이 없습니다.'; // 기본 메시지
});

// 일별 활동 차트에 실제 표시할 데이터가 있는지 확인하는 computed 속성
const dailyActivityHasDisplayableData = computed(() => {
  if (dailyChartData.value && dailyChartData.value.dates && dailyChartData.value.dates.length > 0) {
    // createdCounts 또는 scannedCounts 중 하나라도 0보다 큰 값이 있는지 확인
    const hasNonZeroCreation = dailyChartData.value.createdCounts && dailyChartData.value.createdCounts.some(count => count > 0);
    const hasNonZeroScan = dailyChartData.value.scannedCounts && dailyChartData.value.scannedCounts.some(count => count > 0);
    return hasNonZeroCreation || hasNonZeroScan;
  }
  return false; // 날짜 데이터가 없거나 dailyChartData 자체가 없는 경우
});

// 스캔 위치 데이터 존재 여부 computed 속성
const scannedLocationDataExists = computed(() => {
  return scannedQrCodes.value && scannedQrCodes.value.length > 0;
});

// 시간 통계 데이터 존재 여부 computed 속성
const timeStatsDataExists = computed(() => {
  if (timeStats.value) {
    const hourlyCounts = timeStats.value.hourly ? Object.values(timeStats.value.hourly) : [];
    const weekdayCounts = timeStats.value.weekday ? Object.values(timeStats.value.weekday) : [];

    const hasNonZeroHourly = hourlyCounts.some(count => count > 0);
    const hasNonZeroWeekday = weekdayCounts.some(count => count > 0);

    return hasNonZeroHourly || hasNonZeroWeekday;
  }
  return false;
});

// 각 통계 카드의 표시 상태를 관리하는 객체
const cardVisibility = ref({
  basic: true,
  type: true,
  status: true,
  device: true,
  time: true,
  exchange: true,
  location: true,
});

// 카드 표시 상태를 토글하는 함수
const toggleCard = (cardName) => {
  if (cardVisibility.value.hasOwnProperty(cardName)) {
    cardVisibility.value[cardName] = !cardVisibility.value[cardName];
  }
};

// 차트 참조
const typeDistributionChart = ref(null);
const statusDistributionChart = ref(null);
const deviceTypeChart = ref(null);
const browserChart = ref(null);
const osChart = ref(null);
const hourlyChart = ref(null);
const weekdayChart = ref(null);
const approvalCountsChart = ref(null);
const approverCountsChart = ref(null);
const hourlyApprovalChart = ref(null);
const dailyActivityChartRef = ref(null); // 일별 활동 차트 ref

// 차트 인스턴스 저장
const charts = ref({});


// QR 타입 이름 변환 함수
const getQrTypeName = (type) => {
  const typeMap = {
    'URL': 'URL',
    'TEXT': '텍스트',
    'WIFI': 'Wi-Fi',
    'SNS_LINK': 'SNS 링크',
    'EXCHANGE_VOUCHER_LINK': '교환권',
    'LANDING_PAGE': '랜딩 페이지',
    'EVENT_ATTENDANCE': '참가자',
    'LOCATION': '위치',
    'EVENT': '이벤트'
  };
  return typeMap[type] || type;
};

// 상태 이름 변환 함수
const getStatusName = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'INACTIVE': '비활성',
    'EXPIRED': '만료됨'
  };
  return statusMap[status] || status;
};

// 날짜 필터 적용 함수
const applyDateFilter = () => {
  dateError.value = null;
  if (!startDate.value || !endDate.value) {
    dateError.value = '시작일과 종료일을 모두 선택해주세요.';
    return;
  }
  if (new Date(startDate.value) > new Date(endDate.value)) {
    dateError.value = '시작일은 종료일보다 이전이거나 같아야 합니다.';
    return;
  }
  // 날짜 유효성 검사 통과 후 데이터 로드
  dataLoaded.value = false; // 데이터 로드 상태 초기화하여 watch 트리거
  loadAllStatistics(startDate.value, endDate.value);
};

// 모든 통계 데이터 로드
const loadAllStatistics = async (filterStartDate, filterEndDate) => {
  isLoading.value = true;
  error.value = null;
  dataLoaded.value = false; // 데이터 로드 상태 초기화
  // 데이터 초기화 추가
  basicStats.value = null;
  typeDistribution.value = null;
  statusDistribution.value = null;
  deviceStats.value = null;
  timeStats.value = null;
  exchangeStats.value = null;
  scannedQrCodes.value = null; // 스캔 위치 정보 초기화
  dailyChartData.value = null; // 일별 활동 데이터 초기화

  try {
    // 프로젝트 ID 설정
    const projectId = currentProjectId.value;
    // 종합 통계 API 호출
    const response = await getQrComprehensiveStats({
      projectId,
      includeTypes: 'basic,type,status,device,time,exchange',
      startDate: filterStartDate || startDate.value,
      endDate: filterEndDate || endDate.value,
    });

    if (response.success) {
      const statsData = response.data || {}; // response.data가 null일 경우 빈 객체로 처리

      // 'basic' 통계 데이터 할당
      basicStats.value = statsData.basic || {};

      // 다른 차트들의 데이터 할당 경로 수정 (API 응답 구조에 맞게)
      typeDistribution.value = statsData.typeDistribution;
      statusDistribution.value = statsData.statusDistribution;
      deviceStats.value = statsData.deviceStats;
      timeStats.value = statsData.timeStats;
      exchangeStats.value = statsData.exchangeStats;
      dailyChartData.value = statsData.dailyQrStats; // dailyQrStats가 null이어도 그대로 할당됨
      scannedQrCodes.value = statsData.scannedQrCodes?.qrCodes || [];

      dataLoaded.value = true;
    } else {
      console.error('API 응답 실패:', response);
      error.value = response.message || '통계 데이터를 가져오는데 실패했습니다.';
      // API 응답 실패 시, 각 통계 데이터를 초기화하거나 빈 상태로 유지
      basicStats.value = {};
      dailyChartData.value = null;
      typeDistribution.value = null;
      statusDistribution.value = null;
      deviceStats.value = null;
      timeStats.value = null;
      exchangeStats.value = null;
      scannedQrCodes.value = [];
      dataLoaded.value = true; // 데이터 로드 시도 완료로 처리
    }
  } catch (err) {
    console.error('통계 데이터 로드 중 오류 발생:', err);
    error.value = '통계 데이터를 불러오는 중 오류가 발생했습니다: ' + err.message;
    // 예외 발생 시, 각 통계 데이터를 초기화
    basicStats.value = {};
    dailyChartData.value = null;
    typeDistribution.value = null;
    statusDistribution.value = null;
    deviceStats.value = null;
    timeStats.value = null;
    exchangeStats.value = null;
    scannedQrCodes.value = [];
    dataLoaded.value = true; // 데이터 로드 시도 완료로 처리 (오류 발생 시에도)
  } finally {
    isLoading.value = false;
  }
};

// 차트 렌더링 함수
const renderCharts = () => {
  // 기존 차트 제거
  Object.values(charts.value).forEach(chart => {
    if (chart) {
      try {
        chart.destroy();
      } catch (e) {
        console.error('차트 제거 중 오류 발생:', e);
      }
    }
  });

  // 새 차트 객체 초기화
  charts.value = {};

  // 캔버스 요소 확인 함수
  const getCanvasContext = (ref) => {
    try {
      // 캔버스 ID 결정
      let canvasId = '';

      if (ref === typeDistributionChart) {
        canvasId = 'typeDistributionCanvas';
      } else if (ref === statusDistributionChart) {
        canvasId = 'statusDistributionCanvas';
      } else if (ref === deviceTypeChart) {
        canvasId = 'deviceTypeCanvas';
      } else if (ref === browserChart) {
        canvasId = 'browserCanvas';
      } else if (ref === osChart) {
        canvasId = 'osCanvas';
      } else if (ref === hourlyChart) {
        canvasId = 'hourlyCanvas';
      } else if (ref === weekdayChart) {
        canvasId = 'weekdayCanvas';
      } else if (ref === approvalCountsChart) {
        canvasId = 'approvalCountsCanvas';
      } else if (ref === approverCountsChart) {
        canvasId = 'approverCountsCanvas';
      } else if (ref === hourlyApprovalChart) {
        canvasId = 'hourlyApprovalCanvas';
      } else if (ref === dailyActivityChartRef) {
        canvasId = 'dailyActivityChart';
      }

      // ID로 캔버스 직접 찾기
      let canvas = document.getElementById(canvasId);
      // 캔버스가 없거나 ref가 없는 경우
      if (!canvas && (!ref || !ref.value)) {
        return null;
      }

      // ref.value가 있으면 사용
      if (ref && ref.value) {
        canvas = ref.value;
      }

      // 캔버스 컨텍스트 가져오기
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.warn('캔버스 컨텍스트를 가져올 수 없습니다');
        return null;
      }

      return ctx;
    } catch (e) {
      console.error('캔버스 컨텍스트 가져오기 오류:', e);
      return null;
    }
  };

  // 타입별 분포 차트
  if (typeDistribution.value) {
    // DOM에서 직접 캔버스 요소 찾기
    const canvas = document.getElementById('typeDistributionCanvas');
    if (!canvas) {
      console.error('타입별 분포 차트 캔버스를 찾을 수 없습니다. ID: typeDistributionCanvas');
      return; // 캔버스가 없으면 렌더링 중단
    }

    // 캔버스 컨텍스트 직접 가져오기
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('타입별 분포 차트 캔버스 컨텍스트를 가져올 수 없습니다.');
      return;
    }

    // 데이터 구조 확인 - 더 명확한 로깅
    let types = [];
    let counts = [];
    let percentages = {};

    // 더미 데이터 - 차트가 표시되지 않을 때 테스트용
    const dummyData = {
      'URL': 5,
      'TEXT': 3,
      'WIFI': 2,
      'SNS_LINK': 4,
      'LANDING_PAGE': 6
    };

    // typeDistribution.typeDistribution이 존재하는 경우 (템플릿에서 사용하는 구조)
    if (typeDistribution.value.typeDistribution && Object.keys(typeDistribution.value.typeDistribution).length > 0) {
      types = Object.keys(typeDistribution.value.typeDistribution);
      counts = Object.values(typeDistribution.value.typeDistribution);
      percentages = typeDistribution.value.typePercentage || {};
    }
    // distribution이 존재하는 경우 (원래 코드에서 사용하던 구조)
    else if (typeDistribution.value.distribution && Object.keys(typeDistribution.value.distribution).length > 0) {
      types = Object.keys(typeDistribution.value.distribution);
      counts = Object.values(typeDistribution.value.distribution);
      percentages = typeDistribution.value.percentage || {};
    }
    // 둘 다 없거나 빈 객체인 경우 더미 데이터 사용 (테스트용)
    else {
      console.warn('타입 분포 데이터 구조가 예상과 다릅니다. 더미 데이터를 사용합니다.');
      types = Object.keys(dummyData);
      counts = Object.values(dummyData);
      // 더미 퍼센티지 계산
      const total = counts.reduce((sum, count) => sum + count, 0);
      types.forEach(type => {
        percentages[type] = Math.round((dummyData[type] / total) * 100);
      });
    }

    const typeNames = types.map(getQrTypeName);

    try {
      // 기존 차트 제거 (있는 경우)
      if (charts.value.typeDistribution) {
        charts.value.typeDistribution.destroy();
      }

      // 새 차트 생성
      charts.value.typeDistribution = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: typeNames,
          datasets: [{
            data: counts,
            backgroundColor: [
              '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              display: true
            },
            tooltip: {
              enabled: true,
              callbacks: {
                label: (context) => {
                  const label = context.label || '';
                  const value = context.raw || 0;
                  const percentage = percentages[types[context.dataIndex]] || 0;
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    } catch (e) {
      console.error('타입별 분포 차트 생성 중 오류:', e, e.stack);
    }
  } else {
    console.warn('타입별 분포 차트 렌더링 조건이 충족되지 않음: 데이터 없음');
  }

  // 상태별 분포 차트
  if (statusDistribution.value) {
    // DOM에서 직접 캔버스 요소 찾기
    const canvas = document.getElementById('statusDistributionCanvas');
    if (!canvas) {
      console.error('상태별 분포 차트 캔버스를 찾을 수 없습니다. ID: statusDistributionCanvas');
      return; // 캔버스가 없으면 렌더링 중단
    }

    // 캔버스 컨텍스트 직접 가져오기
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('상태별 분포 차트 캔버스 컨텍스트를 가져올 수 없습니다.');
      return;
    }

    // 데이터 구조 확인 - 더 명확한 로깅
    let statuses = [];
    let counts = [];
    let percentages = {};

    // 더미 데이터 - 차트가 표시되지 않을 때 테스트용
    const dummyData = {
      'ACTIVE': 10,
      'INACTIVE': 5,
      'EXPIRED': 2
    };

    // statusDistribution.statusCounts가 존재하는 경우 (템플릿에서 사용하는 구조)
    if (statusDistribution.value.statusCounts && Object.keys(statusDistribution.value.statusCounts).length > 0) {
      statuses = Object.keys(statusDistribution.value.statusCounts);
      counts = Object.values(statusDistribution.value.statusCounts);
      percentages = statusDistribution.value.statusPercentage || {};
    }
    // distribution이 존재하는 경우 (원래 코드에서 사용하던 구조)
    else if (statusDistribution.value.distribution && Object.keys(statusDistribution.value.distribution).length > 0) {
      statuses = Object.keys(statusDistribution.value.distribution);
      counts = Object.values(statusDistribution.value.distribution);
      percentages = statusDistribution.value.percentage || {};
    }
    // 둘 다 없거나 빈 객체인 경우 더미 데이터 사용 (테스트용)
    else {
      console.warn('상태 분포 데이터 구조가 예상과 다릅니다. 더미 데이터를 사용합니다.');
      statuses = Object.keys(dummyData);
      counts = Object.values(dummyData);
      // 더미 퍼센티지 계산
      const total = counts.reduce((sum, count) => sum + count, 0);
      statuses.forEach(status => {
        percentages[status] = Math.round((dummyData[status] / total) * 100);
      });
    }

    const statusNames = statuses.map(getStatusName);

    try {
      // 기존 차트 제거 (있는 경우)
      if (charts.value.statusDistribution) {
        charts.value.statusDistribution.destroy();
      }

      // 새 차트 생성
      charts.value.statusDistribution = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: statusNames,
          datasets: [{
            data: counts,
            backgroundColor: [
              '#4BC0C0', '#FF6384', '#FFCE56'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              display: true
            },
            tooltip: {
              enabled: true,
              callbacks: {
                label: (context) => {
                  const label = context.label || '';
                  const value = context.raw || 0;
                  const percentage = percentages[statuses[context.dataIndex]] || 0;
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    } catch (e) {
      console.error('상태별 분포 차트 생성 중 오류:', e, e.stack);
    }
  } else {
    console.warn('상태별 분포 차트 렌더링 조건이 충족되지 않음: 데이터 없음');
  }

  // 기기 유형 차트
  if (deviceStats.value && deviceTypeChart.value) {
    const ctx = getCanvasContext(deviceTypeChart);
    if (ctx) {
      const devices = Object.keys(deviceStats.value.device.counts);
      const counts = Object.values(deviceStats.value.device.counts);

      try {
        charts.value.deviceType = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: devices,
            datasets: [{
              data: counts,
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
              legend: {
                position: 'bottom',
              },
              tooltip: {
                callbacks: {
                  label: (context) => {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    const percentage = deviceStats.value.device.percentage[devices[context.dataIndex]];
                    return `${label}: ${value} (${percentage}%)`;
                  }
                }
              }
            }
          }
        });
      } catch (e) {
        console.error('기기 유형 차트 생성 중 오류:', e);
      }
    }
  }

  // 브라우저 차트
  if (deviceStats.value && browserChart.value) {
    const ctx = getCanvasContext(browserChart);
    if (ctx) {
      const browsers = Object.keys(deviceStats.value.browser.counts);
      const counts = Object.values(deviceStats.value.browser.counts);

      try {
        charts.value.browser = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: browsers,
            datasets: [{
              data: counts,
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
              legend: {
                position: 'bottom',
              },
              tooltip: {
                callbacks: {
                  label: (context) => {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    const percentage = deviceStats.value.browser.percentage[browsers[context.dataIndex]];
                    return `${label}: ${value} (${percentage}%)`;
                  }
                }
              }
            }
          }
        });
      } catch (e) {
        console.error('브라우저 차트 생성 중 오류:', e);
      }
    }
  }

  // OS 차트
  if (deviceStats.value && osChart.value) {
    const ctx = getCanvasContext(osChart);
    if (ctx) {
      const oses = Object.keys(deviceStats.value.os.counts);
      const counts = Object.values(deviceStats.value.os.counts);

      try {
        charts.value.os = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: oses,
            datasets: [{
              data: counts,
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
              legend: {
                position: 'bottom',
              },
              tooltip: {
                callbacks: {
                  label: (context) => {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    const percentage = deviceStats.value.os.percentage[oses[context.dataIndex]];
                    return `${label}: ${value} (${percentage}%)`;
                  }
                }
              }
            }
          }
        });
      } catch (e) {
        console.error('OS 차트 생성 중 오류:', e);
      }
    }
  }

  // 시간대별 스캔 차트
  if (timeStats.value && hourlyChart.value) {
    const ctx = getCanvasContext(hourlyChart);
    if (ctx) {
      const hours = Object.keys(timeStats.value.hourly);
      const counts = Object.values(timeStats.value.hourly);

      try {
        charts.value.hourly = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: hours.map(h => `${h}시`),
            datasets: [{
              label: '스캔 횟수',
              data: counts,
              backgroundColor: '#36A2EB'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      } catch (e) {
        console.error('시간대별 스캔 차트 생성 중 오류:', e);
      }
    }
  }

  // 요일별 스캔 차트
  if (timeStats.value && weekdayChart.value) {
    const ctx = getCanvasContext(weekdayChart);
    if (ctx) {
      const weekdays = Object.keys(timeStats.value.weekday);
      const counts = Object.values(timeStats.value.weekday);

      // 요일 한글화
      const weekdayNames = {
        'Monday': '월요일',
        'Tuesday': '화요일',
        'Wednesday': '수요일',
        'Thursday': '목요일',
        'Friday': '금요일',
        'Saturday': '토요일',
        'Sunday': '일요일'
      };

      try {
        charts.value.weekday = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: weekdays.map(day => weekdayNames[day] || day),
            datasets: [{
              label: '스캔 횟수',
              data: counts,
              backgroundColor: '#4BC0C0'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      } catch (e) {
        console.error('요일별 스캔 차트 생성 중 오류:', e);
      }
    }
  }

  // 일별 승인 횟수 차트
  if (exchangeStats.value && approvalCountsChart.value) {
    const ctx = getCanvasContext(approvalCountsChart);
    if (ctx) {
      try {
        charts.value.approvalCounts = new Chart(ctx, {
          type: 'line',
          data: {
            labels: exchangeStats.value.daily.dates,
            datasets: [{
              label: '승인 횟수',
              data: exchangeStats.value.daily.counts,
              borderColor: '#FF6384',
              backgroundColor: 'rgba(255, 99, 132, 0.2)',
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      } catch (e) {
        console.error('일별 승인 횟수 차트 생성 중 오류:', e);
      }
    }
  }

  // 승인자별 승인 횟수 차트
  if (exchangeStats.value && approverCountsChart.value) {
    const ctx = getCanvasContext(approverCountsChart);
    if (ctx) {
      const approvers = Object.keys(exchangeStats.value.approver.counts);
      const counts = Object.values(exchangeStats.value.approver.counts);

      try {
        charts.value.approverCounts = new Chart(ctx, {
          type: 'pie',
          data: {
            labels: approvers,
            datasets: [{
              data: counts,
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
              legend: {
                position: 'bottom',
              },
              tooltip: {
                callbacks: {
                  label: (context) => {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    const percentage = exchangeStats.value.approver.percentage[approvers[context.dataIndex]];
                    return `${label}: ${value} (${percentage}%)`;
                  }
                }
              }
            }
          }
        });
      } catch (e) {
        console.error('승인자별 승인 횟수 차트 생성 중 오류:', e);
      }
    }
  }

  // 시간대별 승인 횟수 차트
  if (exchangeStats.value && hourlyApprovalChart.value) {
    const ctx = getCanvasContext(hourlyApprovalChart);
    if (ctx) {
      const hours = Object.keys(exchangeStats.value.hourly);
      const counts = Object.values(exchangeStats.value.hourly);

      try {
        charts.value.hourlyApproval = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: hours.map(h => `${h}시`),
            datasets: [{
              label: '승인 횟수',
              data: counts,
              backgroundColor: '#9966FF'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      } catch (e) {
        console.error('시간대별 승인 횟수 차트 생성 중 오류:', e);
      }
    }
  }

  // 일별 QR 활동 차트 (기본 현황 섹션에 추가)
  if (dailyChartData.value && dailyActivityChartRef.value) {
    const ctx = dailyActivityChartRef.value.getContext('2d');
    if (ctx) {
      const labels = dailyChartData.value.dates || [];
      const createdCounts = dailyChartData.value.createdCounts || [];
      const scannedCounts = dailyChartData.value.scannedCounts || [];

      if (charts.value.dailyActivityChart) {
        charts.value.dailyActivityChart.destroy();
      }

      charts.value.dailyActivityChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [
            {
              label: '일별 생성 QR 수',
              data: createdCounts,
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              fill: false,
              tension: 0.1
            },
            {
              label: '일별 스캔 수',
              data: scannedCounts,
              borderColor: 'rgb(255, 99, 132)',
              backgroundColor: 'rgba(255, 99, 132, 0.2)',
              fill: false,
              tension: 0.1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              // 정수만 표시하도록 설정 (선택 사항)
              ticks: {
                precision: 0 
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
            },
            tooltip: {
              mode: 'index',
              intersect: false,
            }
          }
        }
      });
    } else {
      console.error("일별 QR 활동 차트 컨텍스트를 가져올 수 없습니다.");
    }
  } else {
    console.warn("일별 QR 활동 차트 렌더링 조건 미충족: 데이터 또는 ref 없음", dailyChartData.value, dailyActivityChartRef.value);
  }

};

// 데이터 로드 상태 추적
const dataLoaded = ref(false);
// DOM 렌더링 완료 상태
const domReady = ref(false);

// 지도 표시 여부를 결정하는 computed 속성
const shouldShowMap = computed(() => scannedLocationDataExists.value && cardVisibility.value.location);

// 컴포넌트 마운트 시 통계 데이터 로드
onMounted(() => {
  // SUPER_ADMIN이거나 현재 프로젝트가 선택되어 있을 때만 통계 로드
  if (canViewStatistics.value) {
    loadAllStatistics();
  } else {
    isLoading.value = false;
  }

  // DOM이 완전히 렌더링된 후에 domReady를 true로 설정
  setTimeout(() => {
    domReady.value = true;
  }, 500);
  // 기존 onMounted 내 지도 초기화 로직은 shouldShowMap watcher의 immediate 실행으로 대체됨
});

// 차트 컨테이너 준비 함수
const prepareChartContainers = () => {
  // 타입별 분포 차트 컨테이너 확인 및 준비
  const typeChartContainer = document.getElementById('typeDistributionChartContainer');
  if (typeChartContainer) {
    // 기존 캔버스 제거
    typeChartContainer.innerHTML = '';

    // 새 캔버스 생성
    const canvas = document.createElement('canvas');
    canvas.id = 'typeDistributionCanvas';
    canvas.width = typeChartContainer.clientWidth || 300;
    canvas.height = 280;

    // 컨테이너에 추가
    typeChartContainer.appendChild(canvas);
  }

  // 상태별 분포 차트 컨테이너 확인 및 준비
  const statusChartContainer = document.getElementById('statusDistributionChartContainer');
  if (statusChartContainer) {
    // 기존 캔버스 제거
    statusChartContainer.innerHTML = '';

    // 새 캔버스 생성
    const canvas = document.createElement('canvas');
    canvas.id = 'statusDistributionCanvas';
    canvas.width = statusChartContainer.clientWidth || 300;
    canvas.height = 280;

    // 컨테이너에 추가
    statusChartContainer.appendChild(canvas);
  }
};

// 차트 렌더링 조건이 모두 충족되었는지 감시
watch([dataLoaded, domReady], ([dataLoadedValue, domReadyValue]) => {
  // 데이터가 로드되고 DOM이 준비된 경우에만 차트 렌더링
  if (dataLoadedValue && domReadyValue) {
    // 데이터 유효성 검사
    const hasData = basicStats.value || // basicStats도 유효성 검사에 포함
                    dailyChartData.value || // dailyChartData도 유효성 검사에 포함
                    typeDistribution.value || statusDistribution.value ||
                    deviceStats.value || timeStats.value || exchangeStats.value ||
                    (scannedQrCodes.value && scannedQrCodes.value.length > 0); // scannedQrCodes 유효성 검사 추가

    if (!hasData) {
      console.warn('유효한 통계 데이터가 없습니다.');
      isLoading.value = false;
      return;
    }

    // 차트 렌더링 시작
    isChartLoading.value = true; // 차트 로딩 상태 활성화

    // DOM이 완전히 업데이트될 때까지 기다린 후 차트 렌더링
    setTimeout(() => {
      // 직접 DOM 요소 확인 및 생성
      prepareChartContainers();

      // 추가 지연으로 DOM 업데이트 보장
      setTimeout(() => {
        try {
          // DOM 요소 다시 확인
          const typeCanvas = document.getElementById('typeDistributionCanvas');
          const statusCanvas = document.getElementById('statusDistributionCanvas');

          if (!typeCanvas || !statusCanvas) {
            console.warn('일부 차트 캔버스를 찾을 수 없습니다. 다시 생성합니다.');
            prepareChartContainers();
          }

          // 차트 렌더링
          renderCharts();
        } catch (error) {
          console.error('차트 렌더링 중 오류 발생:', error);
        } finally {
          // 로딩 상태 해제 (성공/실패 상관없이)
          isChartLoading.value = false;
        }
      }, 500);
    }, 500);
  }
});

// 프로젝트 변경 시 통계 데이터 다시 로드
watch(currentProjectId, () => {
  dataLoaded.value = false;
  // SUPER_ADMIN이거나 현재 프로젝트가 있을 때만 통계 로드
  if (canViewStatistics.value) {
    loadAllStatistics();
  } else {
    isLoading.value = false;
  }
});

// 맨 위로 스크롤하는 함수
const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 맨 아래로 스크롤하는 함수
const scrollToBottom = () => {
  window.scrollTo({ top: document.documentElement.scrollHeight, behavior: 'smooth' });
};

// 지도 컨테이너 ref
const mapContainerRef = ref(null);
// 카카오맵 인스턴스 (반응성 제거)
let mapInstance = null;
// 마커 클러스터러 인스턴스 (반응성 제거)
let clustererInstance = null;
// 정보창 인스턴스 (반응성 제거)
let infowindow = null;

// initMap 재시도 카운터 및 최대 횟수
let initMapRetries = 0;
const MAX_INIT_MAP_RETRIES = 10; // 예: 최대 10번 (약 5초간) 재시도

// 카카오맵 초기화 함수
const initMap = () => {
  if (window.kakao && window.kakao.maps && mapContainerRef.value) {
    initMapRetries = 0; // 성공 시 재시도 카운터 리셋
    const container = mapContainerRef.value;
    const options = {
      center: new window.kakao.maps.LatLng(37.566826, 126.9786567), // 기본 중심 좌표 (예: 서울시청)
      level: 7, // 기본 확대 레벨
    };
    try {
      mapInstance = new window.kakao.maps.Map(container, options); // .value 제거

      // mapInstance가 유효한 Map 객체인지 확인
      if (!mapInstance || typeof mapInstance.getCenter !== 'function') { // .value 제거
        console.error("카카오맵 객체 생성 실패 또는 유효하지 않은 객체가 반환되었습니다.");
        if (initMapRetries < MAX_INIT_MAP_RETRIES) {
          initMapRetries++;
          setTimeout(initMap, 500);
        } else {
          console.error("맵 객체 생성 실패 후 최대 재시도 도달.");
        }
        return;
      }

      // 마커 클러스터러 초기화 (옵션 최소화)
      clustererInstance = new window.kakao.maps.MarkerClusterer({ // .value 제거
        map: mapInstance, // .value 제거
        averageCenter: true,
        minLevel: 5, // 줌 레벨 3까지 클러스터가 보이도록 수정 (기존 6)
        // gridSize: 60, // 필요시 옵션 복원
        // texts: (count) => String(count), // 필요시 옵션 복원
        // styles: [ // 필요시 옵션 복원
        //   { width: '30px', height: '30px', background: 'rgba(255, 80, 80, .8)', borderRadius: '15px', color: '#fff', textAlign: 'center', fontWeight: 'bold', lineHeight: '30px' },
        //   { width: '40px', height: '40px', background: 'rgba(255, 150, 0, .8)', borderRadius: '20px', color: '#fff', textAlign: 'center', fontWeight: 'bold', lineHeight: '40px' },
        //   { width: '50px', height: '50px', background: 'rgba(0, 128, 255, .8)', borderRadius: '25px', color: '#fff', textAlign: 'center', fontWeight: 'bold', lineHeight: '50px' }
        // ]
      });

      displayMarkers();
    } catch (error) {
      console.error("카카오맵 또는 클러스터러 초기화 중 예외 발생:", error);
    }
  } else {
    let errorMessage = "카카오맵 초기화 조건 미충족: ";
    if (!window.kakao || !window.kakao.maps) errorMessage += "SDK 미로드 상태. ";
    if (!mapContainerRef.value) errorMessage += "맵 컨테이너 없음. ";
    console.error(errorMessage, "mapContainerRef.value 상태:", mapContainerRef.value);

    initMapRetries++;
    if (initMapRetries < MAX_INIT_MAP_RETRIES) {
      setTimeout(initMap, 500);
    } else {
      console.error("카카오맵 초기화 최대 재시도 횟수 도달. 초기화를 중단합니다.");
    }
  }
};

// 마커들을 저장할 배열
const markers = ref([]); // 이 배열은 마커 객체 자체를 담고 있으므로, 필요시 markRaw 처리 고려 가능

// 지도에 마커 표시 함수
const displayMarkers = async () => {
  if (!mapInstance || typeof mapInstance.getCenter !== 'function' || 
      !clustererInstance || typeof clustererInstance.clear !== 'function') { // .value 제거
    return;
  }

  clustererInstance.clear(); // .value 제거

  markers.value = [];

  if (!scannedQrCodes.value || !Array.isArray(scannedQrCodes.value) || scannedQrCodes.value.length === 0) {
    mapInstance.setCenter(new window.kakao.maps.LatLng(37.566826, 126.9786567)); // .value 제거
    mapInstance.setLevel(7); // .value 제거
    return;
  }

  const newMarkersForClusterer = [];
  const bounds = new window.kakao.maps.LatLngBounds();
  let validMarkerExists = false;

  scannedQrCodes.value.forEach(qrCode => {
    if (qrCode && qrCode.installationLocationLat != null && qrCode.installationLocationLng != null) { 
      const lat = parseFloat(qrCode.installationLocationLat);
      const lng = parseFloat(qrCode.installationLocationLng);

      if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        const markerPosition = new window.kakao.maps.LatLng(lat, lng);
        const marker = new window.kakao.maps.Marker({
          position: markerPosition,
        });
        newMarkersForClusterer.push(marker);
        markers.value.push(marker);
        bounds.extend(markerPosition);
        validMarkerExists = true;

        // 마커 클릭 시 정보창 표시 이벤트 리스너 추가
        window.kakao.maps.event.addListener(marker, 'click', function() {
          if (!infowindow) { // 정보창 객체가 없으면 생성
            infowindow = new window.kakao.maps.InfoWindow({
              removable: true, // 사용자가 닫을 수 있도록 설정
              content: '' // 내용은 동적으로 설정
            });
          }
          // 정보창 내용 설정
          const content = `<div style="padding:5px;font-size:12px;min-width:150px;">${'QR 코드 이름: ' + qrCode.qrName || '이름 없음'}</div>`;
          infowindow.setContent(content);
          // 정보창을 마커 위에 표시
          infowindow.open(mapInstance, marker);
        });
      } else {
        console.warn(`잘못된 위경도 데이터 또는 범위 벗어남: QR ${qrCode.qrName || '이름없음'}, Lat ${qrCode.installationLocationLat}, Lng ${qrCode.installationLocationLng}`);
      }
    } else {
       console.warn(`누락된 위경도 데이터 또는 유효하지 않은 QR 코드 객체: ${JSON.stringify(qrCode)}`);
    }
  });

  if (newMarkersForClusterer.length > 0) {
    await nextTick();
    clustererInstance.addMarkers(newMarkersForClusterer); // .value 제거
  } 

  if (validMarkerExists && !bounds.isEmpty()) {
    await nextTick();
    mapInstance.setBounds(bounds); // .value 제거
    if (mapInstance.getLevel() > 10) { // .value 제거
        mapInstance.setLevel(10); // .value 제거
    }
  } else if (scannedQrCodes.value.length > 0 && newMarkersForClusterer.length === 0) {
    mapInstance.setCenter(new window.kakao.maps.LatLng(37.566826, 126.9786567)); // .value 제거
    mapInstance.setLevel(7); // .value 제거
  }
};

// scannedQrCodes 데이터가 변경되면 마커 업데이트 (이 watch는 유지)
watch(scannedQrCodes, (newCodes, oldCodes) => {
  if (mapInstance && typeof mapInstance.getCenter === 'function' && 
      clustererInstance && typeof clustererInstance.clear === 'function' && 
      cardVisibility.value.location) { // cardVisibility 대신 shouldShowMap.value를 사용하는 것이 더 정확할 수 있음
    displayMarkers();
  }
}, { deep: true });

// shouldShowMap 값 변경에 따라 지도 관리 (핵심 로직)
watch(shouldShowMap, (canShowMap) => {
  if (canShowMap) {
    nextTick(() => {
      if (!mapContainerRef.value) {
        console.error('[Watch shouldShowMap - TRUE] mapContainerRef is null/undefined after nextTick. Cannot init map.');
        return;
      }
      initMapRetries = 0; // initMap 호출 전 재시도 카운터 리셋
      initMap(); // initMap은 내부적으로 mapInstance, clustererInstance를 생성하고 displayMarkers를 호출
    });
  } else {
    mapInstance = null;
    clustererInstance = null;
    // markers.value = []; // 마커 배열도 비우는 것을 고려할 수 있으나, displayMarkers에서 이미 처리함
  }
}, { immediate: true }); // immediate: true 로 초기 로드 시에도 실행

// QR 코드 목록 페이징 관련 상태
const currentPage = ref(1);
const itemsPerPage = ref(10);
const itemsPerPageOptions = [10, 20, 50];

// 현재 페이지에 표시될 QR 코드 목록 (페이징 처리)
const paginatedQrCodes = computed(() => {
  if (!scannedQrCodes.value || scannedQrCodes.value.length === 0) {
    return [];
  }
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return scannedQrCodes.value.slice(start, end);
});

// 총 페이지 수 계산
const totalPages = computed(() => {
  if (!scannedQrCodes.value || scannedQrCodes.value.length === 0) {
    return 1;
  }
  return Math.ceil(scannedQrCodes.value.length / itemsPerPage.value);
});

// 특정 페이지로 이동하는 함수
const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

// 다음 페이지로 이동
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 이전 페이지로 이동
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

// itemsPerPage 변경 시 currentPage를 1로 리셋
watch(itemsPerPage, () => {
  currentPage.value = 1;
});

// 지도에서 QR 코드 위치로 이동하는 함수
const panToMapLocation = (qrCode) => {
  if (mapInstance && typeof mapInstance.panTo === 'function') { // panTo 메소드 존재 확인
    if (qrCode && qrCode.installationLocationLat != null && qrCode.installationLocationLng != null) {
      const lat = parseFloat(qrCode.installationLocationLat);
      const lng = parseFloat(qrCode.installationLocationLng);

      if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        const targetPosition = new window.kakao.maps.LatLng(lat, lng);
        mapInstance.panTo(targetPosition); // 부드럽게 이동
        // mapInstance.setLevel(4, {animate: true}); // 적절한 레벨로 확대 (애니메이션과 함께, 선택 사항)
        
        // 해당 위치의 마커 정보창을 열어주는 로직 (선택 사항 - 구현하려면 마커 인스턴스 접근 방법 필요)
        // 예: findMarkerAndOpenInfoWindow(lat, lng, qrCode.qrName);

      } else {
        console.warn("클릭된 항목의 위경도 값이 유효하지 않습니다:", qrCode);
        // 사용자에게 알림 (예: Toast 메시지)
      }
    } else {
      console.warn("클릭된 항목에 위경도 정보가 없습니다:", qrCode);
    }
  } else {
    console.warn("맵 인스턴스가 유효하지 않아 위치로 이동할 수 없습니다.");
  }
};

</script>

<style scoped>
.project-select-message {
  margin-top: 30px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.message-container {
  padding: 40px 20px;
}

.message-container p {
  font-size: 18px;
  color: #666;
  margin: 0;
}

.statistics-view {
  padding: 20px;
  /* height: 100vh; */ /* 내부 스크롤 유발 속성 제거 */
  /* overflow-y: auto; */ /* 내부 스크롤 유발 속성 제거 */
  overflow-y: visible; /* 내부 스크롤바 명시적으로 비활성화 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px; /* 헤더와 내용 사이 간격 */
}

.toggle-button {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85em;
}

.toggle-button:hover {
  background-color: #e0e0e0;
}

.card-content {
  /* 필요하다면 여기에 카드 내용 부분에 대한 추가 스타일을 적용할 수 있습니다. */
  /* 예를 들어, 접히고 펼쳐질 때 부드러운 애니메이션 효과를 추가할 수 있습니다. */
  /* transition: max-height 0.3s ease-out; */
  /* overflow: hidden; */
}

h1 {
  margin-bottom: 20px;
  color: #333;
}

/* 기본 통계 카드 스타일 (클래스명 변경하여 중복 방지) */
.basic-stats-card-readded {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-left: 5px solid #4CAF50; /* 강조색 */
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.basic-stats-card-readded h2 {
  color: #4CAF50;
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eeeeee;
  font-size: 1.2em;
}

.summary-stats-readded {
  display: flex;
  flex-wrap: wrap;
  gap: 15px; /* 항목 간 간격 */
}

.summary-item-readded {
  background-color: #f9f9f9;
  padding: 10px 15px;
  border-radius: 4px;
  flex: 1 1 180px; /* flex-grow, flex-shrink, flex-basis */
  text-align: center;
  min-width: 150px; /* 최소 너비 보장 */
}

.label-readded {
  display: block;
  font-size: 0.85em;
  color: #555555;
  margin-bottom: 5px;
}

.value-readded {
  font-size: 1.3em;
  font-weight: bold;
  color: #333333;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
}

.loading-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-container p {
  font-size: 1.2rem;
  color: #333;
  font-weight: 500;
}

.loading-progress {
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
}

.chart-loading {
  background-color: rgba(255, 255, 255, 0.95);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  padding: 20px;
  background-color: #ffebee;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.error-message {
  color: #d32f2f;
  margin-bottom: 15px;
}

.retry-button {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.statistics-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.stat-card h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-wrapper {
  height: 300px;
  position: relative;
  width: 100%;
  margin-bottom: 20px;
  display: block;
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
}

.chart-wrapper canvas {
  display: block !important;
  height: 100% !important;
  width: 100% !important;
  max-height: 280px !important;
}

.charts-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.chart-column {
  flex: 1;
  min-width: 300px;
}

.chart-column.wide {
  flex: 2;
  min-width: 500px;
}

.chart-column h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
  font-size: 1.2rem;
  text-align: center;
}

.data-table {
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  margin-top: 15px;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
  position: sticky;
  top: 0;
}

.data-table tr:hover {
  background-color: #f9f9f9;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.no-data-cell {
  text-align: center;
  color: #888;
  font-style: italic;
  padding: 20px !important;
}

/* 기존 테이블 스타일 유지 (다른 테이블에 영향을 주지 않기 위함) */
table:not(.data-table table) {
  width: 100%;
  border-collapse: collapse;
}

th:not(.data-table th), td:not(.data-table td) {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th:not(.data-table th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

.no-data {
  padding: 40px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  border: 1px dashed #ddd;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #ccc;
}

.no-data-text {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.no-data-subtext {
  font-size: 0.9rem;
  color: #999;
  max-width: 80%;
  line-height: 1.4;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 20px;
}

.chart-placeholder .mini-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

.summary-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.summary-item {
  flex: 1;
  min-width: 200px;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-item .label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.summary-item .value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

@media (max-width: 768px) {
  .chart-column, .chart-column.wide {
    flex: 100%;
    min-width: 100%;
  }

  .summary-item {
    min-width: 100%;
  }
}

.scroll-buttons {
  position: fixed;
  bottom: 30px; /* 화면 하단과의 간격 */
  right: 30px; /* 화면 우측과의 간격 */
  display: flex;
  flex-direction: column; /* 버튼들을 세로로 정렬 */
  gap: 10px; /* 버튼 사이의 간격 */
  z-index: 1000; /* 다른 요소들 위에 표시되도록 z-index 설정 */
}

.scroll-button {
  background-color: #555; /* 버튼 배경색 */
  color: white; /* 버튼 글자색 */
  border: none;
  border-radius: 50%; /* 원형 버튼 */
  width: 50px; /* 버튼 너비 */
  height: 50px; /* 버튼 높이 */
  font-size: 1.5em; /* 아이콘 크기 */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2); /* 그림자 효과 */
  transition: background-color 0.3s ease; /* 부드러운 색상 변경 효과 */
}

.scroll-button:hover {
  background-color: #333; /* 호버 시 버튼 배경색 변경 */
}

/* top-button과 bottom-button에 대한 개별 마진은 scroll-buttons의 gap으로 대체되었으므로 제거하거나 조정할 수 있습니다. */
/* .top-button { */
/*   margin-bottom: 10px; */
/* } */

/* .bottom-button { */
  /* background-color: #555; */ /* 삭제 또는 전체 주석 처리 */
/* } */

.top-button {
  margin-bottom: 10px;
}

.bottom-button {
  margin-top: 10px;
}

.qr-list-controls-and-table {
  margin-top: 20px;
}

.list-controls {
  display: flex;
  justify-content: flex-end; /* 오른쪽 정렬 */
  align-items: center;
  margin-bottom: 12px;
}

.items-per-page-selector label {
  margin-right: 8px;
  font-size: 0.9em;
  color: #555;
}

.items-per-page-selector select {
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  padding: 10px 0;
  border-top: 1px solid #eee; /* 구분선 추가 */
}

.pagination-controls button {
  background-color: #fff;
  border: 1px solid #ccc;
  color: #333;
  padding: 8px 15px;
  margin: 0 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  font-size: 0.9em;
}

.pagination-controls button:hover:not(:disabled) {
  background-color: #e9e9e9;
  border-color: #bbb;
}

.pagination-controls button:disabled {
  background-color: #f8f8f8;
  color: #aaa;
  cursor: not-allowed;
  border-color: #eee;
}

.pagination-controls span {
  margin: 0 12px;
  font-size: 0.95em;
  color: #333;
  font-weight: 500;
}

.qr-list-row {
  cursor: pointer; /* 클릭 가능함을 시각적으로 표시 */
}

.qr-list-row:hover {
  background-color: #f0f0f0; /* 호버 효과 */
}

/* 날짜 필터링 UI 스타일 */
.date-filter-container {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap; /* 작은 화면에서 줄바꿈 허용 */
}

.date-inputs {
  display: flex;
  gap: 15px;
  flex-wrap: wrap; /* 작은 화면에서 줄바꿈 허용 */
}

.date-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input-group label {
  font-size: 0.9em;
  color: #333;
}

.date-input-group input[type="date"] {
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover:not(:disabled) {
  background-color: #0b7dda;
}

.search-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.date-error-message {
  color: #d32f2f; /* Material Design 에러 색상 */
  font-size: 0.85em;
  margin-top: 5px;
  width: 100%; /* 에러 메시지가 필터 컨테이너 전체 너비를 차지하도록 */
  text-align: left; /* 에러 메시지 왼쪽 정렬 */
}
</style>
