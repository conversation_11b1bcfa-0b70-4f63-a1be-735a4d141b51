import axios from 'axios';

// 인증이 필요 없는 공개 API 호출을 위한 Axios 인스턴스
const publicApiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, // 환경 변수에서 baseURL 읽어오기
  headers: {
    'Content-Type': 'application/json',
  },
  // withCredentials 옵션을 false로 설정하여 인증 정보를 보내지 않음
  withCredentials: false,
});

// 인터셉터 설정 - 인증 관련 오류 무시
publicApiClient.interceptors.response.use(
  (response) => response, // 성공 응답
  (error) => {
    return Promise.reject(error);
  }
);

export default publicApiClient;
