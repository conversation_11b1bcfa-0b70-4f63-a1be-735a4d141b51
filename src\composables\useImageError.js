import { ref } from 'vue';
import noImageUrl from '@/assets/image/no-image.png';

/**
 * 이미지 에러 핸들링을 위한 컴포저블
 * 이미지 로딩 실패 시 기본 'no-image' 이미지로 대체
 */
export function useImageError() {
  const imageLoadError = ref(false);

  /**
   * 이미지 에러 핸들러
   * @param {Event} event - 이미지 에러 이벤트
   */
  const handleImageError = (event) => {
    const img = event.target;

    // 이미 no-image로 변경된 경우 무한 루프 방지
    if (img.src.includes('no-image.png')) {
      console.error('no-image.png 파일도 로드할 수 없습니다.');
      return;
    }

    console.error('이미지 로드 실패:', img.src);
    imageLoadError.value = true;

    // 원본 이미지의 크기 관련 스타일 보존
    const computedStyle = window.getComputedStyle(img);
    const originalWidth = img.style.width || computedStyle.width;
    const originalHeight = img.style.height || computedStyle.height;
    const originalMaxWidth = img.style.maxWidth || computedStyle.maxWidth;
    const originalMaxHeight = img.style.maxHeight || computedStyle.maxHeight;

    // no-image 이미지로 대체
    img.src = noImageUrl;
    img.alt = '이미지를 불러올 수 없습니다';

    // 크기 관련 스타일 적용 (원본 스타일이 있는 경우에만)
    if (originalMaxWidth && originalMaxWidth !== 'none') {
      img.style.maxWidth = originalMaxWidth;
    }
    if (originalMaxHeight && originalMaxHeight !== 'none') {
      img.style.maxHeight = originalMaxHeight;
    }

    // object-fit 스타일 적용 (이미지 비율 유지)
    if (!img.style.objectFit) {
      img.style.objectFit = 'contain';
    }

    img.style.overflow = 'hidden';
  };

  /**
   * 이미지 에러 상태 초기화
   */
  const resetImageError = () => {
    imageLoadError.value = false;
  };

  /**
   * 이미지 src 속성에 에러 핸들러를 바인딩하는 헬퍼 함수
   * @param {string} originalSrc - 원본 이미지 URL
   * @returns {object} - 이미지 속성 객체
   */
  const getImageProps = (originalSrc) => {
    return {
      src: originalSrc,
      onError: handleImageError
    };
  };

  return {
    imageLoadError,
    handleImageError,
    resetImageError,
    getImageProps,
    noImageUrl
  };
}
