import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * QR 코드 스캔으로 참가자 혜택 정보를 조회합니다.
 * @param {string} confirmationCode - QR 코드에서 스캔한 확인 코드
 * @returns {Promise<object>} 참가자 혜택 정보 결과
 */
export const getAttendeeRedemptionDetails = async (confirmationCode) => {
  try {
    // 확인 코드가 없는 경우 에러 발생
    if (!confirmationCode) {
      throw new Error('확인 코드가 필요합니다.');
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    return await executeApiCall(
      () => apiClient.get(`/admin/attendee-redemption/details/${confirmationCode}`),
      '참가자 혜택 정보를 가져오는데 실패했습니다.'
    );
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '참가자 혜택 정보를 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 참가자의 특정 혜택을 사용 처리합니다.
 * @param {string} confirmationCode - 참가자 확인 코드
 * @param {number} benefitId - 혜택 ID
 * @returns {Promise<object>} 혜택 사용 처리 결과
 */
export const redeemBenefit = async (confirmationCode, benefitId) => {
  try {
    // 확인 코드나 혜택 ID가 없는 경우 에러 발생
    if (!confirmationCode) {
      throw new Error('확인 코드가 필요합니다.');
    }

    if (!benefitId) {
      throw new Error('혜택 ID가 필요합니다.');
    }

    // 요청 본문 생성
    const requestBody = {
      confirmationCode,
      benefitId
    };

    // 공통 API 호출 함수를 사용하여 요청 처리
    return await executeApiCall(
      () => apiClient.post('/admin/attendee-redemption/redeem', requestBody),
      '혜택 사용 처리에 실패했습니다.'
    );
  } catch (error) {
    // 특정 오류 코드에 대한 처리
    if (error.response && error.response.data) {
      const { data } = error.response;

      // 서버 응답 구조 확인
      if (data.success === false && data.error) {
        // 재고 부족 오류
        if (data.error.code === 'B014') {
          throw new Error(data.error.message || '재고가 부족하여 처리할 수 없습니다.');
        }

        // 이미 사용된 혜택 오류
        if (data.error.code === 'B009') {
          throw new Error(data.error.message || '이미 사용 처리된 혜택입니다.');
        }

        // 기타 오류 코드가 있는 경우
        if (data.error.message) {
          throw new Error(data.error.message);
        }
      }
    }

    // 기타 오류 처리
    const errorMessage = handleApiError(error, '혜택 사용 처리 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * QR 코드 스캔을 통해 참석자 참석 처리를 합니다.
 * @param {string} confirmationCode - QR 코드에서 스캔한 확인 코드
 * @returns {Promise<object>} 참석 처리 결과
 */
export const processAttendance = async (confirmationCode) => {
  try {
    // 확인 코드가 없는 경우 에러 발생
    if (!confirmationCode) {
      throw new Error('확인 코드가 필요합니다.');
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    return await executeApiCall(
      () => apiClient.put(`/admin/attendees/attend/${confirmationCode}`),
      '참석 처리에 실패했습니다.'
    );
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '참석 처리 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 이벤트의 참석자 목록을 가져옵니다.
 * @param {number} eventId - 이벤트 ID
 * @param {object} params - 페이지네이션 및 정렬 파라미터
 * @returns {Promise<object>} 참석자 목록 및 이벤트 정보
 */
export const getEventAttendees = async (eventId, params = {}) => {
  try {
    // 이벤트 ID가 없는 경우 에러 발생
    if (!eventId) {
      throw new Error('이벤트 ID가 필요합니다.');
    }

    const defaultParams = {
      page: 0,
      size: 10,
      sort: 'createDate,desc'
    };

    // 기본 파라미터와 사용자 지정 파라미터 병합
    const requestParams = { ...defaultParams, ...params };

    // 공통 API 호출 함수를 사용하여 요청 처리
    return await executeApiCall(
      () => apiClient.get(`/events/${eventId}/attendees`, { params: requestParams }),
      '참석자 목록을 가져오는 데 실패했습니다.'
    );
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '참석자 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * SUPER_ADMIN용 모든 참가자 목록을 가져옵니다.
 * @param {object} params - 페이지네이션 및 정렬 파라미터
 * @param {number} [params.projectId] - 선택적 프로젝트 ID (특정 프로젝트로 필터링하는 경우)
 * @param {number} [params.eventId] - 선택적 이벤트 ID (특정 이벤트로 필터링하는 경우)
 * @param {number} [params.formId] - 선택적 폼 ID (특정 폼으로 필터링하는 경우)
 * @returns {Promise<object>} 참가자 목록을 포함한 응답 객체 Promise
 */
export const getAllAttendees = async (params = {}) => {
  try {
    const defaultParams = {
      page: 0,
      size: 10,
      sort: 'registrationDate,desc'
    };

    // 기본 파라미터와 사용자 지정 파라미터 병합
    const requestParams = { ...defaultParams, ...params };

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/super/attendees/all`, { params: requestParams }),
      '참가자 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '참가자 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 참가자의 특정 혜택 사용 승인을 취소합니다.
 * @param {string} confirmationCode - 참가자 확인 코드
 * @param {number} benefitId - 혜택 ID
 * @returns {Promise<object>} 혜택 사용 취소 결과
 */
export const cancelBenefitRedemption = async (confirmationCode, benefitId) => {
  try {
    // 확인 코드나 혜택 ID가 없는 경우 에러 발생
    if (!confirmationCode) {
      throw new Error('확인 코드가 필요합니다.');
    }

    if (!benefitId) {
      throw new Error('혜택 ID가 필요합니다.');
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    return await executeApiCall(
      () => apiClient.delete(`/admin/attendee-redemption/cancel/${confirmationCode}/${benefitId}`),
      '혜택 사용 취소에 실패했습니다.'
    );
  } catch (error) {
    // 특정 오류 코드에 대한 처리
    if (error.response && error.response.data) {
      const { data } = error.response;

      // 서버 응답 구조 확인
      if (data.success === false && data.error) {
        // 참석자를 찾을 수 없는 경우
        if (data.error.code === 'A101') {
          throw new Error(data.error.message || '참석자를 찾을 수 없습니다.');
        }

        // 기타 오류 코드가 있는 경우
        if (data.error.message) {
          throw new Error(data.error.message);
        }
      }
    }

    // 기타 오류 처리
    const errorMessage = handleApiError(error, '혜택 사용 취소 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};