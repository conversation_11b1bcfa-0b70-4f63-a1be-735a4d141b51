<template>
  <div class="property-panel">
    <!-- 캔버스 속성 편집 모드 -->
    <div v-if="!selectedElement || showCanvasProperties">
      <div v-if="showCanvasProperties && selectedElement" class="panel-header">
        <button class="back-button" @click="showCanvasProperties = false">
          <span class="back-icon">&larr;</span> 요소 속성으로 돌아가기
        </button>
      </div>
      <CanvasProperties />
    </div>

    <!-- 요소 속성 편집 모드 -->
    <template v-else>
      <div class="panel-header">
        <h3>{{ getElementTitle(selectedElement.type) }} 속성</h3>
        <div class="header-actions">
          <button class="canvas-button" @click="showCanvasProperties = true">
            <span class="canvas-icon">📷</span> 캔버스 속성
          </button>
          <button class="delete-button" @click="deleteElement">삭제</button>
        </div>
      </div>

      <!-- 공통 속성 -->
      <div class="property-section">
        <h4>위치 및 크기</h4>

        <div class="position-controls">
          <div class="property-row position-row">
            <label>X 좌표:</label>
            <div class="input-with-unit">
              <input
                type="number"
                :value="selectedElement.position.x.value"
                @input="updatePosition('x', $event.target.value)"
              />
              <select
                class="unit-select"
                :value="selectedElement.position.x.unit"
                @change="updatePositionUnit('x', $event.target.value)"
              >
                <option value="px">px</option>
                <option value="%">%</option>
                <option value="vw">vw</option>
              </select>
            </div>
          </div>

          <div class="property-row position-row">
            <label>Y 좌표:</label>
            <div class="input-with-unit">
              <input
                type="number"
                :value="selectedElement.position.y.value"
                @input="updatePosition('y', $event.target.value)"
              />
              <select
                class="unit-select"
                :value="selectedElement.position.y.unit"
                @change="updatePositionUnit('y', $event.target.value)"
              >
                <option value="px">px</option>
                <option value="%">%</option>
                <option value="vh">vh</option>
              </select>
            </div>
          </div>
        </div>

        <div class="property-row size-row">
          <label>너비:</label>
          <div class="size-controls">
            <div class="input-with-unit">
              <input
                type="number"
                :value="getNumericSize(selectedElement.size.width)"
                @input="updateSize('width', $event.target.value)"
                min="50"
                max="500"
                placeholder="자동"
                :disabled="isAutoWidth"
              />
              <select
                class="unit-select"
                :value="getSizeUnit(selectedElement.size.width)"
                @change="updateSizeUnit('width', $event.target.value)"
                :disabled="isAutoWidth"
              >
                <option value="px">px</option>
                <option value="%">%</option>
                <option value="vw">vw</option>
              </select>
            </div>
            <div class="checkbox-container">
              <input
                type="checkbox"
                :checked="isAutoWidth"
                @change="toggleAutoSize('width', $event.target.checked)"
                id="autoWidth"
              />
              <label for="autoWidth" class="checkbox-label">자동</label>
            </div>
          </div>
        </div>

        <div class="property-row size-row">
          <label>높이:</label>
          <div class="size-controls">
            <div class="input-with-unit">
              <input
                type="number"
                :value="getNumericSize(selectedElement.size.height)"
                @input="updateSize('height', $event.target.value)"
                min="20"
                max="1000"
                placeholder="자동"
                :disabled="isAutoHeight"
              />
              <select
                class="unit-select"
                :value="getSizeUnit(selectedElement.size.height)"
                @change="updateSizeUnit('height', $event.target.value)"
                :disabled="isAutoHeight"
              >
                <option value="px">px</option>
                <option value="%">%</option>
                <option value="vh">vh</option>
              </select>
            </div>
            <div class="checkbox-container">
              <input
                type="checkbox"
                :checked="isAutoHeight"
                @change="toggleAutoSize('height', $event.target.checked)"
                id="autoHeight"
              />
              <label for="autoHeight" class="checkbox-label">자동</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 요소별 특화 속성 -->
      <component
        :is="getPropertyComponent(selectedElement.type)"
        :element="selectedElement"
        @update="updateElementProperty"
      />

      <!-- 스타일 속성 -->
      <div class="property-section">
        <h4>스타일</h4>

        <div class="property-row">
          <label>배경색:</label>
          <input
            type="color"
            :value="selectedElement.style.backgroundColor || '#ffffff'"
            @input="updateStyle('backgroundColor', $event.target.value)"
          />
        </div>

        <div class="property-row">
          <label>테두리 두께:</label>
          <input
            type="number"
            :value="selectedElement.style.borderWidth"
            @input="updateStyle('borderWidth', $event.target.value)"
            min="0"
          />
        </div>

        <div class="property-row">
          <label>테두리 색상:</label>
          <input
            type="color"
            :value="selectedElement.style.borderColor || '#000000'"
            @input="updateStyle('borderColor', $event.target.value)"
            :disabled="!selectedElement.style.borderWidth"
          />
        </div>

        <div class="property-row">
          <label>테두리 둥글기:</label>
          <input
            type="number"
            :value="selectedElement.style.borderRadius"
            @input="updateStyle('borderRadius', $event.target.value)"
            min="0"
          />
        </div>

        <div class="property-row">
          <label>투명도:</label>
          <input
            type="range"
            :value="selectedElement.style.opacity * 100"
            @input="updateStyle('opacity', $event.target.value / 100)"
            min="0"
            max="100"
          />
          <span>{{ Math.round(selectedElement.style.opacity * 100) }}%</span>
        </div>
      </div>

      <!-- 레이어 순서 조정 -->
      <div class="property-section">
        <h4>레이어 순서</h4>

        <div class="layer-buttons">
          <button @click="bringToFront">맨 앞으로</button>
          <button @click="bringForward">앞으로</button>
          <button @click="sendBackward">뒤로</button>
          <button @click="sendToBack">맨 뒤로</button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useLandingEditorStore } from '@/stores/landingEditor';

// 요소별 속성 컴포넌트 임포트
import TextProperties from './properties/TextProperties.vue';
import ImageProperties from './properties/ImageProperties.vue';
import ButtonProperties from './properties/ButtonProperties.vue';
import LinkProperties from './properties/LinkProperties.vue';
import DivProperties from './properties/DivProperties.vue';
import CanvasProperties from './CanvasProperties.vue';

// 스토어 접근
const landingEditorStore = useLandingEditorStore();

// 캔버스 속성 편집 모드 상태
const showCanvasProperties = ref(false);

// 선택된 요소
const selectedElement = computed(() => landingEditorStore.selectedElement);

// 너비 자동 크기 여부
const isAutoWidth = computed(() => {
  if (!selectedElement.value) return false;
  return selectedElement.value.size.width.value === 'auto';
});

// 높이 자동 크기 여부
const isAutoHeight = computed(() => {
  if (!selectedElement.value) return false;
  return selectedElement.value.size.height.value === 'auto';
});

// 요소 타입에 따른 제목 반환
const getElementTitle = (type) => {
  const titleMap = {
    text: '텍스트',
    image: '이미지',
    button: '버튼',
    link: '링크',
    div: '블록'
  };

  return titleMap[type] || '요소';
};

// 요소 타입에 따른 속성 컴포넌트 반환
const getPropertyComponent = (type) => {
  const componentMap = {
    text: TextProperties,
    image: ImageProperties,
    button: ButtonProperties,
    link: LinkProperties,
    div: DivProperties
  };

  return componentMap[type] || null;
};

// 크기 값을 숫자로 변환 (auto, px, % 등 처리)
const getNumericSize = (size) => {
  if (!size) return '';
  if (size.value === 'auto' || size.value === undefined) return '';
  return size.value;
};

// 크기 단위 가져오기
const getSizeUnit = (size) => {
  if (!size) return 'px';
  if (size.unit === 'auto') return 'px';
  return size.unit;
};

// 위치 업데이트
const updatePosition = (axis, value) => {
  if (!selectedElement.value) return;

  const numValue = parseInt(value);
  if (isNaN(numValue)) return;

  // 현재 위치 객체 복사
  const currentPosition = selectedElement.value.position[axis];

  // 값만 변경하고 단위는 유지
  const newPosition = { value: numValue, unit: currentPosition.unit };

  // 업데이트
  if (axis === 'x') {
    landingEditorStore.updateElementPosition(
      selectedElement.value.id,
      newPosition,
      selectedElement.value.position.y
    );
  } else {
    landingEditorStore.updateElementPosition(
      selectedElement.value.id,
      selectedElement.value.position.x,
      newPosition
    );
  }
};

// 위치 단위 업데이트
const updatePositionUnit = (axis, unit) => {
  if (!selectedElement.value) return;

  // 현재 위치 객체 복사
  const currentPosition = selectedElement.value.position[axis];

  // 값은 유지하고 단위만 변경
  const newPosition = { value: currentPosition.value, unit };

  // 업데이트
  if (axis === 'x') {
    landingEditorStore.updateElementPosition(
      selectedElement.value.id,
      newPosition,
      selectedElement.value.position.y
    );
  } else {
    landingEditorStore.updateElementPosition(
      selectedElement.value.id,
      selectedElement.value.position.x,
      newPosition
    );
  }
};

// 크기 업데이트
const updateSize = (dimension, value) => {
  if (!selectedElement.value) return;

  // 값이 비어있으면 'auto'로 설정
  if (value === '') {
    toggleAutoSize(dimension, true);
    return;
  }

  const numValue = parseInt(value);
  if (isNaN(numValue)) return;

  // 현재 크기 객체 복사
  const currentSize = selectedElement.value.size[dimension];

  // 값만 변경하고 단위는 유지
  const newSize = { value: numValue, unit: currentSize.unit };

  // 업데이트
  if (dimension === 'width') {
    landingEditorStore.updateElementSize(
      selectedElement.value.id,
      newSize,
      selectedElement.value.size.height
    );
  } else {
    landingEditorStore.updateElementSize(
      selectedElement.value.id,
      selectedElement.value.size.width,
      newSize
    );
  }
};

// 크기 단위 업데이트
const updateSizeUnit = (dimension, unit) => {
  if (!selectedElement.value) return;

  // 현재 크기 객체 복사
  const currentSize = selectedElement.value.size[dimension];

  // 값은 유지하고 단위만 변경
  const newSize = { value: currentSize.value, unit };

  // 업데이트
  if (dimension === 'width') {
    landingEditorStore.updateElementSize(
      selectedElement.value.id,
      newSize,
      selectedElement.value.size.height
    );
  } else {
    landingEditorStore.updateElementSize(
      selectedElement.value.id,
      selectedElement.value.size.width,
      newSize
    );
  }
};

// 자동 크기 토글
const toggleAutoSize = (dimension, isAuto) => {
  if (!selectedElement.value) return;

  if (isAuto) {
    // 자동 크기로 설정
    const autoSize = { value: 'auto', unit: 'auto' };

    if (dimension === 'width') {
      landingEditorStore.updateElementSize(
        selectedElement.value.id,
        autoSize,
        selectedElement.value.size.height
      );
    } else {
      landingEditorStore.updateElementSize(
        selectedElement.value.id,
        selectedElement.value.size.width,
        autoSize
      );
    }
  } else {
    // 기본 크기로 설정
    const defaultSize = { value: dimension === 'width' ? 200 : 100, unit: 'px' };

    if (dimension === 'width') {
      landingEditorStore.updateElementSize(
        selectedElement.value.id,
        defaultSize,
        selectedElement.value.size.height
      );
    } else {
      landingEditorStore.updateElementSize(
        selectedElement.value.id,
        selectedElement.value.size.width,
        defaultSize
      );
    }
  }
};

// 스타일 업데이트
const updateStyle = (property, value) => {
  if (!selectedElement.value) return;

  landingEditorStore.updateElementProperty(
    selectedElement.value.id,
    `style.${property}`,
    value
  );
};

// 요소 속성 업데이트
const updateElementProperty = (propertyPath, value) => {
  if (!selectedElement.value) return;

  // size 속성의 경우 크기 업데이트 함수 호출
  if (propertyPath === 'size') {
    landingEditorStore.updateElementSize(
      selectedElement.value.id,
      value.width,
      value.height
    );
  } else {
    // 일반 속성 업데이트
    landingEditorStore.updateElementProperty(
      selectedElement.value.id,
      propertyPath,
      value
    );
  }
};

// 요소 삭제
const deleteElement = () => {
  if (!selectedElement.value) return;

  if (confirm('이 요소를 삭제하시겠습니까?')) {
    landingEditorStore.deleteElement(selectedElement.value.id);
  }
};

// 레이어 순서 조정
const bringToFront = () => {
  if (!selectedElement.value) return;
  landingEditorStore.bringElementToFront(selectedElement.value.id);
};

const bringForward = () => {
  if (!selectedElement.value) return;
  landingEditorStore.bringElementForward(selectedElement.value.id);
};

const sendBackward = () => {
  if (!selectedElement.value) return;
  landingEditorStore.sendElementBackward(selectedElement.value.id);
};

const sendToBack = () => {
  if (!selectedElement.value) return;
  landingEditorStore.sendElementToBack(selectedElement.value.id);
};
</script>

<style scoped>
.property-panel {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  overflow-y: auto;
  height: 100%;
  max-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #999;
  font-style: italic;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.canvas-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.canvas-button:hover {
  background-color: #1976D2;
}

.canvas-icon {
  font-size: 14px;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.delete-button:hover {
  background-color: #d32f2f;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 13px;
  cursor: pointer;
  margin-bottom: 16px;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.back-icon {
  font-size: 14px;
}

.property-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
}

.property-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  color: #555;
}

.property-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.property-row label {
  flex: 0 0 100px;
  font-size: 13px;
  color: #666;
}

.property-row input {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.property-row input[type="color"] {
  width: 40px;
  height: 24px;
  padding: 0;
  border: 1px solid #ddd;
}

.property-row input[type="range"] {
  margin-right: 8px;
}

.property-row input:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.property-row span {
  font-size: 13px;
  color: #666;
  margin-left: 8px;
}

.input-with-unit {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 중요: flex 아이템이 너무 작아지는 것을 방지 */
}

.input-with-unit input {
  flex: 1;
  margin-right: 4px;
  min-width: 0; /* 중요: 입력 필드가 너무 작아지는 것을 방지 */
}

.unit-select {
  width: 50px;
  min-width: 50px; /* 최소 너비 고정 */
  padding: 6px 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  background-color: #f9f9f9;
  flex-shrink: 0; /* 중요: 선택 상자가 줄어들지 않도록 방지 */
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.checkbox-label {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.size-row {
  margin-bottom: 12px;
}

.size-controls {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
}

.size-controls .input-with-unit {
  flex: 1;
  min-width: 120px;
  margin-bottom: 4px;
}

.size-controls .checkbox-container {
  margin-left: 8px;
  margin-bottom: 4px;
}

.position-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.position-row {
  flex: 1;
  min-width: 180px;
  margin-bottom: 4px;
}

.layer-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.layer-buttons button {
  padding: 6px 8px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.layer-buttons button:hover {
  background-color: #f0f0f0;
}
</style>
