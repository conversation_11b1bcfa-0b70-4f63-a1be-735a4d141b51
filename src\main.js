import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import App from './App.vue'
import router from './router'
import { setupInterceptors } from './api/interceptors'
import { useAuthStore } from '@/stores/auth'

const app = createApp(App)

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

const authStore = useAuthStore();

setupInterceptors();

app.use(router)

// 앱을 즉시 마운트하고 인증 상태 확인은 백그라운드에서 진행
app.mount('#app')

// 라우터 준비 후, 인증이 필요한 페이지에서만 백그라운드 인증 상태 확인
router.isReady().then(() => {
  const to = router.currentRoute.value;
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  if (requiresAuth) {
    authStore.checkAuthStatus().catch(error => {
      console.error("Failed to initialize app auth status:", error);
    });
  }
});
