<template>
  <div class="inquiry-list-view">
    <h1>문의하기</h1>
    
    <div class="inquiry-list-container">
      <div class="filters">
        <div class="filter-group">
          <router-link to="/inquiries/new" class="create-btn">새 문의 작성</router-link>
          
          <!-- 페이지당 항목 수 선택 UI -->
          <div class="items-per-page-selector">
            <span>페이지당 항목:</span>
            <div class="items-per-page-buttons">
              <button 
                @click="changePageSize(10)" 
                :class="['item-count-btn', pageSize === 10 ? 'active' : '']">
                10개
              </button>
              <button 
                @click="changePageSize(30)" 
                :class="['item-count-btn', pageSize === 30 ? 'active' : '']">
                30개
              </button>
              <button 
                @click="changePageSize(50)" 
                :class="['item-count-btn', pageSize === 50 ? 'active' : '']">
                50개
              </button>
            </div>
          </div>
        </div>
        
        <div class="filter-group">
          <label for="searchTypeSelect">검색 유형:</label>
          <select id="searchTypeSelect" v-model="searchType" class="search-type-select">
            <option 
              v-for="type in availableSearchTypes" 
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </option>
          </select>

          <label for="answerTypeSelect">답변 유형:</label>
          <select id="answerTypeSelect" v-model="selectedAnswerType" class="answer-type-select">
            <option 
              v-for="type in answerTypes" 
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </option>
          </select>

          <label for="searchInput">검색어:</label>
          <input
            id="searchInput"
            type="text"
            v-model="searchTerm"
            placeholder="검색어 입력"
            @keyup.enter="applyFilters"
          />
          
          <button @click="applyFilters" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>

      <!-- 로딩 표시 -->
      <div v-if="isLoading" class="text-center my-5">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">로딩 중...</span>
        </div>
      </div>

      <!-- 오류 메시지 -->
      <div v-else-if="error" class="alert alert-danger my-3">
        {{ error }}
      </div>

      <!-- 문의 목록이 비어 있는 경우 -->
      <div v-else-if="!inquiries.length" class="no-data-message">
        문의 내역이 없습니다.
      </div>

      <!-- 문의 목록 -->
      <div v-else class="inquiry-table-container">
        <table class="inquiry-table">
          <thead>
            <tr>
              <th>번호</th>
              <th>제목</th>
              <th>유형</th>
              <th>상태</th>
              <th>작성자</th>
              <th>작성일</th>
              <th>기능</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(inquiry, index) in inquiries" :key="inquiry.inquiryId">
              <td>{{ pageInfo.totalElements - (pageInfo.number * pageInfo.size + index) }}</td>
              <td class="inquiry-title">{{ inquiry.inquiryTitle }}</td>
              <td>{{ getTypeLabel(inquiry.inquiryType) }}</td>
              <td>
                <span
                  class="status-badge"
                  :class="{
                    'pending': inquiry.inquiryStatus === 'PENDING',
                    'in-progress': inquiry.inquiryStatus === 'PROCESSING',
                    'completed': inquiry.inquiryStatus === 'COMPLETED'
                  }"
                >
                  {{ getStatusLabel(inquiry.inquiryStatus) }}
                </span>
              </td>
              <td>{{ inquiry.userEmail }}</td>
              <td>{{ formatDate(inquiry.createdAt) }}</td>
              <td class="actions">
                <button 
                  @click="viewInquiryDetail(inquiry.inquiryId)" 
                  class="action-btn view-btn"
                  :disabled="!canViewInquiry(inquiry.userEmail)"
                >보기</button>
                <button 
                  v-if="isCurrentUserAuthor(inquiry.userEmail)" 
                  @click="confirmDelete(inquiry.inquiryId)" 
                  class="action-btn delete-btn"
                  :disabled="isDeleting"
                >
                  삭제
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 페이지네이션 -->
      <div class="pagination-section" v-if="totalPages > 1">
        <div class="pagination">
          <button
            @click="changePage(1)"
            :disabled="pageInfo.number === 0"
            class="pagination-btn"
          >
            &laquo;
          </button>
          <button
            @click="changePage(pageInfo.number + 1)"
            :disabled="pageInfo.number === 0"
            class="pagination-btn"
          >
            &lt;
          </button>

          <span class="page-info">{{ pageInfo.number + 1 }} / {{ totalPages }}</span>

          <button
            @click="changePage(pageInfo.number + 2)"
            :disabled="pageInfo.last"
            class="pagination-btn"
          >
            &gt;
          </button>
          <button
            @click="changePage(totalPages)"
            :disabled="pageInfo.last"
            class="pagination-btn"
          >
            &raquo;
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { inquiryList, deleteInquiry } from '@/api/inquiry';
import { useAuthStore } from '@/stores/auth';

export default {
  name: 'InquiryListView',
  setup() {
    const router = useRouter();
    const authStore = useAuthStore();
    const inquiries = ref([]);
    const isLoading = ref(false);
    const error = ref(null);
    const searchTerm = ref('');
    const selectedAnswerType = ref(null); // null로 초기화하여 기본값 처리 준비
    const searchType = ref('');
    const answerTypes = ref([]);
    const availableSearchTypes = ref([]);
    const isDeleting = ref(false); // 삭제 중 상태 표시용
    const totalPages = ref(1);
    const currentPage = ref(0); // API는 0부터 시작하는 페이지 인덱스 사용
    const pageSize = ref(10);
    const projectId = ref(7); // 현재 프로젝트 ID - 실제로는 스토어에서 가져와야 합니다
    
    // API 응답 데이터 저장을 위한 상태 관리
    const pageInfo = ref({});
    
    // 권한 체크
    const canCreateInquiry = computed(() => {
      // 모든 사용자가 문의를 작성할 수 있도록 허용
      return true;
    });
    
    // 실제 사용자 역할 가져오기
    const userRole = computed(() => {
      return authStore.user?.roleId || 'USER';
    });
    
    // 문의 목록 불러오기
    const loadInquiries = async () => {
      isLoading.value = true;
      error.value = null;
      
      try {
        // API 요청 파라미터 준비 (서버에서 요구하는 정확한 형식으로 설정)
        const params = {
          projectId: projectId.value, // 프로젝트 ID 추가
          page: currentPage.value, // 서버는 페이지 번호가 1부터 시작하는 방식 사용
          size: pageSize.value,
          sort: "createDate,desc" // 정렬 기준: 배열이 아니라 문자열 형식으로 전달
        };
        
        // 검색어 처리
        if (searchTerm.value) {
          // 검색 유형에 따라 적절한 파라미터 설정
          // 서버에서 기대하는 문의 유형 검색 타입은 'inquiryType'
          if (searchType.value === 'inquiryType') {
            const convertedTerm = getTypeCode(searchTerm.value);
            
            // 문의 유형 검색일 경우에는 searchKeyword에 변환된 코드 값을 전송
            params.searchKeyword = convertedTerm;
          } else {
            // 다른 검색 유형의 경우는 그대로 전송
            params.searchKeyword = searchTerm.value;
          }
        }
        
        if (selectedAnswerType.value && selectedAnswerType.value !== 'ALL') {
          params.inquiryStatus = selectedAnswerType.value; // 서버에서 기대하는 파라미터는 inquiryStatus임
        }
        
        if (searchType.value) {
          params.searchType = searchType.value;
        }
        
        // API 호출
        const response = await inquiryList(params);
        
        // 응답 데이터 처리
        if (response.data.success) {
          // 페이지 정보와 콘텐츠 저장
          pageInfo.value = response.data.data;
          
          // null 처리를 하여 안전하게 데이터 전용
          inquiries.value = response.data.data.content.map(item => ({
            ...item,
            comments: item.comments || [],
            attachments: item.attachments || []
          }));
          
          // 페이지네이션 정보 설정
          totalPages.value = response.data.data.totalPages;
          
          // 답변 유형 데이터 처리
          if (response.data.data.answerSearchTypes) {
            // 데이터 형식 확인 및 적절한 처리
            const rawData = response.data.data.answerSearchTypes;
            
            if (Array.isArray(rawData)) {
              // 이미 배열인 경우 (각 항목이 {value, label} 형태인지 확인)
              answerTypes.value = rawData
                .map(item => {
                  if (typeof item === 'object' && item.value !== undefined && item.label !== undefined) {
                    return item; // 이미 알릮은 형태
                  } else {
                    return { value: item, label: item }; // 기본 형태로 변환
                  }
                })
                // 값이 비어있거나 undefined인 항목 제외
                .filter(item => item.value !== '' && item.value !== undefined && item.label !== '' && item.label !== undefined);
              
              // 데이터가 존재하면 기본값 설정
              if (answerTypes.value.length > 0) {
                // 기본값이 설정되지 않았거나 null인 경우에만 처음 항목으로 설정
                if (selectedAnswerType.value === null || selectedAnswerType.value === '') {
                  selectedAnswerType.value = answerTypes.value[0].value;
                }
              }
            } else if (typeof rawData === 'object') {
              // 객체인 경우 - Object.entries로 변환
              answerTypes.value = Object.entries(rawData)
                .map(([key, value]) => {
                  return { value: key, label: value };
                })
                // 값이 비어있거나 undefined인 항목 제외
                .filter(item => item.value !== '' && item.value !== undefined && item.label !== '' && item.label !== undefined);
              
              // 데이터가 존재하면 기본값 설정
              if (answerTypes.value.length > 0) {
                // 기본값이 설정되지 않았거나 null인 경우에만 처음 항목으로 설정
                if (selectedAnswerType.value === null || selectedAnswerType.value === '') {
                  selectedAnswerType.value = answerTypes.value[0].value;
                }
              }
            } else {
              // 그 외의 경우, 빈 배열로 초기화
              answerTypes.value = [];
              console.error('서버에서 받은 답변 유형 데이터 형식이 유효하지 않음:', rawData);
            }
          }
          
          // 검색 유형 데이터 처리 (availableSearchTypes)
          if (response.data.data.availableSearchTypes) {
            
            // 서버에서 받은 검색 유형 데이터를 적용
            if (Array.isArray(response.data.data.availableSearchTypes)) {
              availableSearchTypes.value = response.data.data.availableSearchTypes;
              
              // 기본값 설정 (처음 로드되었고 현재 값이 비어있는 경우)
              if (availableSearchTypes.value.length > 0 && !searchType.value) {
                searchType.value = availableSearchTypes.value[0].value;
              }
            }
          }
          
        } else {
          throw new Error(response.data.error?.message || '문의 목록을 불러오는데 실패했습니다.');
        }
      } catch (e) {
        error.value = '문의 목록을 불러오는데 실패했습니다: ' + e.message;
        console.error('문의 목록 조회 오류:', e);
      } finally {
        isLoading.value = false;
      }
    };
    
    // 필터 적용
    const applyFilters = () => {
      currentPage.value = 0; // 필터 적용 시 첫 페이지로 이동
      loadInquiries();
    };
    
    // 검색 초기화
    const resetSearch = () => {
      searchTerm.value = '';
      
      // 기본 답변 유형으로 초기화
      if (answerTypes.value.length > 0) {
        selectedAnswerType.value = answerTypes.value[0].value;
      } else {
        selectedAnswerType.value = '';
      }
      
      // 기본 검색 유형으로 초기화
      if (availableSearchTypes.value.length > 0) {
        searchType.value = availableSearchTypes.value[0].value;
      } else {
        searchType.value = '';
      }
      
      currentPage.value = 0;
      loadInquiries();
    };
    
    // 페이지 크기 변경
    const changePageSize = (size) => {
      pageSize.value = size;
      currentPage.value = 0; // 페이지 크기 변경 시 처음 페이지로 돌아가기
      loadInquiries();
    };
    
    // 페이지 변경
    const changePage = (page) => {
      // UI와 API 모두 1부터 시작하는 페이지 번호 사용
      // 다만 내부적으로는 0부터 시작하는 인덱스로 관리 (페이지 변경 시 +1 처리는 API 호출 때만 적용)
      currentPage.value = page - 1;
      loadInquiries();
    };
    
    // 문의 상세 조회
    const viewInquiryDetail = (inquiryId) => {
      // 상세 페이지로 따로 이동
      router.push(`/inquiries/${inquiryId}`);
    };
    
    // 날짜 형식화
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('ko-KR', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };
    
    // 문의 상태 라벨 변환
    const getStatusLabel = (status) => {
      const statusMap = {
        'PENDING': '접수됨',
        'PROCESSING': '처리중',
        'COMPLETED': '답변완료'
      };
      return statusMap[status] || status;
    };
    
    // 문의 유형 라벨 변환
    const getTypeLabel = (type) => {
      const typeMap = {
        'ACCOUNT': '계정 문의',
        'TECHNICAL': '기술 문의',
        'USAGE': '이용 문의',
        'ETC': '기타 문의'
      };
      return typeMap[type] || type;
    };
    
    // 문의 유형 한글명을 코드로 변환 (검색어 변환용)
    const getTypeCode = (typeLabel) => {
      // 빈 검색어인 경우 그대로 반환
      if (!typeLabel || typeLabel.trim() === '') {
        return '';
      }
      
      // 대소문자 구분 없이 비교하기 위해 소문자로 통일 및 공백 제거
      const normalizedLabel = typeLabel.trim().toLowerCase();
      
      // 간소화된 키워드 중심 매핑
      const keywordMap = {
        '계정': 'ACCOUNT',
        '기술': 'TECHNICAL',
        '이용': 'USAGE',
        '기타': 'ETC'
      };
      
      // 1. 정확한 키워드 포함 여부 확인
      for (const [keyword, code] of Object.entries(keywordMap)) {
        if (normalizedLabel.includes(keyword)) {
          return code;
        }
      }
      
      return typeLabel; // 매칭되는 코드가 없으면 원본 검색어 반환
    };
    
    // 삭제 기능 관련 메서드
    
    // 현재 로그인한 사용자가 작성자인지 확인
    const isCurrentUserAuthor = (authorEmail) => {
      const currentUserEmail = authStore.user?.userEmail || '';
      return currentUserEmail === authorEmail;
    };
    
    // 문의 상세 조회 권한 확인 (SUPER_ADMIN이거나 작성자인 경우)
    const canViewInquiry = (authorEmail) => {
      const isSuperAdmin = userRole.value === 'SUPER_ADMIN';
      return isSuperAdmin || isCurrentUserAuthor(authorEmail);
    };
    
    // 삭제 확인 다이얼로그
    const confirmDelete = (inquiryId) => {
      if (confirm('문의를 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.')) {
        deleteInquiryItem(inquiryId);
      }
    };
    
    // 실제 삭제 처리
    const deleteInquiryItem = async (inquiryId) => {
      try {
        isDeleting.value = true;
        
        const response = await deleteInquiry(inquiryId);
        
        if (response.data.success) {
          alert('문의가 삭제되었습니다.');
          
          // 목록 새로 불러오기
          await loadInquiries();
        } else {
          console.error('문의 삭제 실패:', response.data.error);
          alert('문의 삭제에 실패하였습니다: ' + (response.data.error?.message || '알 수 없는 오류'));
        }
      } catch (e) {
        console.error('문의 삭제 오류:', e);
        alert('문의 삭제 중 오류가 발생하였습니다: ' + e.message);
      } finally {
        isDeleting.value = false;
      }
    };
    
    // 컴포넌트 마운트 시 목록 불러오기
    onMounted(() => {
      loadInquiries();
    });
    
    // 답변 유형 변경 감지
    watch(() => answerTypes.value, (newTypes) => {
      if (newTypes && Array.isArray(newTypes) && newTypes.length > 0) {
        // 기본값이 설정되지 않았거나 null인 경우에만 처음 항목으로 설정
        if (selectedAnswerType.value === null || selectedAnswerType.value === '') {
          selectedAnswerType.value = newTypes[0].value;
        }
      }
    }, { immediate: true });
    
    return {
      inquiries,
      isLoading,
      error,
      searchTerm,
      selectedAnswerType,
      answerTypes,
      searchType,
      availableSearchTypes,
      totalPages,
      currentPage,
      pageSize,
      projectId,
      canCreateInquiry,
      applyFilters,
      resetSearch,
      changePage,
      changePageSize,
      viewInquiryDetail,
      formatDate,
      getStatusLabel,
      getTypeLabel,
      pageInfo,
      isCurrentUserAuthor,
      canViewInquiry,
      confirmDelete,
      isDeleting
    };
  }
};
</script>

<style scoped>
.inquiry-list-view {
  padding: 20px;
}

.header-section {
  margin-bottom: 20px;
}

/* 필터 스타일 */
.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group input, .filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

/* 테이블 스타일 */
.inquiry-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.inquiry-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
}

.inquiry-table th {
  background-color: #f2f2f2;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  cursor: pointer;
  user-select: none;
}

.inquiry-table th:hover {
  background-color: #e6e6e6;
}

.inquiry-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #ddd;
}

.inquiry-table tr:hover {
  background-color: #f5f5f5;
}

/* 문의 항목 스타일 */
.list-group-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

/* 버튼 스타일 */
.action-btn.view-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  margin-right: 5px;
}

.action-btn.delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.action-btn:disabled {
  background-color: #cdcecf;
  cursor: not-allowed;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  text-decoration: none;
}

.create-btn:hover {
  background-color: #45a049;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: #0b7dda;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

/* 페이지네이션 섹션 */
.pagination-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pagination-btn:hover:not([disabled]) {
  background-color: #e6e6e6;
}

.pagination-btn[disabled] {
  color: #aaa;
  cursor: not-allowed;
  background-color: #f8f8f8;
  opacity: 0.5;
}

.page-info {
  padding: 4px 8px;
  font-weight: 500;
  color: #333;
}

/* 로딩 및 오류 메시지 */
.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
  color: #2196F3;
}

.alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #ffebee;
  color: #d32f2f;
  border: 1px solid #ffcdd2;
}

.alert-info {
  background-color: #e3f2fd;
  color: #0d47a1;
  border: 1px solid #bbdefb;
}

/* 테이블 셀 스타일 */
.inquiry-title {
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
}

/* 상태 배지 */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

.status-badge.pending {
  background-color: #fff3e0;
  color: #e65100;
  border: 1px solid #ffe0b2;
}

.status-badge.in-progress {
  background-color: #e3f2fd;
  color: #0d47a1;
  border: 1px solid #bbdefb;
}

.status-badge.completed {
  background-color: #e8f5e9;
  color: #1b5e20;
  border: 1px solid #c8e6c9;
}

/* 액션 버튼 스타일 */
.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background-color: #bbdefb;
}

/* 페이지당 항목 수 선택 UI 스타일 */
.items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
  gap: 10px;
}

.items-per-page-selector span {
  font-size: 14px;
  color: #555;
}

.items-per-page-buttons {
  display: flex;
  gap: 5px;
}

.item-count-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f8f8;
  color: #333;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.item-count-btn:hover {
  background-color: #e0e0e0;
}

.item-count-btn.active {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}
</style>