import axios from 'axios';

// Axios 인스턴스 생성
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, // 환경 변수에서 baseURL 읽어오기
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // 쿠키를 요청과 함께 보내기 위한 설정
  // 필요에 따라 타임아웃 등의 다른 설정 추가 가능
  // timeout: 10000,
});

// QR 코드 통계 조회 API 함수
export const getQrCodeStatistics = (qrCodeId, startDate, endDate) => {
  return apiClient.get(`/qrcodes/${qrCodeId}/statistics`, {
    params: {
      startDate,
      endDate
    }
  });
};

export default apiClient;
