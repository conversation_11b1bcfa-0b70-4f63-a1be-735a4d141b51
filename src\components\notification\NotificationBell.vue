<template>
  <div class="notification-bell" @click="toggleNotifications">
    <div class="bell-icon">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
        <path fill="currentColor" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/>
      </svg>
    </div>
    <div v-if="unreadCount > 0" class="notification-badge">
      {{ displayCount }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useNotificationStore } from '@/stores/notificationStore';

const notificationStore = useNotificationStore();

// 읽지 않은 알림 개수
const unreadCount = computed(() => notificationStore.unreadCount);

// 표시할 개수 (99+로 표시)
const displayCount = computed(() => {
  return unreadCount.value > 99 ? '99+' : unreadCount.value;
});

// 부모 컴포넌트의 이벤트 트리거
const emit = defineEmits(['toggle']);

// 알림 목록 토글
const toggleNotifications = () => {
  emit('toggle');
};
</script>

<style scoped>
.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 50%;
  border: 1px solid #C9C9C9;
}

.bell-icon {
  color: #333;
  width: 24px;
  height: 24px;
  transition: color 0.3s;
}

.notification-bell:hover .bell-icon {
  color: #007bff;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
