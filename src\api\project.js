import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 프로젝트 목록을 가져옵니다.
 * @param {object} paginationParams - 페이지네이션 파라미터 (선택적)
 * @returns {Promise<Object>} 프로젝트 목록을 포함한 응답 객체 Promise
 */
export const getProjects = async (paginationParams = {}) => {
  try {
    // 기본 파라미터 설정
    const params = {
      page: 0,
      size: 10,
      sort: 'projectId,desc',
      ...paginationParams
    };

    // 공통 API 호출 함수를 사용하여 요청 처리
    // 새로운 서버 응답 구조: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    const response = await executeApiCall(
      () => apiClient.get('/projects/list', { params }),
      '프로젝트 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '프로젝트 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트 상세 정보를 가져옵니다.
 * @param {number} projectId - 가져올 프로젝트 ID
 * @returns {Promise<Object>} 프로젝트 상세 정보를 포함한 응답 객체 Promise
 */
export const getProjectById = async (projectId) => {
  try {
    // 서버 응답 구조: { success: true, data: {...} }
    const response = await executeApiCall(
      () => apiClient.get(`/projects/${projectId}`),
      '프로젝트 상세 정보를 가져오는 데 실패했습니다.'
    );
    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    console.error(`프로젝트 상세 정보 오류:`, error);
    const errorMessage = handleApiError(error, '프로젝트 상세 정보를 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새 프로젝트를 생성합니다.
 * @param {object} projectData - 프로젝트 데이터 ({ projectName, description, projectAdminUserEmail })
 * @returns {Promise<object>} 성공 시 생성된 프로젝트 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const createProject = async (projectData) => {
  try {
    const response = await apiClient.post('/projects', projectData);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data; // 성공 응답 전체 또는 필요한 부분 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || '프로젝트 생성에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '프로젝트 생성 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트를 수정합니다.
 * @param {number} projectId - 수정할 프로젝트 ID
 * @param {object} projectData - 프로젝트 데이터 ({ projectName, description, projectAdminUserEmail })
 * @returns {Promise<object>} 성공 시 수정된 프로젝트 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const updateProject = async (projectId, projectData) => {
  try {
    const response = await apiClient.put(`/projects/${projectId}`, projectData);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data; // 성공 응답 전체 또는 필요한 부분 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || '프로젝트 수정에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '프로젝트 수정 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트를 삭제합니다.
 * @param {number} projectId - 삭제할 프로젝트 ID
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const deleteProject = async (projectId) => {
  try {
    const response = await apiClient.delete(`/projects/${projectId}`);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return true; // 성공 시 true 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || '프로젝트 삭제에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '프로젝트 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 현재 로그인한 사용자가 속한 프로젝트 목록을 가져옵니다.
 * @returns {Promise<Object>} 사용자가 속한 프로젝트 목록을 포함한 응답 객체 Promise
 */
export const getUserProjects = async () => {
  try {
    // 직접 API 호출하여 응답 구조 확인
    const response = await apiClient.get('/projects/user');

    if (response && response.data) {
      // 응답 구조에 따라 데이터 추출 및 반환
      return response.data;
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('사용자 프로젝트 목록 API 오류:', error);
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사용자 프로젝트 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * SUPER_ADMIN이 모든 프로젝트 목록을 가져옵니다.
 * @param {object} paginationParams - 페이지네이션 파라미터 (선택적)
 * @returns {Promise<Object>} 모든 프로젝트 목록을 포함한 응답 객체 Promise
 *
 * 응답 형식:
 * {
 *   "success": true,
 *   "data": {
 *     "totalPages": 0,
 *     "totalElements": 0,
 *     "size": 0,
 *     "content": [
 *       {
 *         "projectId": 1,
 *         "projectName": "웨이플러스 QR코드",
 *         "description": "웨이플러스 QR코드 관리용 프로젝트",
 *         "projectAdminUserEmail": "<EMAIL>",
 *         "status": "ACTIVE",
 *         "createUserEmail": "<EMAIL>",
 *         "createDate": "2025-04-01 10:00:00",
 *         "updateUserEmail": "<EMAIL>",
 *         "lastUpdateDate": "2025-04-08 14:30:00"
 *       }
 *     ],
 *     "number": 0,
 *     "sort": [...],
 *     "first": true,
 *     "last": true,
 *     "numberOfElements": 0,
 *     "pageable": {...},
 *     "empty": true
 *   },
 *   "error": {...}
 * }
 */
export const getAllProjects = async (paginationParams = {}) => {
  try {
    // 기본 파라미터 설정
    const params = {
      page: 0,
      size: 100, // 더 많은 프로젝트를 한 번에 가져오기 위해 크게 설정
      sort: 'projectId,desc',
      ...paginationParams
    };

    // 직접 API 호출하여 응답 구조 확인
    const response = await apiClient.get('/super/projects/all', { params });

    if (response && response.data) {
      // 응답 구조에 따라 데이터 추출 및 반환
      return response.data;
    }

    throw new Error('유효하지 않은 응답 구조');
  } catch (error) {
    console.error('모든 프로젝트 목록 API 오류:', error);
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'SUPER_ADMIN 모든 프로젝트 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};
