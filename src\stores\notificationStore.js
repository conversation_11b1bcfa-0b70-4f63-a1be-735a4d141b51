import { defineStore } from 'pinia';
import * as notificationApi from '@/api/notification';

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: [],
    unreadCount: 0,
    isLoading: false,
    error: null,
    lastResponse: null, // 디버깅용 마지막 응답 저장
    pagination: {
      currentPage: 0,
      totalPages: 0,
      totalElements: 0,
      size: 10
    }
  }),
  
  getters: {
    // 읽지 않은 알림만 필터링
    unreadNotifications: (state) => {
      return state.notifications.filter(notification => !notification.read);
    },
    
    // 읽은 알림만 필터링
    readNotifications: (state) => {
      return state.notifications.filter(notification => notification.read);
    },
  },
  
  actions: {
    // 알림 목록 조회
    async fetchNotificationsAction(params) {
      this.isLoading = true;
      this.error = null;
      
      try {
        const response = await notificationApi.getNotificationList(params);
        this.lastResponse = response; // 디버깅용 응답 저장
        
        if (response.data && response.data.success) {
          const data = response.data.data;
          this.notifications = data.content || [];
          
          // 페이지네이션 정보 업데이트
          this.pagination = {
            currentPage: data.number || 0,
            totalPages: data.totalPages || 0,
            totalElements: data.totalElements || 0,
            size: data.size || 10
          };
          
          // 페이징 시에는 개수 업데이트를 하지 않음
          // 읽음 처리 후에만 개수 업데이트
          return response;
        } else {
          console.warn('Notification API response not successful:', response);
        }
      } catch (err) {
        console.error('Error fetching notifications:', err);
        this.error = err.message || '알림 목록을 불러오는데 실패했습니다.';
        this.notifications = [];
      } finally {
        this.isLoading = false;
      }
    },
    
    // 읽지 않은 알림 개수 조회
    async fetchUnreadCountAction() {
      this.error = null;
      
      try {
        // API 호출 시도
        const response = await notificationApi.getUnreadNotificationCount();
        this.lastResponse = response; // 디버깅용 응답 저장
        
        if (response.data && response.data.success) {
          // API 결과가 성공적으로 돌아오면 API 결과 사용
          this.unreadCount = response.data.data.unreadCount || 0;
        } else {
        }
      } catch (err) {
        console.error('Error fetching unread count:', err);
        this.error = err.message || '읽지 않은 알림 개수를 불러오는데 실패했습니다.';
      }
      return this.unreadCount;
    },
    
    // 로컬 상태의 읽지 않은 알림 개수 업데이트 (API 호출 없이)
    updateUnreadCount() {
      this.unreadCount = this.notifications.filter(notification => !notification.read).length;
    },
    
    // 알림 읽음 처리
    async markNotificationAsReadAction(notificationId) {
      this.error = null;
      
      try {
        await notificationApi.markNotificationAsRead(notificationId);
        
        // 로컬 상태 업데이트
        const index = this.notifications.findIndex(notification => notification.notificationId === notificationId);
        if (index !== -1) {
          this.notifications[index].read = true;
        }
        
        // 읽지 않은 알림 개수 다시 가져오기
        await this.fetchUnreadCountAction();
      } catch (err) {
        this.error = err.message || '알림 읽음 처리에 실패했습니다.';
        throw err;
      }
    },
    
    // 모든 알림 읽음 처리
    async markAllAsReadAction() {
      this.error = null;
      const unreadNotificationIds = this.unreadNotifications.map(notification => notification.notificationId);
      
      if (unreadNotificationIds.length === 0) return;
      
      try {
        await notificationApi.markAllNotificationsAsRead();
        
        // 로컬 상태 업데이트
        this.notifications.forEach(notification => {
          if (!notification.read) {
            notification.read = true;
          }
        });
        
        // 읽지 않은 알림 개수 다시 가져오기
        await this.fetchUnreadCountAction();
      } catch (err) {
        this.error = err.message || '알림 읽음 처리에 실패했습니다.';
        throw err;
      }
    },
    
    // 새 알림 추가 (WebSocket 또는 주기적 폴링으로 새 알림 수신 시)
    addNewNotification(notification) {
      this.notifications.unshift(notification);
      if (!notification.read) {
        this.unreadCount += 1;
      }
    },
  },
});
