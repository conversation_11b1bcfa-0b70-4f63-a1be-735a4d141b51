import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 프로젝트별 이벤트 리스트를 조회합니다.
 * @param {number|string} projectId - 프로젝트 ID
 * @returns {Promise<import('axios').AxiosResponse>} 이벤트 리스트 응답
 */
export const getEvents = async (projectId) => {
  const response = await apiClient.get('/events', { params: { projectId } });
  // API 응답 data 구조: { success: boolean, data: { content: [...], totalElements, totalPages, ... } }
  if (response.data && response.data.success && response.data.data) {
    // 페이지네이션 형식의 응답에서 content 배열 추출
    if (response.data.data.content && Array.isArray(response.data.data.content)) {
      return response.data.data.content;
    }
    // 이전 버전과의 호환성을 위해 items 배열도 확인
    if (response.data.data.items && Array.isArray(response.data.data.items)) {
      return response.data.data.items;
    }
    // 데이터가 직접 배열인 경우
    if (Array.isArray(response.data.data)) {
      return response.data.data;
    }
    // 데이터가 없는 경우 빈 배열 반환
    return [];
  }
  throw new Error(response.data?.error?.message || response.data?.message || '이벤트 목록을 가져오는 데 실패했습니다.');
};

/**
 * 이벤트를 복사합니다.
 * @param {number|string} eventId - 복사할 이벤트 ID
 * @returns {Promise<Object>} 복사된 이벤트 정보 또는 성공 여부
 */
export const copyEvent = async (eventId) => {
  try {
    const response = await apiClient.post(`/events/${eventId}/copy`);
    if (response.data && response.data.success) {
      return response.data.data || response.data;
    }
    throw new Error(response.data?.error?.message || response.data?.message || '이벤트 복사에 실패했습니다.');
  } catch (error) {
    // handleApiError는 메시지 문자열만 반환하민로, 이 메시지로 새 Error 객체를 생성하여 던집니다.
    const errorMessage = handleApiError(error, '이벤트 복사에 실패했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트별 이벤트 목록을 페이지네이션과 함께 가져옵니다.
 * @param {number|string} projectId - 프로젝트 ID
 * @param {number} page - 페이지 번호 (0부터 시작)
 * @param {number} size - 페이지당 항목 수
 * @param {Object} searchParams - 검색 파라미터 (searchType, searchKeyword)
 * @returns {Promise<Object>} 이벤트 목록을 포함한 응답 객체 Promise
 */
export const getEventsPaginated = async (projectId, page = 0, size = 10, searchParams = {}) => {
  try {
    // 프로젝트 ID가 없는 경우 에러 발생
    if (!projectId) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }

    // 페이지네이션 파라미터 추가
    const params = {
      projectId,
      page,
      size,
      sort: 'createDate,desc'
    };

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/events`, { params }),
      '이벤트 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '이벤트 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * SUPER_ADMIN용 모든 이벤트 목록을 가져옵니다.
 * @param {number} page - 페이지 번호 (0부터 시작)
 * @param {number} size - 페이지당 항목 수
 * @param {number} [projectId] - 선택적 프로젝트 ID (특정 프로젝트로 필터링하는 경우)
 * @param {Object} searchParams - 검색 파라미터 (searchType, searchKeyword)
 * @returns {Promise<Object>} 이벤트 목록을 포함한 응답 객체 Promise
 */
export const getAllEvents = async (page = 0, size = 10, projectId = null, searchParams = {}) => {
  try {
    // 페이지네이션 파라미터 추가
    const params = {
      page,
      size,
      sort: 'createDate,desc'
    };

    // 프로젝트 ID가 있는 경우에만 파라미터에 추가
    if (projectId) {
      params.projectId = projectId;
    }

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/super/events/all`, { params }),
      '이벤트 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '이벤트 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새로운 이벤트를 생성합니다.
 * @param {object} eventData - 생성할 이벤트 데이터
 * @returns {Promise<import('axios').AxiosResponse>} 생성된 이벤트 응답
 */
export const createEvent = async (eventData) => {
  // FormData 전송 시 multipart/form-data headers 및 transformRequest 설정
  const config = eventData instanceof FormData
    ? {
        headers: { 'Content-Type': 'multipart/form-data' },
        transformRequest: [(data, headers) => {
          // 정확한 Content-Type 헤더 설정 (boundary 자동 포함)
          headers['Content-Type'] = 'multipart/form-data';
          return data;
        }]
      }
    : {};
  return await apiClient.post('/events', eventData, config);
};

// 단일 이벤트 상세 조회
export const getEvent = async (eventId) => {
  try {
    if (!eventId) throw new Error('이벤트 ID가 필요합니다.');
    const response = await apiClient.get(`/events/${eventId}`);
    if (response.data && response.data.success) return response.data.data;
    throw new Error(response.data?.error?.message || '이벤트 조회에 실패했습니다.');
  } catch (error) {
    const msg = handleApiError(error, '이벤트 상세 조회 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};

// 이벤트 수정
export const updateEvent = async (eventId, data) => {
  try {
    if (!eventId) throw new Error('이벤트 ID가 필요합니다.');
    // FormData 전송 시 multipart/form-data headers 및 transformRequest 설정
    const config = data instanceof FormData
      ? {
          headers: { 'Content-Type': 'multipart/form-data' },
          transformRequest: [(data, headers) => {
            headers['Content-Type'] = 'multipart/form-data';
            return data;
          }]
        }
      : {};
    const response = await apiClient.put(`/events/${eventId}`, data, config);
    if (response.data && response.data.success) return response.data.data;
    throw new Error(response.data?.error?.message || '이벤트 수정에 실패했습니다.');
  } catch (error) {
    const msg = handleApiError(error, '이벤트 수정 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};

// 이벤트 삭제
export const deleteEvent = async (eventId) => {
  try {
    if (!eventId) throw new Error('이벤트 ID가 필요합니다.');
    await apiClient.delete(`/events/${eventId}`);
  } catch (error) {
    const msg = handleApiError(error, '이벤트 삭제 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};

/**
 * 이벤트의 혜택을 삭제합니다.
 * @param {number|string} eventId - 이벤트 ID
 * @param {number|string} benefitId - 혜택 ID
 * @returns {Promise<void>}
 */
export const deleteEventBenefit = async (eventId, benefitId) => {
  try {
    if (!eventId) throw new Error('이벤트 ID가 필요합니다.');
    if (!benefitId) throw new Error('혜택 ID가 필요합니다.');

    const response = await apiClient.delete(`/events/${eventId}/benefits/${benefitId}`);

    if (response.data && response.data.success) {
      return response.data;
    }

    throw new Error(response.data?.error?.message || '혜택 삭제에 실패했습니다.');
  } catch (error) {
    const msg = handleApiError(error, '혜택 삭제 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};
