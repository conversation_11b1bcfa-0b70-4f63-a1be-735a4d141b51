import apiClient from './index';

// 사용자별 알림 목록 조회
export const getNotificationList = async (params = { page: 0, size: 10, sort: 'createdAt,desc' }) => {
  return apiClient.get('/notifications/list', { params });
};

// 알림 읽음 처리
export const markNotificationAsRead = async (notificationId) => {
  return apiClient.put(`/notifications/${notificationId}/read`);
};

// 읽지 않은 알림 개수 조회
export const getUnreadNotificationCount = async () => {
  return apiClient.get('/notifications/noread-count');
};

// 모든 알림 읽음 처리
export const markAllNotificationsAsRead = async () => {
  return apiClient.put('/notifications/read-all');
};
