<template>
  <div
    class="button-element element"
    :class="{ 'selected': isSelected, 'hover-effect': !isSelected && !isDragging, 'dragging': isDragging, 'resizing': isResizing }"
    :style="elementStyle"
    @click.stop="handleSelect"
    @mousedown.stop="startDrag"
    @mouseover="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <img
      v-if="element.content.icon && element.content.icon.src"
      :src="element.content.icon.src"
      :class="['button-icon', `icon-${element.content.icon.position || 'left'}`]"
      alt="Button Icon"
      @error="handleImageError"
    />
    <span class="button-text" :style="buttonTextStyle">{{ element.content.text }}</span>

    <!-- 리사이즈 핸들 -->
    <template v-if="isSelected">
      <div class="resize-handle top-left" @mousedown.stop="(e) => startResize(e, 'top-left')"></div>
      <div class="resize-handle top" @mousedown.stop="(e) => startResize(e, 'top')"></div>
      <div class="resize-handle top-right" @mousedown.stop="(e) => startResize(e, 'top-right')"></div>
      <div class="resize-handle right" @mousedown.stop="(e) => startResize(e, 'right')"></div>
      <div class="resize-handle bottom-right" @mousedown.stop="(e) => startResize(e, 'bottom-right')"></div>
      <div class="resize-handle bottom" @mousedown.stop="(e) => startResize(e, 'bottom')"></div>
      <div class="resize-handle bottom-left" @mousedown.stop="(e) => startResize(e, 'bottom-left')"></div>
      <div class="resize-handle left" @mousedown.stop="(e) => startResize(e, 'left')"></div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useDraggable } from '@/composables/useDraggable';
import { useResizable } from '@/composables/useResizable';
import { useImageError } from '@/composables/useImageError';

// 이미지 에러 핸들링
const { handleImageError } = useImageError();

// 프롭스
const props = defineProps({
  element: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

// 이벤트
const emit = defineEmits(['select', 'update', 'positionUpdate']);

// 상태
const isHovered = ref(false);

// 버튼 텍스트 스타일 (높이가 작을 때 텍스트 위치 조정)
const buttonTextStyle = computed(() => {
  const { size } = props.element;

  // 높이 값 가져오기
  let height = 0;
  if (size && size.height) {
    if (typeof size.height === 'object' && size.height.value) {
      height = Number(size.height.value);
    } else if (typeof size.height === 'number') {
      height = size.height;
    }
  }

  // 높이가 20px 미만인 경우 텍스트 위치 조정
  if (height < 20) {
    return {
      position: 'absolute',
      top: '20px',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      padding: '2px 5px',
      borderRadius: '3px',
      fontSize: '12px',
      zIndex: 10
    };
  }

  return {};
});

// 요소 스타일
const elementStyle = computed(() => {
  const { position, size, style, content, zIndex } = props.element;
  const { textStyle, hoverStyle } = content;

  // 호버 상태에 따른 스타일 적용
  const backgroundColor = isHovered.value && hoverStyle?.backgroundColor
    ? hoverStyle.backgroundColor
    : style.backgroundColor;

  const textColor = isHovered.value && hoverStyle?.color
    ? hoverStyle.color
    : textStyle?.color;

  // 위치 단위 처리
  const getPositionValue = (pos) => {
    if (!pos || !pos.value) return '0px';
    return pos.unit === 'px' ? `${pos.value}px` :
           pos.unit === '%' ? `${pos.value}%` :
           pos.unit === 'vw' ? `${pos.value}vw` :
           pos.unit === 'vh' ? `${pos.value}vh` :
           `${pos.value}px`;
  };

  // 크기 단위 처리
  const getSizeValue = (sz) => {
    if (!sz || !sz.value) return 'auto';
    if (sz.value === 'auto') return 'auto';
    return sz.unit === 'px' ? `${sz.value}px` :
           sz.unit === '%' ? `${sz.value}%` :
           sz.unit === 'vw' ? `${sz.value}vw` :
           sz.unit === 'vh' ? `${sz.value}vh` :
           `${sz.value}px`;
  };

  // 높이 값 가져오기
  let height = 0;
  if (size && size.height) {
    if (typeof size.height === 'object' && size.height.value) {
      height = Number(size.height.value);
    } else if (typeof size.height === 'number') {
      height = size.height;
    }
  }

  // 높이가 작을 때 패딩 조정
  const buttonPadding = height < 17 ? '0px' : (style.padding || '8px 16px');

  return {
    position: 'absolute',
    left: getPositionValue(position.x),
    top: getPositionValue(position.y),
    width: getSizeValue(size.width),
    height: getSizeValue(size.height),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: backgroundColor || '#3498db',
    opacity: style.opacity,
    transform: style.rotation ? `rotate(${style.rotation}deg)` : 'none',
    borderWidth: `${style.borderWidth}px`,
    borderStyle: style.borderWidth > 0 ? (style.borderStyle || 'solid') : 'none',
    borderColor: style.borderColor || '#000000',
    borderRadius: `${style.borderRadius}px`,
    padding: buttonPadding,
    margin: style.margin || '0',
    boxShadow: style.boxShadow || 'none',
    fontSize: textStyle?.fontSize ? `${textStyle.fontSize}px` : '16px',
    fontFamily: textStyle?.fontFamily || 'Arial, sans-serif',
    fontWeight: textStyle?.fontWeight || 'bold',
    color: textColor || '#ffffff',
    zIndex,
    cursor: 'pointer',
    userSelect: 'none',
    transition: 'background-color 0.2s, color 0.2s',
    boxSizing: 'border-box',
    lineHeight: height < 17 ? '0' : 'normal'
  };
});

// 선택 핸들러
const handleSelect = () => {
  emit('select', props.element.id);
};

// 드래그 기능 사용
const { isDragging, startDrag } = useDraggable(props, emit, handleSelect);

// 리사이즈 기능 사용
const { isResizing, startResize } = useResizable(props, emit);
</script>

<style scoped>
.button-element {
  min-width: 1px;
  min-height: 1px;
  text-align: center;
  padding: 0 !important;
  box-sizing: border-box !important;
}

.button-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
  padding: 0;
  margin: 0;
}

.button-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.icon-left {
  margin-right: 8px;
}

.icon-right {
  margin-left: 8px;
  order: 1;
}

.element.selected {
  outline: 2px solid #2196F3;
  outline-offset: 2px;
}

/* 호버 효과는 선택되지 않은 상태에서만 적용 */
.hover-effect:hover {
  filter: brightness(1.05);
}

.element.dragging {
  opacity: 0.8;
  cursor: move;
  z-index: 9999 !important;
}

.element.resizing {
  opacity: 0.8;
  z-index: 9999 !important;
}

/* 리사이즈 핸들 스타일 */
.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #2196F3;
  border: 1px solid #fff;
  border-radius: 50%;
  z-index: 10;
}

.top-left {
  top: -4px;
  left: -4px;
  cursor: nwse-resize;
}

.top {
  top: -4px;
  left: calc(50% - 4px);
  cursor: ns-resize;
}

.top-right {
  top: -4px;
  right: -4px;
  cursor: nesw-resize;
}

.right {
  top: calc(50% - 4px);
  right: -4px;
  cursor: ew-resize;
}

.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: nwse-resize;
}

.bottom {
  bottom: -4px;
  left: calc(50% - 4px);
  cursor: ns-resize;
}

.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: nesw-resize;
}

.left {
  top: calc(50% - 4px);
  left: -4px;
  cursor: ew-resize;
}
</style>
