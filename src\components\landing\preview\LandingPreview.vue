<template>
  <div class="landing-preview" :style="containerStyle">
    <!-- 배경 설정 -->
    <div class="preview-background" :style="backgroundStyle">
      <img
        v-if="backgroundImage"
        :src="backgroundImage"
        :style="backgroundImageStyle"
        class="background-image"
        alt="배경 이미지"
        @error="handleImageError"
      />
    </div>

    <!-- 요소 렌더링 -->
    <template v-for="element in sortedElements" :key="element.id">
      <!-- 텍스트 요소 -->
      <div
        v-if="element.type === 'text'"
        class="preview-element preview-text"
        :style="getTextStyle(element)"
        :data-element-id="element.id"
        v-html="formatTextContent(element.content.text)"
      ></div>

      <!-- 이미지 요소 -->
      <div
        v-else-if="element.type === 'image'"
        class="preview-element preview-image"
        :style="getElementStyle(element)"
        :data-element-id="element.id"
      >
        <img
          v-if="element.content.src"
          :src="element.content.src"
          :alt="element.content.alt || '이미지'"
          :style="getImageStyle(element)"
          @error="handleImageError"
        />
      </div>

      <!-- 버튼 요소 -->
      <div
        v-else-if="element.type === 'button'"
        class="preview-element preview-button"
        :style="getButtonStyle(element)"
        :data-element-id="element.id"
        @click="handleButtonClick(element)"
        @mouseover="handleButtonHover(element, true)"
        @mouseleave="handleButtonHover(element, false)"
      >
        <img
          v-if="element.content.icon && element.content.icon.src"
          :src="element.content.icon.src"
          class="button-icon"
          :class="[`icon-${element.content.icon.position || 'left'}`]"
          alt="버튼 아이콘"
          @error="handleImageError"
        />
        <span
          class="button-text"
          :style="getButtonTextStyle(element)"
        >{{ element.content.text }}</span>
      </div>

      <!-- 링크 요소 -->
      <a
        v-else-if="element.type === 'link'"
        class="preview-element preview-link"
        :style="getLinkStyle(element)"
        :data-element-id="element.id"
        :href="element.content.url"
        :target="element.content.newTab ? '_blank' : '_self'"
        @mouseover="handleLinkHover(element, true)"
        @mouseleave="handleLinkHover(element, false)"
      >
        {{ element.content.text }}
      </a>

      <!-- DIV 블록 요소 -->
      <div
        v-else-if="element.type === 'div'"
        class="preview-element preview-div"
        :style="getDivStyle(element)"
        :data-element-id="element.id"
      ></div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useImageError } from '@/composables/useImageError';

// 이미지 에러 핸들링
const { handleImageError } = useImageError();
// 프롭스
const props = defineProps({
  landingPage: {
    type: Object,
    required: true
  },
  scale: {
    type: Number,
    default: 1
  }
});

// 상태
const isButtonHovered = ref({});
const isLinkHovered = ref({});

// 요소 ID마다 호버 상태 초기화
const initHoverStates = () => {
  if (props.landingPage && props.landingPage.elements) {
    props.landingPage.elements.forEach(element => {
      if (element.type === 'button') {
        isButtonHovered.value[element.id] = false;
      } else if (element.type === 'link') {
        isLinkHovered.value[element.id] = false;
      }
    });
  }
};

// 랜딩 페이지 변경 시 호버 상태 초기화
watch(() => props.landingPage, () => {
  initHoverStates();
}, { immediate: true });

// 컨테이너 스타일
const containerStyle = computed(() => {
  if (!props.landingPage || !props.landingPage.canvas) return {};

  const { width, height } = props.landingPage.canvas;

  // 스크롤바 너비를 고려하지 않고 원래 너비 그대로 사용
  // 부모 컨테이너에서 이미 스크롤바 너비를 고려하여 너비를 조정함
  return {
    width: `${width}px`,
    height: `${height * props.scale}px`,
    transform: `scale(${props.scale})`,
    transformOrigin: 'top left',
    minWidth: `${width}px`,
    boxSizing: 'border-box' // 테두리와 패딩이 너비에 포함되도록 설정
  };
});

// 배경 이미지 URL
const backgroundImage = computed(() => {
  if (!props.landingPage || !props.landingPage.canvas) return null;
  return props.landingPage.canvas.backgroundImage;
});

// 배경 스타일
const backgroundStyle = computed(() => {
  if (!props.landingPage || !props.landingPage.canvas) return {};

  const { backgroundColor } = props.landingPage.canvas;

  return {
    backgroundColor: backgroundColor || '#ffffff',
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 0
  };
});

// 배경 이미지 스타일
const backgroundImageStyle = computed(() => {
  if (!props.landingPage || !props.landingPage.canvas) return {};

  const { backgroundFit } = props.landingPage.canvas;

  return {
    width: '100%',
    height: '100%',
    objectFit: backgroundFit || 'cover',
    objectPosition: 'center'
  };
});

// Z-인덱스 기준으로 정렬된 요소 목록
const sortedElements = computed(() => {
  if (!props.landingPage || !props.landingPage.elements) return [];

  return [...props.landingPage.elements]
    .filter(el => el.visible !== false)
    .sort((a, b) => a.zIndex - b.zIndex);
});

// 요소 스타일 계산
const getElementStyle = (element) => {
  const { position, size, style } = element;

  // 위치 단위 처리
  const getPositionValue = (pos) => {
    if (!pos || !pos.value) return '0px';
    return pos.unit === 'px' ? `${pos.value}px` :
           pos.unit === '%' ? `${pos.value}%` :
           pos.unit === 'vw' ? `${pos.value}vw` :
           pos.unit === 'vh' ? `${pos.value}vh` :
           `${pos.value}px`;
  };

  // 크기 단위 처리
  const getSizeValue = (sz) => {
    if (!sz || !sz.value) return 'auto';
    if (sz.value === 'auto') return 'auto';
    return sz.unit === 'px' ? `${sz.value}px` :
           sz.unit === '%' ? `${sz.value}%` :
           sz.unit === 'vw' ? `${sz.value}vw` :
           sz.unit === 'vh' ? `${sz.value}vh` :
           `${sz.value}px`;
  };

  return {
    position: 'absolute',
    left: getPositionValue(position.x),
    top: getPositionValue(position.y),
    width: getSizeValue(size.width),
    height: getSizeValue(size.height),
    backgroundColor: style.backgroundColor || 'transparent', // 배경색이 없으면 투명하게 처리
    opacity: style.opacity,
    transform: style.rotation ? `rotate(${style.rotation}deg)` : 'none',
    borderWidth: `${style.borderWidth}px`,
    borderStyle: style.borderWidth > 0 ? (style.borderStyle || 'solid') : 'none',
    borderColor: style.borderColor || '#000000',
    borderRadius: `${style.borderRadius}px`,
    padding: style.padding || '0',
    margin: style.margin || '0',
    boxShadow: style.boxShadow || 'none',
    overflow: element.type === 'div' ? (element.content.overflow || 'visible') : 'visible',
    zIndex: element.zIndex
  };
};

// 텍스트 스타일 계산
const getTextStyle = (element) => {
  const baseStyle = getElementStyle(element);
  const { content } = element;

  // 텍스트 요소의 배경색이 하양색인 경우 투명하게 처리하지 않고 유지
  return {
    ...baseStyle,
    backgroundColor: baseStyle.backgroundColor, // 배경색 그대로 유지
    fontSize: content.fontSize ? `${content.fontSize}px` : '16px',
    fontFamily: content.fontFamily || 'Arial, sans-serif',
    fontWeight: content.fontWeight || 'normal',
    fontStyle: content.fontStyle || 'normal',
    textAlign: content.textAlign || 'left',
    color: content.color || '#000000',
    lineHeight: content.lineHeight || 1.5,
    letterSpacing: content.letterSpacing || 'normal'
  };
};

// 이미지 스타일 계산
const getImageStyle = (element) => {
  const { content } = element;

  return {
    width: '100%',
    height: '100%',
    objectFit: content.objectFit || 'contain',
    filter: content.filter || 'none'
  };
};

// 버튼 호버 이벤트 핸들러
const handleButtonHover = (element, isHovered) => {
  if (!element.content.hoverStyle) return;

  // 호버 상태 저장
  isButtonHovered.value[element.id] = isHovered;

  // 호버 스타일 적용
  const buttonElement = document.querySelector(`[data-element-id="${element.id}"]`);
  if (!buttonElement) return;

  if (isHovered && element.content.hoverStyle) {
    // 호버 스타일 적용
    if (element.content.hoverStyle.backgroundColor) {
      buttonElement.style.backgroundColor = element.content.hoverStyle.backgroundColor;
    }

    if (element.content.hoverStyle.color) {
      buttonElement.style.color = element.content.hoverStyle.color;
    }
  } else {
    // 기본 스타일로 돌아가기
    const baseStyle = getElementStyle(element);
    buttonElement.style.backgroundColor = baseStyle.backgroundColor || '#3498db'; // 배경색이 없으면 기본 파란색 사용

    const { textStyle } = element.content;
    buttonElement.style.color = (textStyle && textStyle.color) || '#ffffff';
  }
};

// 링크 호버 이벤트 핸들러
const handleLinkHover = (element, isHovered) => {
  if (!element.content.hoverStyle) return;

  // 호버 상태 저장
  isLinkHovered.value[element.id] = isHovered;

  // 호버 스타일 적용
  const linkElement = document.querySelector(`[data-element-id="${element.id}"]`);
  if (!linkElement) return;

  if (isHovered && element.content.hoverStyle) {
    // 호버 스타일 적용
    if (element.content.hoverStyle.color) {
      linkElement.style.color = element.content.hoverStyle.color;
    }

    if (element.content.hoverStyle.textDecoration) {
      linkElement.style.textDecoration = element.content.hoverStyle.textDecoration;
    }
  } else {
    // 기본 스타일로 돌아가기
    const { textStyle } = element.content;
    linkElement.style.color = textStyle.color || '#3498db';
    linkElement.style.textDecoration = textStyle.textDecoration || 'underline';
  }
};

// 버튼 스타일 계산
const getButtonStyle = (element) => {
  const baseStyle = getElementStyle(element);
  const { content, size } = element;
  const { textStyle } = content;

  // 높이 값 가져오기
  let height = 0;
  if (size && size.height) {
    if (typeof size.height === 'object' && size.height.value) {
      height = Number(size.height.value);
    } else if (typeof size.height === 'number') {
      height = size.height;
    }
  }

  // 높이가 작을 때 패딩 조정
  const buttonPadding = height < 17 ? '0px' : (element.style?.padding || '8px 16px');

  return {
    ...baseStyle,
    display: height < 17 ? 'block' : 'flex',
    alignItems: height < 17 ? 'initial' : 'center',
    justifyContent: height < 17 ? 'initial' : 'center',
    backgroundColor: baseStyle.backgroundColor || '#3498db', // 버튼은 기본적으로 파란색 배경색 사용
    color: (textStyle && textStyle.color) || '#ffffff',
    fontSize: textStyle && textStyle.fontSize ? `${textStyle.fontSize}px` : '16px',
    fontFamily: textStyle && textStyle.fontFamily || 'Arial, sans-serif',
    fontWeight: textStyle && textStyle.fontWeight || 'bold',
    cursor: 'pointer',
    transition: 'background-color 0.3s, color 0.3s', // 스무스한 호버 효과
    padding: buttonPadding,
    boxSizing: 'border-box',
    lineHeight: height < 17 ? '0' : 'normal',
    minHeight: height < 17 ? '1px' : 'auto',
    overflow: 'hidden'
  };
};

// 버튼 텍스트 스타일 계산
const getButtonTextStyle = (element) => {
  const { size } = element;

  // 높이 값 가져오기
  let height = 0;
  if (size && size.height) {
    if (typeof size.height === 'object' && size.height.value) {
      height = Number(size.height.value);
    } else if (typeof size.height === 'number') {
      height = size.height;
    }
  }

  // 높이가 작을 때 텍스트 숨김
  if (height < 17) {
    return {
      display: 'none'
    };
  }

  return {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  };
};

// 링크 스타일 계산
const getLinkStyle = (element) => {
  const baseStyle = getElementStyle(element);
  const { content } = element;
  const { textStyle } = content;

  // 최종 스타일 반환
  return {
    ...baseStyle,
    textDecoration: textStyle.textDecoration || 'underline',
    color: textStyle.color || '#3498db',
    fontSize: textStyle.fontSize ? `${textStyle.fontSize}px` : '16px',
    fontFamily: textStyle.fontFamily || 'Arial, sans-serif',
    fontWeight: textStyle.fontWeight || 'normal',
    cursor: 'pointer',
    transition: 'color 0.3s, text-decoration 0.3s' // 스무스한 호버 효과
  };
};

// DIV 요소 스타일 계산
const getDivStyle = (element) => {
  const baseStyle = getElementStyle(element);
  const { content } = element;

  // DIV 요소의 배경색이 하양색인 경우 투명하게 처리하지 않고 유지
  return {
    ...baseStyle,
    backgroundColor: baseStyle.backgroundColor, // 배경색 그대로 유지
    overflow: content.overflow || 'visible'
  };
};

// 텍스트 내용 포맷팅 (줄바꿈 처리)
const formatTextContent = (text) => {
  if (!text) return '';
  return text.replace(/\n/g, '<br>');
};

// 버튼 클릭 핸들러
const handleButtonClick = (element) => {
  const { action } = element.content;

  if (!action) return;

  console.log(element.content)

  switch (action.type) {
    case 'link':
      if (action.target) { // action.target이 존재하는 경우
        let url = action.target; // action.target 값을 url 변수에 할당

        // "https;//" 또는 "http;//" 같은 일반적인 오타를 수정합니다.
        if (url.startsWith('https;//')) {
          url = 'https://' + url.substring(8);
        } else if (url.startsWith('http;//')) {
          url = 'http://' + url.substring(7);
        }

        if (action.newTab) {
          window.open(url, '_blank'); // 새 탭에서 링크 열기
        } else {
          window.location.href = url; // 현재 탭에서 링크 열기
        }
      }
      break;
    case 'scroll':
      // 스크롤 동작 구현
      if (action.target) {
        // 타겟이 ID인 경우 (#으로 시작)
        if (action.target.startsWith('#')) {
          const targetElement = document.querySelector(action.target);
          if (targetElement) {
            // 부드러운 스크롤 효과 적용
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
          } else {
            console.warn(`스크롤 타겟을 찾을 수 없음: ${action.target}`);
          }
        }
        // 타겟이 요소 ID인 경우 (랜딩 페이지 요소)
        else if (props.landingPage && props.landingPage.elements) {
          // 요소 ID로 요소 찾기
          const targetElement = props.landingPage.elements.find(el => el.id === action.target);
          if (targetElement) {
            // 요소의 DOM 요소 찾기
            const targetDomElement = document.querySelector(`[data-element-id="${action.target}"]`);
            if (targetDomElement) {
              // 부드러운 스크롤 효과 적용 (요소의 윗부분으로 스크롤)
              targetDomElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
              console.warn(`스크롤 타겟 DOM 요소를 찾을 수 없음: ${action.target}`);
            }
          } else {
            console.warn(`스크롤 타겟 요소를 찾을 수 없음: ${action.target}`);
          }
        }
        // 타겟이 픽셀 값인 경우
        else if (!isNaN(parseInt(action.target))) {
          const scrollY = parseInt(action.target);
          window.scrollTo({
            top: scrollY,
            behavior: 'smooth'
          });
        }
      }
      break;
    case 'popup':
      // 팝업 동작 구현
      if (action.target) {
        // 팝업 내용 표시
        showPopup(action.target, element.content.text);
      }
      break;
    default:
      break;
  }
};

// 팝업 표시 함수
const showPopup = (content, title) => {
  // 기존 팝업이 있으면 제거
  const existingPopup = document.querySelector('.landing-popup-overlay');
  if (existingPopup) {
    document.body.removeChild(existingPopup);
  }

  // 팝업 오버레이 생성
  const popupOverlay = document.createElement('div');
  popupOverlay.className = 'landing-popup-overlay';
  popupOverlay.style.position = 'fixed';
  popupOverlay.style.top = '0';
  popupOverlay.style.left = '0';
  popupOverlay.style.width = '100%';
  popupOverlay.style.height = '100%';
  popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  popupOverlay.style.display = 'flex';
  popupOverlay.style.justifyContent = 'center';
  popupOverlay.style.alignItems = 'center';
  popupOverlay.style.zIndex = '9999';

  // 팝업 컨테이너 생성
  const popupContainer = document.createElement('div');
  popupContainer.className = 'landing-popup-container';
  popupContainer.style.backgroundColor = '#fff';
  popupContainer.style.borderRadius = '8px';
  popupContainer.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  popupContainer.style.padding = '20px';
  popupContainer.style.maxWidth = '90%';
  popupContainer.style.maxHeight = '80%';
  popupContainer.style.overflow = 'auto';
  popupContainer.style.position = 'relative';

  // 팝업 헤더 생성
  const popupHeader = document.createElement('div');
  popupHeader.className = 'landing-popup-header';
  popupHeader.style.display = 'flex';
  popupHeader.style.justifyContent = 'space-between';
  popupHeader.style.alignItems = 'center';
  popupHeader.style.marginBottom = '15px';

  // 팝업 제목 생성
  const popupTitle = document.createElement('h3');
  popupTitle.className = 'landing-popup-title';
  popupTitle.textContent = title || '알림';
  popupTitle.style.margin = '0';
  popupTitle.style.fontSize = '18px';
  popupTitle.style.fontWeight = 'bold';

  // 닫기 버튼 생성
  const closeButton = document.createElement('button');
  closeButton.className = 'landing-popup-close';
  closeButton.textContent = '×';
  closeButton.style.background = 'none';
  closeButton.style.border = 'none';
  closeButton.style.fontSize = '24px';
  closeButton.style.cursor = 'pointer';
  closeButton.style.padding = '0';
  closeButton.style.lineHeight = '1';
  closeButton.style.color = '#666';
  closeButton.onclick = () => {
    document.body.removeChild(popupOverlay);
  };

  // 팝업 내용 생성
  const popupContent = document.createElement('div');
  popupContent.className = 'landing-popup-content';
  popupContent.innerHTML = content;

  // 팝업 구성
  popupHeader.appendChild(popupTitle);
  popupHeader.appendChild(closeButton);
  popupContainer.appendChild(popupHeader);
  popupContainer.appendChild(popupContent);
  popupOverlay.appendChild(popupContainer);

  // 오버레이 클릭 시 팝업 닫기
  popupOverlay.addEventListener('click', (e) => {
    if (e.target === popupOverlay) {
      document.body.removeChild(popupOverlay);
    }
  });

  // 팝업 표시
  document.body.appendChild(popupOverlay);
};
</script>

<style scoped>
.landing-preview {
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  box-sizing: border-box; /* 테두리와 패딩이 너비에 포함되도록 설정 */
  min-width: 100%; /* 부모 컨테이너의 너비를 최소한 차지하도록 설정 */
}

.preview-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.background-image {
  display: block;
  width: 100%;
  height: 100%;
}

.preview-element {
  pointer-events: auto;
}

.preview-text {
  word-break: break-word;
}

.preview-button {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s, color 0.3s;
  min-height: 1px !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

/* 버튼 호버 스타일 */
.button-with-hover:hover {
  background-color: var(--hover-bg) !important;
  color: var(--hover-color) !important;
}

.button-with-hover {
  --hover-bg: attr(data-hover-bg);
  --hover-color: attr(data-hover-color);
}

.button-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
  padding: 0;
  margin: 0;
}

.button-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.icon-left {
  margin-right: 8px;
}

.icon-right {
  margin-left: 8px;
  order: 1;
}

.preview-link {
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.3s, text-decoration 0.3s;
}

/* 링크 호버 스타일 */
.link-with-hover:hover {
  color: var(--hover-color) !important;
  text-decoration: var(--hover-decoration) !important;
}

.link-with-hover {
  --hover-color: attr(data-hover-color);
  --hover-decoration: attr(data-hover-decoration);
}

.preview-div {
  /* DIV 블록 요소 스타일 */
}
</style>
