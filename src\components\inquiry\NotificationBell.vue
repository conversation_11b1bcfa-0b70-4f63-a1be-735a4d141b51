<template>
    <!-- <div class="notification-bell">
      <div class="dropdown">
        <a class="notification-icon-wrapper" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false" @click="markAsRead">
          <i class="bi bi-bell"></i>
          <span v-if="unreadCount > 0" class="notification-badge">{{ displayCount }}</span>
        </a>
        
        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
          <li class="dropdown-header">알림</li>
          
          
          <li v-if="isLoading" class="dropdown-item text-center">
            <div class="spinner-border spinner-border-sm" role="status">
              <span class="visually-hidden">로딩 중...</span>
            </div>
          </li>
          
      
          <li v-else-if="notifications.length === 0" class="dropdown-item disabled">
            <span>알림이 없습니다</span>
          </li>
          
          
          <template v-else>
            <li v-for="notification in notifications" :key="notification.id" class="dropdown-item">
              <router-link :to="notification.link" class="notification-item" :class="{ 'unread': !notification.read }">
                <div class="notification-content">{{ notification.message }}</div>
                <div class="notification-time">{{ formatDate(notification.createdAt) }}</div>
              </router-link>
            </li>
          </template>
          
          <li><hr class="dropdown-divider"></li>
          <li v-if="notifications.length > 0" class="dropdown-item text-center">
            <button @click="markAllAsRead" class="btn btn-sm btn-light">모두 읽음 처리</button>
          </li>
        </ul>
      </div>
    </div> -->
  </template>
  
  <script>
  import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
  
  export default {
    name: 'NotificationBell',
    setup() {
      const notifications = ref([]);
      const unreadCount = ref(0);
      const isLoading = ref(false);
      let pollingInterval = null;
      
      // 임시 알림 데이터
      const mockNotifications = [
        {
          id: 1,
          message: '새로운 문의가 등록되었습니다.',
          link: '/inquiries/1',
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 10).toISOString() // 10분 전
        },
        {
          id: 2,
          message: '문의에 답변이 등록되었습니다.',
          link: '/inquiries/2',
          read: true,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2시간 전
        }
      ];
      
      const displayCount = computed(() => {
        // 99+ 처리
        return unreadCount.value > 99 ? '99+' : unreadCount.value;
      });
      
      // 알림 목록 불러오기
      const loadNotifications = async () => {
        isLoading.value = true;
        try {
          // 실제 API 호출 대신 임시 데이터 사용
          // await API 호출 로직 (추후 개발)
          setTimeout(() => {
            notifications.value = mockNotifications;
            updateUnreadCount();
            isLoading.value = false;
          }, 500);
        } catch (err) {
          console.error('알림 불러오기 실패:', err);
          isLoading.value = false;
        }
      };
      
      // 읽지 않은 알림 개수 업데이트
      const updateUnreadCount = () => {
        unreadCount.value = notifications.value.filter(notification => !notification.read).length;
      };
      
      // 드롭다운 열 때 알림 목록 불러오기
      const markAsRead = () => {
        loadNotifications();
      };
      
      // 모든 알림 읽음 처리
      const markAllAsRead = async () => {
        // 실제 API 호출 로직 (추후 개발)
        notifications.value.forEach(notification => {
          notification.read = true;
        });
        unreadCount.value = 0;
      };
      
      // 날짜 형식 변환
      const formatDate = (dateString) => {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000); // 분 단위 차이
        
        if (diffMins < 1) {
          return '방금 전';
        } else if (diffMins < 60) {
          return `${diffMins}분 전`;
        } else if (diffMins < 1440) { // 24시간 이내
          const hours = Math.floor(diffMins / 60);
          return `${hours}시간 전`;
        } else if (diffMins < 10080) { // 7일 이내
          const days = Math.floor(diffMins / 1440);
          return `${days}일 전`;
        } else {
          return date.toLocaleDateString('ko-KR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          });
        }
      };
      
      // 주기적 알림 업데이트 설정
      const setupPolling = () => {
        // 30초마다 알림 개수 업데이트
        pollingInterval = setInterval(() => {
          updateUnreadCount();
        }, 30000);
      };
      
      onMounted(() => {
        updateUnreadCount();
        setupPolling();
      });
      
      onBeforeUnmount(() => {
        // 컴포넌트 제거 시 폴링 중지
        if (pollingInterval) {
          clearInterval(pollingInterval);
        }
      });
      
      return {
        notifications,
        unreadCount,
        isLoading,
        displayCount,
        markAsRead,
        markAllAsRead,
        formatDate
      };
    }
  };
  </script>
  
  <style scoped>
  .notification-bell {
    position: relative;
  }
  
  .notification-icon-wrapper {
    position: relative;
    display: inline-block;
    padding: 0.5rem;
    color: inherit;
    text-decoration: none;
  }
  
  .notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    padding: 0 5px;
    background-color: #dc3545;
    color: white;
    font-size: 10px;
    border-radius: 10px;
  }
  
  .notification-dropdown {
    min-width: 300px;
    max-height: 400px;
    overflow-y: auto;
  }
  
  .notification-item {
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 5px;
    border-radius: 4px;
  }
  
  .notification-item:hover {
    background-color: #f8f9fa;
  }
  
  .notification-item.unread {
    background-color: rgba(13, 110, 253, 0.1);
    font-weight: 500;
  }
  
  .notification-content {
    font-size: 14px;
  }
  
  .notification-time {
    font-size: 12px;
    color: #6c757d;
    margin-top: 3px;
  }
  </style>