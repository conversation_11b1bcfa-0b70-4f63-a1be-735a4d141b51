<template>
  <div class="dashboard">
    <h1>대시보드</h1>

    <!-- 통계 카드 섹션 (SUPER_ADMIN만 접근 가능) -->
    <section v-if="canAccessStatistics" class="statistics-section system-statistics">
      <div class="section-header" @click="toggleStatistics">
        <h2>전체 시스템 통계</h2>
        <button class="toggle-btn">
          <i :class="['fas', isStatisticsVisible ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
        </button>
      </div>

      <!-- 통계 카드 -->
      <transition name="fade">
        <div v-show="isStatisticsVisible" class="statistics-cards">
        <!-- QR 코드 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !systemStatistics.qr.loading && !systemStatistics.qr.error, 'active': systemActiveChartType === 'qr' }"
          :style="{ backgroundColor: systemStatistics.qr.bgColor + '20' }"
          @click="!systemStatistics.qr.loading && !systemStatistics.qr.error && setSystemChartType('qr')"
        >
          <div v-if="systemStatistics.qr.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="systemStatistics.qr.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <button @click="loadSystemQrStatistics" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: systemStatistics.qr.bgColor, color: systemStatistics.qr.color }">
              <i :class="'fas ' + systemStatistics.qr.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ systemStatistics.qr.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">전체 QR 코드:</span>
                  <span class="stat-number" :style="{ color: systemStatistics.qr.color }">
                    {{ formatNumber(systemStatistics.qr.data.totalQrCodes) }}
                  </span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">총 스캔 횟수:</span>
                  <span class="stat-number" :style="{ color: systemStatistics.qr.color }">
                    {{ formatNumber(systemStatistics.qr.data.totalScans) }}
                  </span>
                </div>
              </div>
              <p class="stat-update-time">{{ formatTime(systemStatistics.qr.lastUpdated) }}</p>
            </div>
          </template>
        </div>

        <!-- 관리자 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !systemStatistics.admin.loading && !systemStatistics.admin.error, 'active': systemActiveChartType === 'admin' }"
          :style="{ backgroundColor: systemStatistics.admin.bgColor + '20' }"
          @click="!systemStatistics.admin.loading && !systemStatistics.admin.error && setSystemChartType('admin')"
        >
          <div v-if="systemStatistics.admin.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="systemStatistics.admin.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <button @click="loadSystemAdminStatistics" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: systemStatistics.admin.bgColor, color: systemStatistics.admin.color }">
              <i :class="'fas ' + systemStatistics.admin.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ systemStatistics.admin.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">전체 관리자:</span>
                  <span class="stat-number" :style="{ color: systemStatistics.admin.color }">
                    {{ formatNumber(systemStatistics.admin.data.totalAdmins) }}
                  </span>
                </div>
                <!-- 개별 관리자 통계 -->
                <template v-if="systemStatistics.admin.data.SUPER_ADMIN !== undefined">
                  <div class="stat-item">
                    <span class="stat-label">{{ formatAdminKey('SUPER_ADMIN') }}:</span>
                    <span class="stat-number" :style="{ color: systemStatistics.admin.color }">
                      {{ formatNumber(systemStatistics.admin.data.SUPER_ADMIN) }}
                    </span>
                  </div>
                </template>
                <template v-if="systemStatistics.admin.data.PROJECT_ADMIN !== undefined">
                  <div class="stat-item">
                    <span class="stat-label">{{ formatAdminKey('PROJECT_ADMIN') }}:</span>
                    <span class="stat-number" :style="{ color: systemStatistics.admin.color }">
                      {{ formatNumber(systemStatistics.admin.data.PROJECT_ADMIN) }}
                    </span>
                  </div>
                </template>
                <template v-if="systemStatistics.admin.data.SUB_ADMIN !== undefined">
                  <div class="stat-item">
                    <span class="stat-label">{{ formatAdminKey('SUB_ADMIN') }}:</span>
                    <span class="stat-number" :style="{ color: systemStatistics.admin.color }">
                      {{ formatNumber(systemStatistics.admin.data.SUB_ADMIN) }}
                    </span>
                  </div>
                </template>
                <template v-if="systemStatistics.admin.data.VIEWER !== undefined">
                  <div class="stat-item">
                    <span class="stat-label">{{ formatAdminKey('VIEWER') }}:</span>
                    <span class="stat-number" :style="{ color: systemStatistics.admin.color }">
                      {{ formatNumber(systemStatistics.admin.data.VIEWER) }}
                    </span>
                  </div>
                </template>
                <!-- 서버 응답 키가 다른 경우를 위한 대체 표시 -->
                <template v-if="!systemStatistics.admin.data.SUPER_ADMIN && !systemStatistics.admin.data.PROJECT_ADMIN && !systemStatistics.admin.data.SUB_ADMIN && !systemStatistics.admin.data.VIEWER">
                  <div v-for="(value, key) in systemStatistics.admin.data" :key="key" class="stat-item" v-if="key !== 'totalAdmins'">
                    <span class="stat-label">{{ formatAdminKey(key) }}:</span>
                    <span class="stat-number" :style="{ color: systemStatistics.admin.color }">
                      {{ formatNumber(value) }}
                    </span>
                  </div>
                </template>
              </div>
              <p class="stat-update-time">{{ formatTime(systemStatistics.admin.lastUpdated) }}</p>
            </div>
          </template>
        </div>

        <!-- 이벤트 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !systemStatistics.event.loading && !systemStatistics.event.error, 'active': systemActiveChartType === 'event' }"
          :style="{ backgroundColor: systemStatistics.event.bgColor + '20' }"
          @click="!systemStatistics.event.loading && !systemStatistics.event.error && setSystemChartType('event')"
        >
          <div v-if="systemStatistics.event.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="systemStatistics.event.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <button @click="loadSystemEventStatistics" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: systemStatistics.event.bgColor, color: systemStatistics.event.color }">
              <i :class="'fas ' + systemStatistics.event.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ systemStatistics.event.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">전체 이벤트:</span>
                  <span class="stat-number" :style="{ color: systemStatistics.event.color }">
                    {{ formatNumber(systemStatistics.event.data.totalEventCount || 0) }}
                  </span>
                </div>
              </div>
              <p class="stat-update-time">{{ formatTime(systemStatistics.event.lastUpdated) }}</p>
            </div>
          </template>
        </div>

        <!-- 참가자 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !systemStatistics.attendee.loading && !systemStatistics.attendee.error, 'active': systemActiveChartType === 'attendee' }"
          :style="{ backgroundColor: systemStatistics.attendee.bgColor + '20' }"
          @click="!systemStatistics.attendee.loading && !systemStatistics.attendee.error && setSystemChartType('attendee')"
        >
          <div v-if="systemStatistics.attendee.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="systemStatistics.attendee.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <button @click="loadSystemAttendeeStatistics" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: systemStatistics.attendee.bgColor, color: systemStatistics.attendee.color }">
              <i :class="'fas ' + systemStatistics.attendee.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ systemStatistics.attendee.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">전체 참가자:</span>
                  <span class="stat-number" :style="{ color: systemStatistics.attendee.color }">
                    {{ formatNumber(systemStatistics.attendee.data.totalAttendeeCount || 0) }}
                  </span>
                </div>
              </div>
              <p class="stat-update-time">{{ formatTime(systemStatistics.attendee.lastUpdated) }}</p>
            </div>
          </template>
        </div>
      </div>
      </transition>
    </section>

    <!-- 통계 차트 섹션 (SUPER_ADMIN만 접근 가능) -->
    <transition name="fade" v-if="canAccessStatistics">
      <section v-show="isStatisticsVisible" class="chart-section">
        <div class="chart-header">
          <h2>
            {{
              systemActiveChartType === 'qr' ? 'QR 코드 통계 차트' :
              systemActiveChartType === 'admin' ? '관리자 통계 차트' :
              systemActiveChartType === 'event' ? '이벤트 통계 차트' :
              '참가자 통계 차트'
            }}
          </h2>

          <!-- 차트 유형 선택 탭 -->
          <div class="chart-tabs">
            <button
              class="chart-tab"
              :class="{ 'active': systemActiveChartType === 'qr' }"
              @click="setSystemChartType('qr')"
            >
              QR 코드 통계
            </button>
            <button
              class="chart-tab"
              :class="{ 'active': systemActiveChartType === 'admin' }"
              @click="setSystemChartType('admin')"
            >
              관리자 통계
            </button>
            <button
              class="chart-tab"
              :class="{ 'active': systemActiveChartType === 'event' }"
              @click="setSystemChartType('event')"
            >
              이벤트 통계
            </button>
            <button
              class="chart-tab"
              :class="{ 'active': systemActiveChartType === 'attendee' }"
              @click="setSystemChartType('attendee')"
            >
              참가자 통계
            </button>
          </div>
        </div>

        <div class="chart-period-selector">
          <div class="date-inputs">
            <div class="date-input-group">
              <label for="systemStartDate">시작일:</label>
              <div class="input-with-icon">
                <input
                  type="date"
                  id="systemStartDate"
                  v-model="systemChartPeriod.startDate"
                  class="date-input"
                />
                <i class="fas fa-calendar-alt"></i>
              </div>
            </div>
            <div class="date-input-group">
              <label for="systemEndDate">종료일:</label>
              <div class="input-with-icon">
                <input
                  type="date"
                  id="systemEndDate"
                  v-model="systemChartPeriod.endDate"
                  class="date-input"
                />
                <i class="fas fa-calendar-alt"></i>
              </div>
            </div>
          </div>
          <div class="search-btn-container">
            <button @click="loadSystemChartData()" class="search-btn">
              <i class="fas fa-search"></i> 검색
            </button>
          </div>
        </div>

        <div class="chart-container">
          <div v-if="systemChartLoading" class="chart-loading">
            <div class="loading-spinner"></div>
            <p>차트 데이터 로딩 중...</p>
          </div>
          <div v-else-if="systemChartError" class="chart-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>{{ systemChartError }}</p>
            <button @click="loadSystemChartData()" class="retry-btn">재시도</button>
          </div>
          <div v-else class="chart-wrapper">
            <StatisticsChart
              :chartData="systemChartData"
              :chartOptions="chartOptions"
            />
          </div>
        </div>
      </section>
    </transition>

    <hr>

    <!-- 프로젝트 선택 섹션 -->
    <div class="project-selection-header">
      <h3>프로젝트 선택</h3>

      <!-- 현재 선택된 프로젝트 정보 -->
      <div v-if="currentProject" class="current-project-info">
        <span class="project-name">{{ currentProject.projectName }}</span>
        <span class="project-id">(ID: {{ currentProject.projectId }})</span>
      </div>
      <div v-else-if="isSuperAdmin && filteredProjects.length > 0" class="super-admin-message">
        <p>모든 프로젝트의 통계를 볼 수 있습니다. 아래에서 특정 프로젝트를 선택하세요.</p>
      </div>
      <div v-else class="no-project-message">
        <p>선택된 프로젝트가 없습니다. 아래에서 프로젝트를 선택해주세요.</p>
      </div>

      <!-- 프로젝트 선택 목록 -->
      <div v-if="filteredProjects.length > 0" class="project-list">
        <div class="project-list-header">
          <h4>프로젝트 목록</h4>

          <!-- 검색 입력 -->
          <div class="search-box">
            <input
              type="text"
              v-model="searchQuery"
              @input="handleSearch"
              placeholder="프로젝트 검색..."
              class="search-input"
            />
            <button v-if="searchQuery" @click="searchQuery = ''; handleSearch()" class="clear-search" aria-label="검색어 지우기">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <ul>
          <li
            v-for="project in userProjects"
            :key="project.projectId"
            :class="{ 'active': currentProject && currentProject.projectId === project.projectId }"
            @click="changeProject(filteredProjects.indexOf(project))"
          >
            <div class="project-item">
              <span class="project-item-name">{{ project.projectName }}</span>
              <span class="project-item-id">ID: {{ project.projectId }}</span>
            </div>
          </li>
        </ul>

        <!-- 페이징 UI -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn prev"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>

          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn next"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
      <div v-else class="no-projects-message">
        <p v-if="searchQuery">검색 결과가 없습니다.</p>
        <p v-else>속한 프로젝트가 없습니다.</p>
      </div>
    </div>

    <hr v-if="isProjectSelected">

    <!-- 프로젝트별 통계 카드 섹션 -->
    <section v-if="isProjectSelected" class="statistics-section project-statistics">
      <div class="section-header">
        <h2>"{{ currentProject.projectName }}" 프로젝트 통계</h2>
      </div>

      <div class="statistics-cards">
        <!-- 프로젝트별 QR 코드 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !projectStatistics.qr.loading && !projectStatistics.qr.error, 'active': projectActiveChartType === 'qr' }"
          @click="!projectStatistics.qr.loading && !projectStatistics.qr.error && setProjectChartType('qr')"
        >
          <div v-if="projectStatistics.qr.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="projectStatistics.qr.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>{{ projectStatistics.qr.error }}</p>
            <button @click="loadProjectStatistics(currentProjectId)" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: projectStatistics.qr.bgColor, color: projectStatistics.qr.color }">
              <i :class="'fas ' + projectStatistics.qr.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ projectStatistics.qr.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">총 QR 코드:</span>
                  <span class="stat-number" :style="{ color: projectStatistics.qr.color }">
                    {{ formatNumber(projectStatistics.qr.data.totalQrCodes) }}
                  </span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">총 스캔 횟수:</span>
                  <span class="stat-number" :style="{ color: projectStatistics.qr.color }">
                    {{ formatNumber(projectStatistics.qr.data.totalScans) }}
                  </span>
                </div>
              </div>
              <p class="stat-update-time">{{ formatTime(projectStatistics.qr.lastUpdated) }}</p>
            </div>
          </template>
        </div>

        <!-- 프로젝트별 이벤트 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !projectStatistics.event.loading && !projectStatistics.event.error, 'active': projectActiveChartType === 'event' }"
          @click="!projectStatistics.event.loading && !projectStatistics.event.error && setProjectChartType('event')"
        >
          <div v-if="projectStatistics.event.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="projectStatistics.event.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>{{ projectStatistics.event.error }}</p>
            <button @click="loadProjectStatistics(currentProjectId)" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: projectStatistics.event.bgColor, color: projectStatistics.event.color }">
              <i :class="'fas ' + projectStatistics.event.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ projectStatistics.event.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">이벤트 개수:</span>
                  <span class="stat-number" :style="{ color: projectStatistics.event.color }">
                    {{ formatNumber(projectStatistics.event.data.totalEventCount || 0) }}
                  </span>
                </div>
              </div>
              <p class="stat-update-time">{{ formatTime(projectStatistics.event.lastUpdated) }}</p>
            </div>
          </template>
        </div>

        <!-- 프로젝트별 참가자 통계 카드 -->
        <div
          class="stat-card"
          :class="{ 'clickable': !projectStatistics.attendee.loading && !projectStatistics.attendee.error, 'active': projectActiveChartType === 'attendee' }"
          @click="!projectStatistics.attendee.loading && !projectStatistics.attendee.error && setProjectChartType('attendee')"
        >
          <div v-if="projectStatistics.attendee.loading" class="card-loading">
            <div class="loading-spinner"></div>
          </div>
          <div v-else-if="projectStatistics.attendee.error" class="card-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>{{ projectStatistics.attendee.error }}</p>
            <button @click="loadProjectStatistics(currentProjectId)" class="retry-btn-small">재시도</button>
          </div>
          <template v-else>
            <div class="stat-icon" :style="{ backgroundColor: projectStatistics.attendee.bgColor, color: projectStatistics.attendee.color }">
              <i :class="'fas ' + projectStatistics.attendee.icon"></i>
            </div>
            <div class="stat-content">
              <h3>{{ projectStatistics.attendee.title }}</h3>
              <div class="stat-details">
                <div class="stat-item">
                  <span class="stat-label">참가자 수:</span>
                  <span class="stat-number" :style="{ color: projectStatistics.attendee.color }">
                    {{ formatNumber(projectStatistics.attendee.data.totalAttendeeCount || 0) }}
                  </span>
                </div>
              </div>
              <p class="stat-update-time">{{ formatTime(projectStatistics.attendee.lastUpdated) }}</p>
            </div>
          </template>
        </div>
      </div>
    </section>

    <!-- 프로젝트별 통계 차트 섹션 -->
    <section v-if="isProjectSelected" class="chart-section project-chart">
      <div class="chart-header">
        <h2>"{{ currentProject.projectName }}" {{
          projectActiveChartType === 'qr' ? 'QR 코드' :
          projectActiveChartType === 'admin' ? '관리자' :
          projectActiveChartType === 'event' ? '이벤트' :
          '참가자'
        }} 통계 차트</h2>
        <div class="chart-controls">
          <div class="chart-type-selector">
            <button
              v-for="type in chartTypes"
              :key="type.value"
              :class="['chart-type-btn', { active: projectActiveChartType === type.value }]"
              @click="setProjectChartType(type.value)"
            >
              {{ type.label }}
            </button>
          </div>

          <div class="chart-period-selector">
            <div class="date-inputs">
              <div class="date-input-group">
                <label for="startDate">시작일:</label>
                <div class="input-with-icon">
                  <input
                    type="date"
                    id="startDate"
                    v-model="projectChartPeriod.startDate"
                    class="date-input"
                  />
                  <i class="fas fa-calendar-alt"></i>
                </div>
              </div>
              <div class="date-input-group">
                <label for="endDate">종료일:</label>
                <div class="input-with-icon">
                  <input
                    type="date"
                    id="endDate"
                    v-model="projectChartPeriod.endDate"
                    class="date-input"
                  />
                  <i class="fas fa-calendar-alt"></i>
                </div>
              </div>
            </div>
            <div class="search-btn-container">
              <button @click="loadProjectChartData(currentProjectId)" class="search-btn">
                <i class="fas fa-search"></i> 검색
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-container">
        <div v-if="projectChartLoading" class="chart-loading">
          <div class="loading-spinner"></div>
          <p>차트 데이터 로딩 중...</p>
        </div>
        <div v-else-if="projectChartError" class="chart-error">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ projectChartError }}</p>
          <button @click="loadProjectChartData(currentProjectId)" class="retry-btn">재시도</button>
        </div>
        <div v-else class="chart-wrapper">
          <StatisticsChart
            :chartData="projectChartData"
            :chartOptions="chartOptions"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';
import {
  getQrTotalStatistics,
  getAdminStatistics,
  getEventStatistics,
  getAttendeeStatistics,
  getQrStatisticsByPeriod,
  getAdminStatisticsByPeriod,
  getEventStatisticsByPeriod,
  getAttendeeStatisticsByPeriod,
  getProjectQrStatistics,
  getProjectEventStatistics,
  getProjectAttendeeStatistics
} from '@/api/statistics';
import StatisticsChart from '@/components/charts/StatisticsChart.vue';

// 인증 스토어
const authStore = useAuthStore();

// 현재 사용자가 SUPER_ADMIN인지 확인
const isSuperAdmin = computed(() => {
  const result = authStore.user && authStore.user.roleId === 'SUPER_ADMIN';
  return result;
});

// 현재 선택된 프로젝트 정보
const currentProject = computed(() => {
  return authStore.currentProject;
});

// 프로젝트 ID 계산 속성
const currentProjectId = computed(() => {
  return currentProject.value?.projectId;
});

// 프로젝트가 선택되었는지 여부 (프로젝트 ID가 있는 경우에만 true)
const isProjectSelected = computed(() => {
  return !!currentProjectId.value;
});

// 페이징 및 검색 관련 상태
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = 5; // 페이지당 항목 수

// 검색어에 따라 필터링된 프로젝트 목록
const filteredProjects = computed(() => {
  const projects = authStore.user?.projects || [];

  if (!searchQuery.value) {
    return projects;
  }

  const query = searchQuery.value.toLowerCase();
  const filtered = projects.filter(project =>
    project.projectName.toLowerCase().includes(query) ||
    project.projectId.toString().includes(query)
  );
  return filtered;
});

// 전체 페이지 수
const totalPages = computed(() => {
  return Math.ceil(filteredProjects.value.length / itemsPerPage);
});

// 현재 페이지에 표시할 프로젝트 목록
const userProjects = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredProjects.value.slice(startIndex, endIndex);
});

// 검색 처리 함수
const handleSearch = () => {
  // 검색 시 첫 페이지로 이동
  currentPage.value = 1;
};

// 페이지 변경 함수
const changePage = (page) => {
  currentPage.value = page;
};

// 프로젝트 변경 함수
const changeProject = (projectIndex) => {
  // Auth 스토어의 selectProject 함수 호출
  const success = authStore.selectProject(projectIndex);

  if (success) {

    // 전체 프로젝트 모드인지 확인 (projectId가 null인 경우)
    if (authStore.currentProject.projectId === null) {
      // 전체 프로젝트 모드에서는 프로젝트별 통계를 로드하지 않음
      return;
    }

    // 특정 프로젝트가 선택된 경우에만 프로젝트별 통계 로드
    // 프로젝트 통계 카드 데이터 로드
    loadProjectStatistics(authStore.currentProject.projectId);

    // 프로젝트 차트 데이터 로드 (현재 선택된 차트 유형 유지)
    loadProjectChartData(authStore.currentProject.projectId);
  }
};

// 프로젝트 변경 감지 및 통계 데이터 로드
watch(currentProjectId, (newProjectId, oldProjectId) => {
  // 새 프로젝트 ID가 있고, 이전 프로젝트 ID와 다른 경우에만 처리
  if (newProjectId !== oldProjectId) {

    // 전체 프로젝트 모드인 경우 (newProjectId가 null)
    if (newProjectId === null) {
      // 전체 프로젝트 모드에서는 프로젝트별 통계를 로드하지 않음
      return;
    }

    // 특정 프로젝트가 선택된 경우에만 프로젝트별 통계 로드
    // 프로젝트 통계 카드 데이터 로드
    loadProjectStatistics(newProjectId);

    // 프로젝트 차트 데이터 로드 (현재 선택된 차트 유형 유지)
    loadProjectChartData(newProjectId);
  }
});

// 프로젝트별 통계 데이터 로드
const loadProjectStatistics = async (projectId) => {
  // projectId가 null인 경우 (전체 프로젝트 모드)
  if (projectId === null) {
    return;
  }

  // 프로젝트 ID가 없는 경우
  if (!projectId) {
    console.warn('프로젝트 ID가 없어 통계를 로드할 수 없습니다.');
    return;
  }


  // 각 통계 카드의 로딩 상태 설정
  projectStatistics.value.qr.loading = true;
  projectStatistics.value.event.loading = true;
  projectStatistics.value.attendee.loading = true;

  // 각 통계 카드의 에러 상태 초기화
  projectStatistics.value.qr.error = null;
  projectStatistics.value.event.error = null;
  projectStatistics.value.attendee.error = null;

  // 프로젝트별 QR 코드 통계 로드
  loadProjectQrStatistics(projectId);

  // 프로젝트별 이벤트 통계 로드
  loadProjectEventStatistics(projectId);

  // 프로젝트별 참가자 통계 로드
  loadProjectAttendeeStatistics(projectId);

};

// 프로젝트별 QR 코드 통계 로드
const loadProjectQrStatistics = async (projectId) => {
  try {
    const qrResponse = await getProjectQrStatistics(projectId);

    // 응답 구조 확인 및 데이터 추출
    if (qrResponse && qrResponse.success === true && qrResponse.data) {
      // 응답 형식: { success: true, data: { totalQrCodes: 10, totalScans: 50 } }
      projectStatistics.value.qr.data = qrResponse.data;
    } else if (qrResponse && qrResponse.data) {
      // 다른 가능한 응답 구조
      projectStatistics.value.qr.data = qrResponse.data;
    } else if (qrResponse) {
      // 응답이 있지만 예상 구조가 아닌 경우
      projectStatistics.value.qr.data = qrResponse;
    } else {
      throw new Error('서버에서 유효한 데이터를 반환하지 않았습니다.');
    }

    // 오류 응답 확인
    if (qrResponse && qrResponse.error) {
      throw new Error(qrResponse.error.message || '서버에서 오류가 발생했습니다.');
    }

    projectStatistics.value.qr.lastUpdated = new Date();
  } catch (err) {
    console.error('프로젝트별 QR 코드 통계 로드 실패:', err);
    projectStatistics.value.qr.error = '프로젝트별 QR 코드 통계를 불러오는데 실패했습니다. 서버 오류가 발생했습니다.';
  } finally {
    projectStatistics.value.qr.loading = false;
  }
};

// 프로젝트별 이벤트 통계 로드
const loadProjectEventStatistics = async (projectId) => {
  try {
    const eventResponse = await getProjectEventStatistics(projectId);

    // 응답 구조 확인 및 데이터 추출
    if (eventResponse && eventResponse.success === true && eventResponse.data) {
      // 응답 형식: { success: true, data: { additionalProp1: 0, additionalProp2: 0, additionalProp3: 0 } }

      // 데이터 처리: additionalProp 형식의 데이터를 처리
      const processedData = {
        totalEventCount: 0
      };

      // additionalProp 값들의 합계를 totalEventCount로 설정
      if (typeof eventResponse.data === 'object') {
        let total = 0;
        for (const key in eventResponse.data) {
          if (Object.prototype.hasOwnProperty.call(eventResponse.data, key) &&
              typeof eventResponse.data[key] === 'number') {
            total += eventResponse.data[key];
          }
        }
        processedData.totalEventCount = total;
      }

      projectStatistics.value.event.data = processedData;
    } else if (eventResponse && eventResponse.data) {
      // 다른 가능한 응답 구조
      projectStatistics.value.event.data = eventResponse.data;
    } else if (eventResponse) {
      // 응답이 있지만 예상 구조가 아닌 경우
      projectStatistics.value.event.data = eventResponse;
    } else {
      throw new Error('서버에서 유효한 데이터를 반환하지 않았습니다.');
    }

    // 오류 응답 확인
    if (eventResponse && eventResponse.error) {
      throw new Error(eventResponse.error.message || '서버에서 오류가 발생했습니다.');
    }

    projectStatistics.value.event.lastUpdated = new Date();
  } catch (err) {
    console.error('프로젝트별 이벤트 통계 로드 실패:', err);
    projectStatistics.value.event.error = '프로젝트별 이벤트 통계를 불러오는데 실패했습니다. 서버 오류가 발생했습니다.';
  } finally {
    projectStatistics.value.event.loading = false;
  }
};

// 프로젝트별 참가자 통계 로드
const loadProjectAttendeeStatistics = async (projectId) => {
  try {
    const attendeeResponse = await getProjectAttendeeStatistics(projectId);

    // 응답 구조 확인 및 데이터 추출
    if (attendeeResponse && attendeeResponse.success === true && attendeeResponse.data) {
      // 응답 형식: { success: true, data: { additionalProp1: 0, additionalProp2: 0, additionalProp3: 0 } }

      // 데이터 처리: additionalProp 형식의 데이터를 처리
      const processedData = {
        totalAttendeeCount: 0
      };

      // additionalProp 값들의 합계를 totalAttendeeCount로 설정
      if (typeof attendeeResponse.data === 'object') {
        let total = 0;
        for (const key in attendeeResponse.data) {
          if (Object.prototype.hasOwnProperty.call(attendeeResponse.data, key) &&
              typeof attendeeResponse.data[key] === 'number') {
            total += attendeeResponse.data[key];
          }
        }
        processedData.totalAttendeeCount = total;
      }

      projectStatistics.value.attendee.data = processedData;
    } else if (attendeeResponse && attendeeResponse.data) {
      // 다른 가능한 응답 구조
      projectStatistics.value.attendee.data = attendeeResponse.data;
    } else if (attendeeResponse) {
      // 응답이 있지만 예상 구조가 아닌 경우
      projectStatistics.value.attendee.data = attendeeResponse;
    } else {
      throw new Error('서버에서 유효한 데이터를 반환하지 않았습니다.');
    }

    // 오류 응답 확인
    if (attendeeResponse && attendeeResponse.error) {
      throw new Error(attendeeResponse.error.message || '서버에서 오류가 발생했습니다.');
    }

    projectStatistics.value.attendee.lastUpdated = new Date();
  } catch (err) {
    console.error('프로젝트별 참가자 통계 로드 실패:', err);
    projectStatistics.value.attendee.error = '프로젝트별 참가자 통계를 불러오는데 실패했습니다. 서버 오류가 발생했습니다.';
  } finally {
    projectStatistics.value.attendee.loading = false;
  }
};

// 전체 시스템 통계 데이터 상태
const systemStatistics = ref({
  qr: {
    title: 'QR 코드',
    icon: 'fa-qrcode',
    color: '#1976d2',
    bgColor: '#f0f7ff',
    data: { totalQrCodes: 0, totalScans: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  },
  admin: {
    title: '관리자',
    icon: 'fa-users-cog',
    color: '#2e7d32',
    bgColor: '#e8f5e9',
    data: { totalAdmins: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  },
  event: {
    title: '이벤트',
    icon: 'fa-calendar-alt',
    color: '#f57c00',
    bgColor: '#fff3e0',
    data: { totalEventCount: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  },
  attendee: {
    title: '참가자',
    icon: 'fa-user-check',
    color: '#7b1fa2',
    bgColor: '#f3e5f5',
    data: { totalAttendeeCount: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  }
});

// 프로젝트별 통계 데이터 상태
const projectStatistics = ref({
  qr: {
    title: computed(() => currentProject.value ? `${currentProject.value.projectName} QR 코드` : 'QR 코드'),
    icon: 'fa-qrcode',
    color: '#1976d2',
    bgColor: '#f0f7ff',
    data: { totalQrCodes: 0, totalScans: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  },
  admin: {
    title: computed(() => currentProject.value ? `${currentProject.value.projectName} 관리자` : '관리자'),
    icon: 'fa-users-cog',
    color: '#2e7d32',
    bgColor: '#e8f5e9',
    data: { totalAdmins: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  },
  event: {
    title: computed(() => currentProject.value ? `${currentProject.value.projectName} 이벤트` : '이벤트'),
    icon: 'fa-calendar-alt',
    color: '#f57c00',
    bgColor: '#fff3e0',
    data: { totalEventCount: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  },
  attendee: {
    title: computed(() => currentProject.value ? `${currentProject.value.projectName} 참가자` : '참가자'),
    icon: 'fa-user-check',
    color: '#7b1fa2',
    bgColor: '#f3e5f5',
    data: { totalAttendeeCount: 0 },
    loading: true,
    error: null,
    lastUpdated: new Date()
  }
});

// 전체 시스템 QR 코드 통계 로드
const loadSystemQrStatistics = async () => {
  systemStatistics.value.qr.loading = true;
  systemStatistics.value.qr.error = null;

  try {
    const response = await getQrTotalStatistics();

    // 응답 구조 확인 및 데이터 추출
    if (response && response.data) {
      // 표준 응답 구조: { success: true, data: { totalQrCodes: 10, totalScans: 5 } }
      systemStatistics.value.qr.data = response.data;
    } else {
      // 다른 응답 구조
      systemStatistics.value.qr.data = response;
    }

    // 데이터가 없는 경우 기본값 설정
    if (!systemStatistics.value.qr.data.totalQrCodes && !systemStatistics.value.qr.data.totalScans) {
      systemStatistics.value.qr.data = { totalQrCodes: 0, totalScans: 0 };
    }

    systemStatistics.value.qr.lastUpdated = new Date();
  } catch (err) {
    console.error('전체 시스템 QR 통계 로드 실패:', err);
    systemStatistics.value.qr.error = err.message || 'QR 코드 통계를 불러오는데 실패했습니다.';
    // 오류 발생 시 기본값 설정
    systemStatistics.value.qr.data = { totalQrCodes: 0, totalScans: 0 };
  } finally {
    systemStatistics.value.qr.loading = false;
  }
};

// 불필요한 함수 제거

// 숫자 포맷팅 (천 단위 구분)
const formatNumber = (num) => {
  if (num === undefined || num === null) return '0';
  return num.toLocaleString();
};

// 시간 포맷팅
const formatTime = (date) => {
  if (!date) return '';
  return date.toLocaleTimeString();
};

// 날짜 포매팅 함수는 현재 사용되지 않음

// 관리자 키 포맷팅 함수
const formatAdminKey = (key) => {
  // 관리자 유형 키를 사용자 친화적인 텍스트로 변환
  const keyMap = {
    'SUPER_ADMIN': '최고 관리자',
    'PROJECT_ADMIN': '프로젝트 관리자',
    'SUB_ADMIN': '일반 관리자',
    'VIEWER': '뷰어'
  };

  // 키가 매핑 테이블에 있으면 해당 값 반환, 없으면 키 자체를 반환
  return keyMap[key] || key;
};

// 전체 시스템 관리자 통계 로드
const loadSystemAdminStatistics = async () => {
  systemStatistics.value.admin.loading = true;
  systemStatistics.value.admin.error = null;

  try {
    const response = await getAdminStatistics();

    let adminData = {};

    if (response && response.data) {
      // 표준 응답 구조: { success: true, data: { ... } }
      adminData = response.data;
    } else {
      // 다른 응답 구조
      adminData = response;
    }

    // 서버 응답에서 관리자 총 수를 계산
    const totalAdmins = Object.values(adminData).reduce((sum, count) => sum + (count || 0), 0);
    systemStatistics.value.admin.data = {
      ...adminData,
      totalAdmins
    };

    systemStatistics.value.admin.lastUpdated = new Date();
  } catch (err) {
    console.error('전체 시스템 관리자 통계 로드 실패:', err);
    systemStatistics.value.admin.error = err.message || '관리자 통계를 불러오는데 실패했습니다.';
    // 오류 발생 시 기본값 설정
    systemStatistics.value.admin.data = {
      SUPER_ADMIN: 0,
      PROJECT_ADMIN: 0,
      SUB_ADMIN: 0,
      VIEWER: 0,
      totalAdmins: 0
    };
  } finally {
    systemStatistics.value.admin.loading = false;
  }
};

// 전체 시스템 이벤트 통계 로드
const loadSystemEventStatistics = async () => {
  systemStatistics.value.event.loading = true;
  systemStatistics.value.event.error = null;

  try {
    const response = await getEventStatistics();

    if (response && response.data) {
      // 표준 응답 구조: { success: true, data: { totalEventCount: 5 } }
      systemStatistics.value.event.data = response.data;
    } else {
      // 다른 응답 구조
      systemStatistics.value.event.data = response;
    }

    // 데이터가 없는 경우 기본값 설정
    if (!systemStatistics.value.event.data.totalEventCount) {
      systemStatistics.value.event.data = { totalEventCount: 0 };
    }

    systemStatistics.value.event.lastUpdated = new Date();
  } catch (err) {
    console.error('전체 시스템 이벤트 통계 로드 실패:', err);
    systemStatistics.value.event.error = err.message || '이벤트 통계를 불러오는데 실패했습니다.';
    // 오류 발생 시 기본값 설정
    systemStatistics.value.event.data = { totalEventCount: 0 };
  } finally {
    systemStatistics.value.event.loading = false;
  }
};

// 전체 시스템 참가자 통계 로드
const loadSystemAttendeeStatistics = async () => {
  systemStatistics.value.attendee.loading = true;
  systemStatistics.value.attendee.error = null;

  try {
    const response = await getAttendeeStatistics();

    if (response && response.data) {
      // 표준 응답 구조: { success: true, data: { totalAttendeeCount: 50 } }
      systemStatistics.value.attendee.data = response.data;
    } else {
      // 다른 응답 구조
      systemStatistics.value.attendee.data = response;
    }

    // 데이터가 없는 경우 기본값 설정
    if (!systemStatistics.value.attendee.data.totalAttendeeCount) {
      systemStatistics.value.attendee.data = { totalAttendeeCount: 0 };
    }

    systemStatistics.value.attendee.lastUpdated = new Date();
  } catch (err) {
    console.error('전체 시스템 참가자 통계 로드 실패:', err);
    systemStatistics.value.attendee.error = err.message || '참가자 통계를 불러오는데 실패했습니다.';
    // 오류 발생 시 기본값 설정
    systemStatistics.value.attendee.data = { totalAttendeeCount: 0 };
  } finally {
    systemStatistics.value.attendee.loading = false;
  }
};

// 통계 섹션 표시 여부 (SUPER_ADMIN만 접근 가능)
const isStatisticsVisible = ref(true);

// 통계 섹션 토글 함수 (SUPER_ADMIN만 토글 가능)
const toggleStatistics = () => {
  if (isSuperAdmin.value) {
    isStatisticsVisible.value = !isStatisticsVisible.value;
  }
};

// 통계 섹션 접근 가능 여부
const canAccessStatistics = computed(() => {
  return isSuperAdmin.value;
});

// 차트 관련 상태
const systemActiveChartType = ref('qr'); // 시스템 차트 유형: 'qr', 'admin', 'event' 또는 'attendee'
const projectActiveChartType = ref('qr'); // 프로젝트 차트 유형: 'qr', 'admin', 'event' 또는 'attendee'

// 차트 유형 목록
const chartTypes = [
  { value: 'qr', label: 'QR 코드 통계' },
  { value: 'event', label: '이벤트 통계' },
  { value: 'attendee', label: '참가자 통계' }
];

// 전체 시스템 차트 기간
const systemChartPeriod = ref({
  startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0], // 1개월 전
  endDate: new Date().toISOString().split('T')[0] // 오늘
});

// 프로젝트별 차트 기간
const projectChartPeriod = ref({
  startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0], // 1개월 전
  endDate: new Date().toISOString().split('T')[0] // 오늘
});

// 전체 시스템 차트 데이터 (SUPER_ADMIN만 접근 가능)
const systemChartData = ref({
  labels: [],
  datasets: []
});

const systemChartLoading = ref(false);
const systemChartError = ref('');

// 프로젝트별 차트 데이터
const projectChartData = ref({
  labels: [],
  datasets: []
});

const projectChartLoading = ref(false);
const projectChartError = ref('');

// 차트 옵션은 공통으로 사용

// 시스템 차트 유형 설정
const setSystemChartType = (type) => {
  if (systemActiveChartType.value !== type) {
    systemActiveChartType.value = type;

    // 전체 시스템 차트 데이터만 로드
    loadSystemChartData();
  }
};

// 프로젝트 차트 유형 설정
const setProjectChartType = (type) => {
  if (projectActiveChartType.value !== type) {
    projectActiveChartType.value = type;

    // 프로젝트가 선택된 경우에만 프로젝트별 차트 데이터 로드
    if (isProjectSelected.value) {
      loadProjectChartData(currentProjectId.value);
    }
  }
};

// QR 코드 차트 데이터 초기화
const initQrChartData = () => {
  return {
    labels: [],
    datasets: [
      {
        label: 'QR 코드 생성 수',
        data: [],
        borderColor: '#1976d2',
        backgroundColor: 'rgba(25, 118, 210, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'QR 코드 스캔 수',
        data: [],
        borderColor: '#2e7d32',
        backgroundColor: 'rgba(46, 125, 50, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };
};

// 관리자 차트 데이터 초기화
const initAdminChartData = () => {
  return {
    labels: [],
    datasets: [
      {
        label: '최고 관리자',
        data: [],
        borderColor: '#d32f2f',
        backgroundColor: 'rgba(211, 47, 47, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '프로젝트 관리자',
        data: [],
        borderColor: '#7b1fa2',
        backgroundColor: 'rgba(123, 31, 162, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '일반 관리자',
        data: [],
        borderColor: '#1976d2',
        backgroundColor: 'rgba(25, 118, 210, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '뷰어',
        data: [],
        borderColor: '#388e3c',
        backgroundColor: 'rgba(56, 142, 60, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };
};

// 이벤트 차트 데이터 초기화
const initEventChartData = () => {
  return {
    labels: [],
    datasets: [
      {
        label: '이벤트 생성 수',
        data: [],
        borderColor: '#ff9800',
        backgroundColor: 'rgba(255, 152, 0, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };
};

// 참가자 차트 데이터 초기화
const initAttendeeChartData = () => {
  return {
    labels: [],
    datasets: [
      {
        label: '등록된 참가자 수',
        data: [],
        borderColor: '#e91e63',
        backgroundColor: 'rgba(233, 30, 99, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };
};

// 차트 데이터 초기화 함수는 각 차트 유형별로 분리되어 있음

// 전체 시스템 차트 데이터 로드
const loadSystemChartData = async () => {
  systemChartLoading.value = true;
  systemChartError.value = '';

  try {
    const { startDate, endDate } = systemChartPeriod.value;

    if (systemActiveChartType.value === 'qr') {
      await loadSystemQrChartData(startDate, endDate);
    } else if (systemActiveChartType.value === 'admin') {
      await loadSystemAdminChartData(startDate, endDate);
    } else if (systemActiveChartType.value === 'event') {
      await loadSystemEventChartData(startDate, endDate);
    } else if (systemActiveChartType.value === 'attendee') {
      await loadSystemAttendeeChartData(startDate, endDate);
    }
  } catch (err) {
    console.error('전체 시스템 차트 데이터 로드 실패:', err);
    systemChartError.value = err.message || '차트 데이터를 불러오는데 실패했습니다.';

    // 에러 발생 시 빈 차트 데이터 설정
    if (systemActiveChartType.value === 'qr') {
      systemChartData.value = initQrChartData();
    } else if (systemActiveChartType.value === 'admin') {
      systemChartData.value = initAdminChartData();
    } else if (systemActiveChartType.value === 'event') {
      systemChartData.value = initEventChartData();
    } else if (systemActiveChartType.value === 'attendee') {
      systemChartData.value = initAttendeeChartData();
    }
  } finally {
    systemChartLoading.value = false;
  }
};

// 프로젝트별 차트 데이터 로드
const loadProjectChartData = async (projectId) => {
  // projectId가 null인 경우 (전체 프로젝트 모드)
  if (projectId === null) {
    return;
  }

  // 프로젝트 ID가 없는 경우
  if (!projectId) {
    console.warn('프로젝트 ID가 없어 프로젝트별 차트 데이터를 로드할 수 없습니다.');
    return;
  }

  projectChartLoading.value = true;
  projectChartError.value = '';

  try {
    const { startDate, endDate } = projectChartPeriod.value;

    if (projectActiveChartType.value === 'qr') {
      await loadProjectQrChartData(startDate, endDate, projectId);
    } else if (projectActiveChartType.value === 'admin') {
      await loadProjectAdminChartData(startDate, endDate, projectId);
    } else if (projectActiveChartType.value === 'event') {
      await loadProjectEventChartData(startDate, endDate, projectId);
    } else if (projectActiveChartType.value === 'attendee') {
      await loadProjectAttendeeChartData(startDate, endDate, projectId);
    }
  } catch (err) {
    console.error('프로젝트별 차트 데이터 로드 실패:', err);
    projectChartError.value = err.message || '차트 데이터를 불러오는데 실패했습니다.';

    // 에러 발생 시 빈 차트 데이터 설정
    if (projectActiveChartType.value === 'qr') {
      projectChartData.value = initQrChartData();
    } else if (projectActiveChartType.value === 'admin') {
      projectChartData.value = initAdminChartData();
    } else if (projectActiveChartType.value === 'event') {
      projectChartData.value = initEventChartData();
    } else if (projectActiveChartType.value === 'attendee') {
      projectChartData.value = initAttendeeChartData();
    }
  } finally {
    projectChartLoading.value = false;
  }
};

// 전체 시스템 QR 코드 차트 데이터 로드
const loadSystemQrChartData = async (startDate, endDate) => {

  const response = await getQrStatisticsByPeriod(startDate, endDate, null);

  // 응답 구조에 따라 데이터 추출
  let dates = [], createdCounts = [], scannedCounts = [];

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], createdCounts: [...], scannedCounts: [...] } }
    dates = response.data.dates || [];
    createdCounts = response.data.createdCounts || [];
    scannedCounts = response.data.scannedCounts || [];
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], createdCounts: [...], scannedCounts: [...] }
    dates = response.dates || [];
    createdCounts = response.createdCounts || [];
    scannedCounts = response.scannedCounts || [];
  } else {
    throw new Error('QR 코드 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  systemChartData.value = {
    labels: dates,
    datasets: [
      {
        label: 'QR 코드 생성 수',
        data: createdCounts,
        borderColor: '#1976d2',
        backgroundColor: 'rgba(25, 118, 210, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'QR 코드 스캔 수',
        data: scannedCounts,
        borderColor: '#2e7d32',
        backgroundColor: 'rgba(46, 125, 50, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 프로젝트별 QR 코드 차트 데이터 로드
const loadProjectQrChartData = async (startDate, endDate, projectId) => {
  if (!projectId) {
    console.warn('프로젝트 ID가 없어 프로젝트별 QR 코드 차트 데이터를 로드할 수 없습니다.');
    return;
  }

  const response = await getQrStatisticsByPeriod(startDate, endDate, projectId);

  // 응답 구조에 따라 데이터 추출
  let dates = [], createdCounts = [], scannedCounts = [];

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], createdCounts: [...], scannedCounts: [...] } }
    dates = response.data.dates || [];
    createdCounts = response.data.createdCounts || [];
    scannedCounts = response.data.scannedCounts || [];
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], createdCounts: [...], scannedCounts: [...] }
    dates = response.dates || [];
    createdCounts = response.createdCounts || [];
    scannedCounts = response.scannedCounts || [];
  } else {
    throw new Error('QR 코드 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  projectChartData.value = {
    labels: dates,
    datasets: [
      {
        label: 'QR 코드 생성 수',
        data: createdCounts,
        borderColor: '#1976d2',
        backgroundColor: 'rgba(25, 118, 210, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'QR 코드 스캔 수',
        data: scannedCounts,
        borderColor: '#2e7d32',
        backgroundColor: 'rgba(46, 125, 50, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 전체 시스템 관리자 차트 데이터 로드
const loadSystemAdminChartData = async (startDate, endDate) => {
  const response = await getAdminStatisticsByPeriod(startDate, endDate, null);

  // 응답 구조에 따라 데이터 추출
  let dates = [];
  let createdCounts = {
    SUPER_ADMIN: [],
    PROJECT_ADMIN: [],
    SUB_ADMIN: [],
    VIEWER: []
  };

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], createdCounts: { SUPER_ADMIN: [...], ... } } }
    dates = response.data.dates || [];

    // createdCounts 객체에서 각 관리자 유형별 데이터 추출
    if (response.data.createdCounts) {
      createdCounts.SUPER_ADMIN = response.data.createdCounts.SUPER_ADMIN || [];
      createdCounts.PROJECT_ADMIN = response.data.createdCounts.PROJECT_ADMIN || [];
      createdCounts.SUB_ADMIN = response.data.createdCounts.SUB_ADMIN || [];
      createdCounts.VIEWER = response.data.createdCounts.VIEWER || [];
    }
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], createdCounts: { SUPER_ADMIN: [...], ... } }
    dates = response.dates || [];

    // createdCounts 객체에서 각 관리자 유형별 데이터 추출
    if (response.createdCounts) {
      createdCounts.SUPER_ADMIN = response.createdCounts.SUPER_ADMIN || [];
      createdCounts.PROJECT_ADMIN = response.createdCounts.PROJECT_ADMIN || [];
      createdCounts.SUB_ADMIN = response.createdCounts.SUB_ADMIN || [];
      createdCounts.VIEWER = response.createdCounts.VIEWER || [];
    }
  } else {
    throw new Error('관리자 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  systemChartData.value = {
    labels: dates,
    datasets: [
      {
        label: '최고 관리자',
        data: createdCounts.SUPER_ADMIN,
        borderColor: '#d32f2f',
        backgroundColor: 'rgba(211, 47, 47, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '프로젝트 관리자',
        data: createdCounts.PROJECT_ADMIN,
        borderColor: '#7b1fa2',
        backgroundColor: 'rgba(123, 31, 162, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '일반 관리자',
        data: createdCounts.SUB_ADMIN,
        borderColor: '#1976d2',
        backgroundColor: 'rgba(25, 118, 210, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '뷰어',
        data: createdCounts.VIEWER,
        borderColor: '#388e3c',
        backgroundColor: 'rgba(56, 142, 60, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 프로젝트별 관리자 차트 데이터 로드
const loadProjectAdminChartData = async (startDate, endDate, projectId) => {
  if (!projectId) {
    console.warn('프로젝트 ID가 없어 프로젝트별 관리자 차트 데이터를 로드할 수 없습니다.');
    return;
  }

  const response = await getAdminStatisticsByPeriod(startDate, endDate, projectId);

  // 응답 구조에 따라 데이터 추출
  let dates = [];
  let createdCounts = {
    SUPER_ADMIN: [],
    PROJECT_ADMIN: [],
    SUB_ADMIN: [],
    VIEWER: []
  };

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], createdCounts: { SUPER_ADMIN: [...], ... } } }
    dates = response.data.dates || [];

    // createdCounts 객체에서 각 관리자 유형별 데이터 추출
    if (response.data.createdCounts) {
      createdCounts.SUPER_ADMIN = response.data.createdCounts.SUPER_ADMIN || [];
      createdCounts.PROJECT_ADMIN = response.data.createdCounts.PROJECT_ADMIN || [];
      createdCounts.SUB_ADMIN = response.data.createdCounts.SUB_ADMIN || [];
      createdCounts.VIEWER = response.data.createdCounts.VIEWER || [];
    }
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], createdCounts: { SUPER_ADMIN: [...], ... } }
    dates = response.dates || [];

    // createdCounts 객체에서 각 관리자 유형별 데이터 추출
    if (response.createdCounts) {
      createdCounts.SUPER_ADMIN = response.createdCounts.SUPER_ADMIN || [];
      createdCounts.PROJECT_ADMIN = response.createdCounts.PROJECT_ADMIN || [];
      createdCounts.SUB_ADMIN = response.createdCounts.SUB_ADMIN || [];
      createdCounts.VIEWER = response.createdCounts.VIEWER || [];
    }
  } else {
    throw new Error('관리자 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  projectChartData.value = {
    labels: dates,
    datasets: [
      {
        label: '최고 관리자',
        data: createdCounts.SUPER_ADMIN,
        borderColor: '#d32f2f',
        backgroundColor: 'rgba(211, 47, 47, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '프로젝트 관리자',
        data: createdCounts.PROJECT_ADMIN,
        borderColor: '#7b1fa2',
        backgroundColor: 'rgba(123, 31, 162, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '일반 관리자',
        data: createdCounts.SUB_ADMIN,
        borderColor: '#1976d2',
        backgroundColor: 'rgba(25, 118, 210, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: '뷰어',
        data: createdCounts.VIEWER,
        borderColor: '#388e3c',
        backgroundColor: 'rgba(56, 142, 60, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 전체 시스템 이벤트 차트 데이터 로드
const loadSystemEventChartData = async (startDate, endDate) => {
  const response = await getEventStatisticsByPeriod(startDate, endDate, null);

  // 응답 구조에 따라 데이터 추출
  let dates = [], createdCounts = [];

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], createdCounts: [...] } }
    dates = response.data.dates || [];
    createdCounts = response.data.createdCounts || [];
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], createdCounts: [...] }
    dates = response.dates || [];
    createdCounts = response.createdCounts || [];
  } else {
    throw new Error('이벤트 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  systemChartData.value = {
    labels: dates,
    datasets: [
      {
        label: '이벤트 생성 수',
        data: createdCounts,
        borderColor: '#ff9800',
        backgroundColor: 'rgba(255, 152, 0, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 프로젝트별 이벤트 차트 데이터 로드
const loadProjectEventChartData = async (startDate, endDate, projectId) => {
  if (!projectId) {
    console.warn('프로젝트 ID가 없어 프로젝트별 이벤트 차트 데이터를 로드할 수 없습니다.');
    return;
  }

  const response = await getEventStatisticsByPeriod(startDate, endDate, projectId);

  // 응답 구조에 따라 데이터 추출
  let dates = [], createdCounts = [];

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], createdCounts: [...] } }
    dates = response.data.dates || [];
    createdCounts = response.data.createdCounts || [];
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], createdCounts: [...] }
    dates = response.dates || [];
    createdCounts = response.createdCounts || [];
  } else {
    throw new Error('이벤트 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  projectChartData.value = {
    labels: dates,
    datasets: [
      {
        label: '이벤트 생성 수',
        data: createdCounts,
        borderColor: '#ff9800',
        backgroundColor: 'rgba(255, 152, 0, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };
};

// 전체 시스템 참가자 차트 데이터 로드
const loadSystemAttendeeChartData = async (startDate, endDate) => {
  const response = await getAttendeeStatisticsByPeriod(startDate, endDate, null);

  // 응답 구조에 따라 데이터 추출
  let dates = [], registeredCounts = [];

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], registeredCounts: [...] } }
    dates = response.data.dates || [];
    registeredCounts = response.data.registeredCounts || [];
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], registeredCounts: [...] }
    dates = response.dates || [];
    registeredCounts = response.registeredCounts || [];
  } else {
    throw new Error('참가자 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  systemChartData.value = {
    labels: dates,
    datasets: [
      {
        label: '등록된 참가자 수',
        data: registeredCounts,
        borderColor: '#e91e63',
        backgroundColor: 'rgba(233, 30, 99, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 프로젝트별 참가자 차트 데이터 로드
const loadProjectAttendeeChartData = async (startDate, endDate, projectId) => {
  if (!projectId) {
    console.warn('프로젝트 ID가 없어 프로젝트별 참가자 차트 데이터를 로드할 수 없습니다.');
    return;
  }

  const response = await getAttendeeStatisticsByPeriod(startDate, endDate, projectId);

  // 응답 구조에 따라 데이터 추출
  let dates = [], registeredCounts = [];

  if (response && response.data) {
    // 응답 형식: { success: true, data: { dates: [...], registeredCounts: [...] } }
    dates = response.data.dates || [];
    registeredCounts = response.data.registeredCounts || [];
  } else if (response && response.dates) {
    // 응답 형식: { dates: [...], registeredCounts: [...] }
    dates = response.dates || [];
    registeredCounts = response.registeredCounts || [];
  } else {
    throw new Error('참가자 서버 응답 구조가 예상과 다릅니다');
  }

  // 차트 데이터 설정
  projectChartData.value = {
    labels: dates,
    datasets: [
      {
        label: '등록된 참가자 수',
        data: registeredCounts,
        borderColor: '#e91e63',
        backgroundColor: 'rgba(233, 30, 99, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

};

// 모든 통계 데이터 로드
const loadAllStatistics = () => {
  // 전체 시스템 통계 (SUPER_ADMIN만 접근 가능)
  if (isSuperAdmin.value) {
    // 시스템 통계 카드 데이터 로드
    loadSystemQrStatistics();
    loadSystemAdminStatistics();
    loadSystemEventStatistics();
    loadSystemAttendeeStatistics();

    // 시스템 차트 데이터 로드 (초기 차트 유형은 'qr')
    systemActiveChartType.value = 'qr';
    loadSystemChartData();
  }

  // 현재 선택된 프로젝트가 있으면 프로젝트 통계 로드
  if (currentProjectId.value) {
    // 프로젝트 통계 카드 데이터 로드
    loadProjectStatistics(currentProjectId.value);

    // 프로젝트 차트 데이터 로드 (초기 차트 유형은 'qr')
    projectActiveChartType.value = 'qr';
    loadProjectChartData(currentProjectId.value);
  }
};



// 프로젝트 변경 이벤트 리스너 설정
const setupProjectChangeListener = () => {
  // 프로젝트 변경 이벤트 리스너 등록
  window.addEventListener('project-changed', (event) => {

    // 프로젝트 ID가 있으면 프로젝트별 통계 로드
    if (event.detail.projectId) {
      loadProjectStatistics(event.detail.projectId);
      loadProjectChartData(event.detail.projectId);
    } 
  });
};

// 컴포넌트 마운트 시 모든 통계 데이터 로드
onMounted(async () => {
  // 프로젝트 변경 이벤트 리스너 설정
  setupProjectChangeListener();

  // SUPER_ADMIN인 경우 항상 모든 프로젝트 로드
  if (isSuperAdmin.value) {
    try {
      const success = await authStore.loadAllProjects();
      if (success) {
        // 전체 프로젝트 모드로 설정
        authStore.selectedProjectIndex = -1;
      } else {
        console.warn('모든 프로젝트 로드 실패');
      }
    } catch (error) {
      console.error('모든 프로젝트 로드 중 오류 발생:', error);
    }
  }

  // 프로젝트 목록이 로드될 때까지 잠시 대기
  await new Promise(resolve => setTimeout(resolve, 500));

  // 모든 통계 데이터 로드 (SUPER_ADMIN은 프로젝트별 통계를 로드하지 않음)
  loadAllStatistics();
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  window.removeEventListener('project-changed', () => {
  });
});

</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.user-info {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.user-info h2 {
  margin-top: 0;
}

hr {
  margin: 20px 0;
  border: 0;
  border-top: 1px solid #eee;
}

h2 {
  margin-top: 30px;
  margin-bottom: 20px;
  color: #333;
}

/* 프로젝트 통계 제목 스타일 */
.project-selection-header {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-selection-header h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.current-project-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.project-name {
  font-weight: bold;
  color: #1976d2;
  font-size: 16px;
}

.project-id {
  color: #666;
  font-size: 14px;
  background-color: #e8e8e8;
  padding: 3px 8px;
  border-radius: 4px;
}

.no-project-message {
  color: #d32f2f;
  font-style: italic;
  margin-top: 10px;
  padding: 10px;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 3px solid #d32f2f;
}

.super-admin-message {
  color: #2e7d32;
  margin-top: 10px;
  padding: 10px;
  background-color: #e8f5e9;
  border-radius: 4px;
  border-left: 3px solid #2e7d32;
}

/* 프로젝트 목록 스타일 */
.project-list {
  margin-top: 20px;
}

.project-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.project-list h4 {
  font-size: 16px;
  margin: 0;
  color: #555;
}

/* 검색 입력 스타일 */
.search-box {
  position: relative;
  width: 280px;
  transition: all 0.3s ease;
}

.search-input {
  padding: 10px 40px 10px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: #2e7d32;
  box-shadow: 0 2px 8px rgba(46, 125, 50, 0.15);
  background-color: #fff;
}

.search-input::placeholder {
  color: #999;
  opacity: 0.8;
}

.search-box::before {
  content: '\f002';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
  pointer-events: none;
  transition: color 0.3s ease;
}

.search-input:focus + .search-box::before {
  color: #2e7d32;
}

.search-input {
  padding-left: 40px;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: #d32f2f;
  background-color: rgba(211, 47, 47, 0.1);
}

.project-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.project-list li {
  flex: 1;
  min-width: 200px;
  max-width: calc(20% - 8px); /* 한 줄에 최대 5개 */
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* 모바일 반응형 스타일 */
@media (max-width: 1200px) {
  .project-list li {
    max-width: calc(25% - 8px); /* 한 줄에 최대 4개 */
  }
}

@media (max-width: 992px) {
  .project-list li {
    max-width: calc(33.33% - 8px); /* 한 줄에 최대 3개 */
  }
}

@media (max-width: 768px) {
  .project-list li {
    max-width: calc(50% - 5px); /* 한 줄에 최대 2개 */
    min-width: 150px;
  }
}

@media (max-width: 480px) {
  .project-list li {
    max-width: 100%; /* 한 줄에 1개 */
  }

  .project-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .search-box {
    width: 100%;
  }
}

.project-list li:hover {
  background-color: #f0f7f0;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
  border-color: #c8e6c9;
}

.project-list li.active {
  background-color: #e8f5e9;
  border-color: #2e7d32;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2);
}

.project-list li.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: #2e7d32;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.project-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.project-item-name {
  font-weight: 600;
  color: #333;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
}

.project-list li:hover .project-item-name {
  color: #2e7d32;
}

.project-list li.active .project-item-name {
  color: #2e7d32;
}

.project-item-id {
  font-size: 12px;
  color: #757575;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 3px 8px;
  border-radius: 12px;
  display: inline-block;
  max-width: fit-content;
}

.no-projects-message {
  margin-top: 15px;
  color: #666;
  font-style: italic;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
}

.load-all-projects-btn {
  background-color: #2e7d32;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 5px rgba(46, 125, 50, 0.2);
  margin: 10px auto;
}

.load-all-projects-btn:hover {
  background-color: #1b5e20;
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
  transform: translateY(-2px);
}

/* 페이징 UI 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.page-btn {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
  padding: 0;
  margin: 0 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: #555;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.page-btn:hover:not(:disabled) {
  background-color: #e8f5e9;
  border-color: #2e7d32;
  color: #2e7d32;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.15);
}

.page-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.page-info {
  font-size: 14px;
  color: #555;
  margin: 0 15px;
  background-color: #f5f5f5;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
}

/* 통계 섹션 스타일 */
.statistics-section {
  margin-top: 20px;
}

/* 프로젝트별 통계 섹션 스타일 */
.project-statistics .section-header {
  background-color: #e8f5e9;
}

.project-statistics .section-header h2 {
  color: #2e7d32;
}

/* 전체 시스템 통계 섹션 스타일 */
.system-statistics .section-header {
  background-color: #e3f2fd;
}

.system-statistics .section-header h2 {
  color: #1976d2;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.section-header:hover {
  background-color: #e0e0e0;
}

.section-header h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.toggle-btn {
  background: none;
  border: none;
  color: #555;
  font-size: 16px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.toggle-btn:hover {
  background-color: #ddd;
}

/* 애니메이션 효과 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, max-height 0.5s ease;
  max-height: 2000px;
  overflow: hidden;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  max-height: 0;
}

/* 로딩 및 오류 상태 스타일 */
.card-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 150px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #1976d2;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.card-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 150px;
  padding: 20px;
  text-align: center;
  color: #d32f2f;
}

.card-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.card-error p {
  margin-bottom: 15px;
}

.retry-btn-small {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.retry-btn-small:hover {
  background-color: #d32f2f;
}

/* 프로젝트별 차트 섹션 스타일 */
.project-chart {
  margin-top: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-chart .chart-header {
  margin-bottom: 20px;
}

.project-chart .chart-header h2 {
  color: #2e7d32;
  margin-bottom: 15px;
  font-size: 18px;
}

/* 차트 컨트롤 스타일 */
.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

/* 차트 타입 선택기 스타일 */
.chart-type-selector {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-type-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #555;
  transition: all 0.2s ease;
}

.chart-type-btn:hover {
  background-color: #e0e0e0;
}

.chart-type-btn.active {
  background-color: #2e7d32;
  color: white;
  font-weight: 500;
}

/* 차트 기간 선택기 스타일 */
.chart-period-selector {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: #f9f9f9;
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e0e0e0;
}

.date-inputs {
  display: flex;
  gap: 15px;
  flex: 1;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.date-input-group label {
  font-size: 13px;
  color: #555;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 2px;
}

.input-with-icon {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.input-with-icon i {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #2e7d32;
  pointer-events: none;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  z-index: 1; /* 아이콘이 입력 필드 위에 표시되도록 z-index 추가 */
}

.date-input {
  padding: 10px 40px 10px 12px; /* 오른쪽 패딩 증가 */
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: white;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  position: relative; /* 위치 지정 */
  appearance: none; /* 기본 달력 아이콘 제거 */
  -webkit-appearance: none; /* Safari용 */
}

.date-input::-webkit-calendar-picker-indicator {
  opacity: 0; /* 기본 달력 아이콘 숨기기 */
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: pointer;
}

.date-input:focus {
  outline: none;
  border-color: #2e7d32;
  box-shadow: 0 2px 8px rgba(46, 125, 50, 0.15);
}

.date-input:focus + i {
  opacity: 1;
}

.search-btn-container {
  display: flex;
  align-items: flex-end; /* 버튼을 아래쪽으로 정렬 */
  margin-left: 15px; /* 날짜 입력과의 간격 */
}

.search-btn {
  background-color: #2e7d32;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 5px rgba(46, 125, 50, 0.2);
  min-width: 100px;
  justify-content: center;
  height: 42px;
}

.search-btn:hover {
  background-color: #1b5e20;
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
  transform: translateY(-2px);
}

.search-btn i {
  font-size: 14px;
}

.project-chart .chart-loading,
.project-chart .chart-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  text-align: center;
}

.project-chart .chart-error {
  color: #d32f2f;
}

.project-chart .chart-error i {
  font-size: 32px;
  margin-bottom: 15px;
}

.project-chart .retry-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-top: 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.project-chart .retry-btn:hover {
  background-color: #d32f2f;
}

/* 모바일 반응형 스타일 */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 15px;
  }

  .chart-type-selector {
    width: 100%;
    overflow-x: auto;
    justify-content: center;
  }

  .chart-period-selector {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
    padding: 15px;
  }

  .date-inputs {
    width: 100%;
    flex-direction: column;
    gap: 15px;
  }

  .date-input-group {
    width: 100%;
  }

  .date-input {
    width: 100%;
  }

  .search-btn-container {
    margin-left: 0;
    margin-top: 15px;
    width: 100%;
  }

  .search-btn {
    width: 100%;
    justify-content: center;
    height: 46px;
  }
}

@media (max-width: 480px) {
  .chart-type-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
}

.statistics-cards {
  display: flex;
  flex-wrap: nowrap; /* 줄바꿈 방지 */
  gap: 16px;
  margin-top: 16px;
  justify-content: space-between; /* 균등 분배 */
  width: 100%; /* 전체 너비 사용 */
}

.stat-card {
  flex: 1;
  width: calc(25% - 12px); /* 한 줄에 4개 카드 */
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  align-items: flex-start;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative; /* 로딩 및 에러 표시를 위한 상대 위치 설정 */
  min-height: 180px; /* 최소 높이 설정 - 관리자 카드를 위해 증가 */
}

.stat-card.clickable {
  cursor: pointer;
}

.stat-card.clickable:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.active {
  border: 2px solid #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.admin-stats-card .stat-icon {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.no-data {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 10px 0;
}

.stat-icon {
  width: 40px;
  height: 40px;
  background-color: #f0f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: #1976d2;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  overflow: hidden;
}

.stat-content h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stat-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
}

.stat-number {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  white-space: nowrap;
}

.stat-update-time {
  margin-top: 10px;
  font-size: 11px;
  color: #999;
  text-align: right;
  font-style: italic;
}

/* 카드 내 로딩 및 에러 표시 */
.card-loading, .card-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  z-index: 1;
}

.card-error {
  color: #d32f2f;
  font-size: 24px;
}

.retry-btn-small {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 8px;
  font-size: 12px;
}

.retry-btn-small:hover {
  background-color: #b71c1c;
}

/* 로딩 스피너 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 에러 메시지 */
.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  text-align: center;
}

.retry-btn {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.retry-btn:hover {
  background-color: #b71c1c;
}

/* 차트 섹션 스타일 */
.chart-section {
  margin-top: 40px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-section h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.chart-tabs {
  display: flex;
  gap: 10px;
}

.chart-tab {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.chart-tab:hover {
  background-color: #e0e0e0;
}

.chart-tab.active {
  background-color: #1976d2;
  color: white;
  border-color: #1976d2;
}

.period-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.date-inputs {
  display: flex;
  gap: 16px;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group label {
  margin-right: 8px;
  font-weight: 500;
  color: #555;
}

.input-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-btn {
  padding: 8px 16px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-btn:hover {
  background-color: #1565c0;
}

.search-btn:disabled {
  background-color: #bbdefb;
  cursor: not-allowed;
}

/* 반응형 디자인 */
@media (max-width: 1200px) {
  .statistics-cards {
    flex-wrap: wrap;
  }

  .stat-card {
    width: calc(50% - 8px);
    margin-bottom: 16px;
  }

  .date-inputs {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .stat-card {
    width: 100%;
  }

  .period-selector {
    flex-direction: column;
    gap: 16px;
  }

  .date-inputs {
    width: 100%;
  }

  .input-group {
    width: 100%;
    justify-content: space-between;
  }

  .search-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
