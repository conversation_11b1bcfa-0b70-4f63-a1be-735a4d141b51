<template>
  <div class="qna-management">  
    <h1>QnA 관리</h1>
    
    <!-- 모달 삭제 - 답변 기능을 문서 내에 바로 구현 -->
    <div>
      <div class="filters">
        <div class="filter-group">
          <button @click="createQuestion" class="create-btn">새 질문 등록</button>
          
          <!-- 페이지당 항목 수 선택 UI -->
          <div class="items-per-page-selector">
            <span>페이지당 항목:</span>
            <div class="items-per-page-buttons">
              <button 
                @click="changeItemsPerPage(10)" 
                :class="['item-count-btn', qnaStore.pageSize === 10 ? 'active' : '']">
                10개
              </button>
              <button 
                @click="changeItemsPerPage(30)" 
                :class="['item-count-btn', qnaStore.pageSize === 30 ? 'active' : '']">
                30개
              </button>
              <button 
                @click="changeItemsPerPage(50)" 
                :class="['item-count-btn', qnaStore.pageSize === 50 ? 'active' : '']">
                50개
              </button>
            </div>
          </div>
        </div>
        <div class="filter-group">
          <label for="searchTypeSelect">검색 유형:</label>
          <select id="searchTypeSelect" v-model="searchType" class="search-type">
            <option v-for="type in qnaStore.availableSearchTypes.length > 0 ? qnaStore.availableSearchTypes : defaultSearchTypes" 
              :key="type.value" 
              :value="type.value">
              {{ type.label }}
            </option>
          </select>

          <label for="answerTypeSelect">답변 유형:</label>
          <select id="answerTypeSelect" v-model="selectedAnswerType" class="answer-type-select">
            <option 
              v-for="type in qnaStore.answerTypes" 
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </option>
          </select>

          <label for="searchInput">검색어:</label>
          <input
            id="searchInput"
            type="text"
            v-model="searchQuery"
            placeholder="검색어 입력"
            @input="() => {}"
            @keyup.enter="searchQna"
            class="search-input"
            />
            
            

          <button @click="searchQna" class="search-btn">검색</button>
          <button @click="handleClear" class="reset-btn">초기화</button>
        </div>
      </div>

    <!-- 질문 목록 -->
    <div v-if="qnaStore.loading" class="loading">
      <div class="spinner"></div>
      <p>데이터를 불러오는 중...</p>
    </div>
    
    <div v-else-if="qnaStore.error" class="error">
      <p>{{ qnaStore.error }}</p>
      <button @click="fetchQuestions" class="retry-button">다시 시도</button>
    </div>
    
    <div v-else-if="!qnaStore.hasQuestions" class="empty-state">
      <p>등록된 질문이 없습니다.</p>
    </div>
    
    <div v-else class="question-list">
      <div class="qna-header">
        <div class="qna-header-item qna-id">번호</div>
        <div class="qna-header-item qna-title">제목</div>
        <div class="qna-header-item qna-author">작성자</div>
        <div class="qna-header-item qna-date">작성일</div>
        <div class="qna-header-item qna-status">답변 상태</div>
      </div>

      <div v-for="question in qnaStore.questions" :key="question.qnaQuestionId" class="qna-accordion" :class="{ 'expanded': openQuestionId === question.qnaQuestionId }">
        <!-- 질문 정보 (항상 표시) -->
        <div 
          class="qna-summary" 
          @click="toggleDialog(question.qnaQuestionId)"
          :class="{ 'active': openQuestionId === question.qnaQuestionId, 'editing': editingQuestionId === question.qnaQuestionId }"
        >
          <div class="qna-summary-item qna-id">{{ calculateIndex(qnaStore.questions.indexOf(question)) }}</div>
          <div class="qna-summary-item qna-title">{{ question.title }}</div>
          <div class="qna-summary-item qna-author">{{ question.createUserEmail }}</div>
          <div class="qna-summary-item qna-date">{{ formatDate(question.createDate) }}</div>
          <div class="qna-summary-item qna-status">
            <span :class="question.answer && question.answer.answerContent ? 'status-answered' : 'status-waiting'">
              {{ question.answer && question.answer.answerContent ? '답변 완료' : '답변 대기중' }}
            </span>
          </div>
        </div>

        <!-- 질문 상세 내용 (dialog 태그 활용) -->
        <dialog :open="openQuestionId === question.qnaQuestionId" class="qna-dialog">
          <div class="qna-dialog-content" style="max-height: 500px; overflow-y: auto; background-color: rgba(255, 255, 255, 0.9);">
            <div class="qna-detail-section">
              <h3>질문 내용</h3>
              <div v-if="editingQuestionId === question.qnaQuestionId">
                <textarea v-model="editQuestionContent" class="edit-textarea" placeholder="질문 내용"></textarea>
              </div>
              <p v-else>{{ question.content }}</p>
            </div>
            
            <!-- 기존 답변 표시 영역 -->
            <div v-if="question.answer && question.answer.answerContent" class="qna-answer-section">
              <h3>답변</h3>
              <!-- 수정 모드일 때는 텍스트에리어 표시 -->
              <div v-if="editingAnswerId === question.answer.qnaAnswerId">
                <textarea 
                  v-model="editAnswerContent" 
                  placeholder="답변 내용을 입력하세요"
                  class="answer-textarea"
                  rows="5"
                ></textarea>
                <div class="answer-form-actions">
                  <button @click="saveEditedAnswer(question.answer.qnaAnswerId, question.qnaQuestionId)" class="submit-answer-button">답변 수정 저장</button>
                  <!-- <button @click="cancelEditingAnswer()" class="cancel-answer-button">취소</button> -->
                </div>
              </div>
              <!-- 읽기만 하는 모드 -->
              <div v-else>
                <p>{{ question.answer.answerContent }}</p>
                <p class="answer-info">답변일: {{ formatDate(question.answer.createDate) }}</p>
                <!-- SUPER_ADMIN에게만 답변 수정 버튼 표시 -->
                <div v-if="isSuperAdmin" class="answer-actions">
                  <button @click="startEditingAnswer(question.answer)" class="edit-answer-button">답변 수정</button>
                </div>
              </div>
              
              <!-- 답변 수정 성공/오류 메시지 -->
              <transition name="fade">
                <div v-if="answerUpdateSuccess && question.answer.qnaAnswerId === editingAnswerId" class="answer-success-message">
                  <i class="success-icon">✓</i> 답변이 성공적으로 수정되었습니다.
                </div>
              </transition>
              <transition name="fade">
                <div v-if="answerUpdateError && question.answer.qnaAnswerId === editingAnswerId" class="answer-error-message">
                  <i class="error-icon">✗</i> 답변 수정 실패: {{ answerUpdateError }}
                </div>
              </transition>
            </div>
            
            <!-- 답변 등록 폼 - 답변이 없는 경우에만 표시 -->
            <div v-if="isSuperAdmin && (!question.answer || !question.answer.answerContent)" class="qna-answer-form-section">
              <h3>답변 작성하기</h3>
              <textarea 
                v-model="answerContent" 
                placeholder="답변 내용을 입력하세요"
                class="answer-textarea"
                rows="5"
              ></textarea>
              <div class="answer-form-actions">
                <button @click="submitAnswer(question.qnaQuestionId)" class="submit-answer-button">답변 저장</button>
                <!-- <button @click="cancelAnswering()" class="cancel-answer-button">취소</button> -->
              </div>
              <transition name="fade">
                <div v-if="answerSuccess && question.qnaQuestionId === answeringQuestionId" class="answer-success-message">
                  <i class="success-icon">✓</i> 답변이 성공적으로 저장되었습니다.
                </div>
              </transition>
              <transition name="fade">
                <div v-if="answerError && question.qnaQuestionId === answeringQuestionId" class="answer-error-message">
                  <i class="error-icon">✗</i> 답변 실패: {{ answerError }}
                </div>
              </transition>
            </div>
            
            <!-- 저장/삭제 성공/실패 메시지 -->
            <transition name="fade">
              <div v-if="saveSuccess && question.qnaQuestionId === openQuestionId" class="save-success-message">
                <i class="success-icon">✓</i> 질문이 성공적으로 수정되었습니다.
              </div>
            </transition>
            <transition name="fade">
              <div v-if="saveError && question.qnaQuestionId === openQuestionId" class="save-error-message">
                <i class="error-icon">✗</i> 수정 실패: {{ saveError }}
              </div>
            </transition>
            
            <!-- 삭제 성공/실패 메시지 -->
            <transition name="fade">
              <div v-if="deleteSuccess && question.qnaQuestionId === targetQuestionId.value" class="save-success-message">
                <i class="success-icon">✓</i> 질문이 성공적으로 삭제되었습니다.
              </div>
            </transition>
            <transition name="fade">
              <div v-if="deleteError && question.qnaQuestionId === targetQuestionId.value" class="save-error-message">
                <i class="error-icon">✗</i> 삭제 실패: {{ deleteError }}
              </div>
            </transition>
            
            <div class="qna-dialog-actions">
              <div v-if="editingQuestionId === question.qnaQuestionId">
                <button @click="saveEditedQuestion(question.qnaQuestionId)" class="save-button">저장</button>
                <button @click="cancelEditing()" class="cancel-button">취소</button>
              </div>
              <div v-else>
                <!-- 질문 작성자인 경우 -->
                <template v-if="isQuestionAuthor(question)">
                  <!-- 답변이 없는 경우만 수정 가능 -->
                  <button v-if="!hasAnswer(question)" @click="startEditing(question)" class="edit-button">수정</button>
                  <!-- 삭제는 답변 여부와 관계없이 가능 -->
                  <button @click="confirmDeleteQuestion(question.qnaQuestionId)" class="delete-button">삭제</button>
                </template>
                <button @click="toggleDialog(null)" class="close-button">닫기</button>
              </div>
            </div>
          </div>
        </dialog>
      </div>

      <!-- 페이지네이션 -->
      <div class="pagination" v-if="qnaStore.questions.length > 0">
        <button
          @click="setPage(1)"
          :disabled="qnaStore.displayPage === 1"
          class="pagination-btn"
        >
          &laquo;
        </button>
        <button
          @click="setPage(qnaStore.displayPage - 1)"
          :disabled="qnaStore.displayPage === 1"
          class="pagination-btn"
        >
          &lt;
        </button>

        <span class="page-info">{{ qnaStore.displayPage }} / {{ qnaStore.totalPages || 1 }}</span>

        <button
          @click="setPage(qnaStore.displayPage + 1)"
          :disabled="qnaStore.displayPage >= qnaStore.totalPages"
          class="pagination-btn"
        >
          &gt;
        </button>
        <button
          @click="setPage(qnaStore.totalPages)"
          :disabled="qnaStore.displayPage >= qnaStore.totalPages"
          class="pagination-btn"
        >
          &raquo;
        </button>
      </div>
    </div>

    </div> <!-- qna-list-container 닫는 태그 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useQnaStore } from '@/stores/qnaStore';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const qnaStore = useQnaStore();
const authStore = useAuthStore();

// 기본 검색 유형 목록 (서버에서 제공하지 않을 경우 사용)
const defaultSearchTypes = [
  { value: 'title', label: '제목' },
  { value: 'content', label: '내용' },
  { value: 'author', label: '작성자' }
];

// 검색 관련 설정
const searchQuery = ref('');
const searchType = ref(qnaStore.availableSearchTypes.length > 0 ? qnaStore.availableSearchTypes[0].value : 'title'); // 기본값 제목 기준 검색

// 답변 유형 필터 - 기본값은 null로 설정하여 비어있지 않은 값으로 설정
// 나중에 watch로 서버에서 데이터가 로드되면 처리
const selectedAnswerType = ref(null);

// 현재 열린 질문 ID 상태 관리
const openQuestionId = ref(null);

// 수정 중인 질문 ID
const editingQuestionId = ref(null);

// 수정 폼 데이터
const editQuestionTitle = ref('');
const editQuestionContent = ref('');

// 답변 관련 상태 관리
const answeringQuestionId = ref(null);
const answerContent = ref('');
const answerSuccess = ref(false);
const answerError = ref('');

// 답변 수정 관련 상태 관리
const editingAnswerId = ref(null);
const editAnswerContent = ref('');
const answerUpdateSuccess = ref(false);
const answerUpdateError = ref('');

// 관리자 권한 확인 (스토어에서 역할 확인)
const isSuperAdmin = computed(() => {
  // userRoles가 존재하는지 먼저 확인
  return authStore.user.roleId === 'SUPER_ADMIN';
});

// 현재 로그인한 사용자가 질문 작성자인지 확인
const isQuestionAuthor = (question) => {
  // 현재 로그인한 사용자 정보
  const currentUser = authStore.user;
  
  // 질문 작성자 확인 (이메일 비교)
  if (!currentUser || !question) return false;
  
  // 모든 사용자(관리자 포함)는 자신이 작성한 질문만 수정/삭제 가능
  return currentUser.userEmail === question.createUserEmail;
};

// 질문에 답변이 있는지 확인
const hasAnswer = (question) => {
  return question.answer && question.answer.answerContent && question.answer.answerContent.trim() !== '';
};

// 저장 상태 관리
const saveSuccess = ref(false);
const saveError = ref('');

// 삭제 상태 관리
const deleteSuccess = ref(false);
const deleteError = ref('');
const targetQuestionId = ref(null); // 삭제 대상 질문 ID

// 페이지당 항목 수 변경 함수
const changeItemsPerPage = (count) => {
  qnaStore.setPageSize(count);
  fetchQuestions();
};

// 질문 아코디언 토글 함수
const toggleDialog = (questionId) => {
  // 수정 중이면 토글하지 않음
  if (editingQuestionId.value !== null && questionId !== null) {
    return;
  }
  openQuestionId.value = openQuestionId.value === questionId ? null : questionId;
  
  // 아코디언을 닫을 때 수정 모드도 취소
  if (openQuestionId.value === null) {
    editingQuestionId.value = null;
  }
};

// 수정 시작
const startEditing = (question) => {
  editingQuestionId.value = question.qnaQuestionId;
  editQuestionTitle.value = question.title;
  editQuestionContent.value = question.content;
};

// 수정 취소
const cancelEditing = () => {
  editingQuestionId.value = null;
  editQuestionTitle.value = '';
  editQuestionContent.value = '';
};

// 성공/실패 메시지 자동 지우기
const clearSuccessMessage = () => {
  if (saveSuccess.value || deleteSuccess.value) {
    setTimeout(() => {
      saveSuccess.value = false;
      deleteSuccess.value = false;
    }, 3000); // 3초 후 자동으로 메시지 삭제
  }
};

// 수정된 질문 저장
const saveEditedQuestion = async (questionId) => {
  try {
    // 초기화
    saveSuccess.value = false;
    saveError.value = '';
    
    const result = await qnaStore.updateQuestionAction(questionId, {
      title: editQuestionTitle.value,
      content: editQuestionContent.value
    });
    
    if (result.success) {
      // 성공적으로 수정되면 전체 리스트를 다시 불러오지 않고 해당 질문만 업데이트
      // 질문 목록에서 해당 질문 찾기
      const questionIndex = qnaStore.questions.findIndex(q => q.qnaQuestionId === questionId);
      
      if (questionIndex !== -1) {
        // 해당 질문의 내용 업데이트 (원본 유지하면서 필요한 필드만 변경)
        const updatedQuestion = { ...qnaStore.questions[questionIndex] };
        updatedQuestion.content = editQuestionContent.value;
        
        // 해당 질문만 로컬에서 업데이트 (참조 반영을 위해 splice 사용)
        qnaStore.questions.splice(questionIndex, 1, updatedQuestion);
      }
      
      // alert 대신 상태 설정
      saveSuccess.value = true;
      clearSuccessMessage(); // 자동 삭제 타이머 설정
      
      // 평럼한 토글 전환 (다음 트릭으로 임시 동기화 문제 해결)
      setTimeout(() => {
        editingQuestionId.value = null;
      }, 100);
    } else {
      saveError.value = result.error || '알 수 없는 오류가 발생했습니다.';
    }
  } catch (error) {
    saveError.value = error.message || '알 수 없는 오류가 발생했습니다.';
  }
};

// 컴포넌트 마운트 시 질문 목록 조회
onMounted(() => {
  fetchQuestions();
  
  // 데이터가 로드된 후에 watch에서 기본값 설정함
});

// answerTypes가 로드될 때 자동으로 처리
watch(() => qnaStore.answerTypes, (newTypes) => {
  // 답변 유형 데이터가 배열이고 참이면 값 설정
  if (newTypes && Array.isArray(newTypes) && newTypes.length > 0) {
    // 기본값을 설정하지 않았거나 업데이트가 필요할 때
    if (!selectedAnswerType.value || selectedAnswerType.value === '') {
      // 첫 번째 옵션을 기본값으로 설정
      selectedAnswerType.value = newTypes[0].value;
    }
  }
}, { immediate: true });

// 프로젝트 변경 시 질문 목록 다시 조회
watch(() => authStore.currentProject, () => {
  fetchQuestions();
});

// 질문 목록 조회 함수
const fetchQuestions = async () => {
  await qnaStore.fetchQuestionsAction();
};

// 검색 처리 - 검색 버튼 클릭 시 실행
const searchQna = () => {
  // 검색어와 검색 유형, 답변 유형을 store에 설정하고 데이터 요청
  qnaStore.setSearchFilter(searchQuery.value, searchType.value, selectedAnswerType.value);
  fetchQuestions(); // 실제 데이터 로딩 함수 호출
};

// 검색 초기화
const handleClear = () => {
  // 서버에서 검색 유형이 제공되면 첫 번째 유형 사용, 아니면 'title' 기본값 사용
  if (qnaStore.availableSearchTypes.length > 0) {
    searchType.value = qnaStore.availableSearchTypes[0].value;
  } else {
    searchType.value = 'title';
  }
  searchQuery.value = '';
  // 서버에서 받은 첫 번째 옵션으로 초기화
  selectedAnswerType.value = qnaStore.answerTypes.length > 0 ? qnaStore.answerTypes[0].value : '';
  qnaStore.setSearchFilter('', searchType.value, '');
  fetchQuestions(); // 검색 초기화 후 데이터 다시 로드
};

// 페이지 이동
const setPage = (page) => {
  if (page < 1 || page > qnaStore.totalPages) return;
  qnaStore.setPage(page);
};

// 리스트 순서 번호 계산 함수 (역순으로 표시 - 가장 최근 항목이 1번)
const calculateIndex = (index) => {
  // 전체 데이터 개수 계산
  const totalItems = qnaStore.totalItems;
  
  // 현재 페이지, 페이지 크기를 고려하여 순서 번호 계산
  // qnaStore.displayPage는 1부터 시작하는 번호
  const startIndex = (qnaStore.displayPage - 1) * qnaStore.pageSize;
  
  // 역순으로 번호 계산 (totalItems에서 현재 순서번호를 뺌)
  return totalItems - (startIndex + index);
};

// 페이지네이션 표시 계산
const displayedPages = computed(() => {
  const totalPages = qnaStore.totalPages;
  const currentPage = qnaStore.displayPage; // displayPage 사용 (1부터 시작하는 번호)
  
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }
  
  if (currentPage <= 3) {
    return [1, 2, 3, 4, 5, '...', totalPages];
  }
  
  if (currentPage >= totalPages - 2) {
    return [1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }
  
  return [1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages];
});

// 날짜 포맷팅 함수
const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  // 시간도 표시하도록 추가
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 질문 상세 조회
const viewQuestion = (questionId) => {
  router.push({ name: 'qna-detail', params: { questionId } });
};

// 질문 수정 - 더 이상 외부 페이지로 이동하지 않음
// 이 함수는 사용하지 않음
const editQuestion = (questionId) => {
  // 더 이상 외부 페이지로 이동하지 않음
  // router.push({ name: 'qna-edit', params: { questionId } });
};

// 답변 수정 시작
const startEditingAnswer = (answer) => {
  editingAnswerId.value = answer.qnaAnswerId;
  editAnswerContent.value = answer.answerContent;
  answerUpdateSuccess.value = false;
  answerUpdateError.value = '';
};

// 답변 수정 취소
const cancelEditingAnswer = () => {
  editingAnswerId.value = null;
  editAnswerContent.value = '';
  answerUpdateSuccess.value = false;
  answerUpdateError.value = '';
};

// 수정된 답변 저장
const saveEditedAnswer = async (answerId, questionId) => {
  if (!editAnswerContent.value.trim()) {
    answerUpdateError.value = '답변 내용을 입력해주세요.';
    return;
  }
  
  try {
    // 초기화
    answerUpdateSuccess.value = false;
    answerUpdateError.value = '';
    
    const result = await qnaStore.updateAnswerAction(
      answerId,
      editAnswerContent.value,
      questionId
    );
    
    if (result.success) {
      // 성공 후 바로 처리
      editingAnswerId.value = null;
      editAnswerContent.value = '';
      
      // 질문 목록 즉시 새로고침하여 수정된 답변 표시
      await fetchQuestions();
    } else {
      answerUpdateError.value = result.error || '알 수 없는 오류가 발생했습니다.';
    }
  } catch (error) {
    answerUpdateError.value = error.message || '알 수 없는 오류가 발생했습니다.';
  }
};

// 질문 삭제 확인
const confirmDeleteQuestion = (questionId) => {
  if (confirm('정말로 이 질문을 삭제하시겠습니까?')) {
    targetQuestionId.value = questionId; // 삭제 대상 질문 ID 저장
    deleteQuestion(questionId);
  }
};

// 답변 시작
const answerModal = ref(null);
const startAnswering = (question) => {
  answeringQuestionId.value = question.qnaQuestionId;
  answerContent.value = '';
  answerSuccess.value = false;
  answerError.value = '';
  if (answerModal.value) {
    answerModal.value.showModal();
  }
};

// 답변 취소
const cancelAnswering = () => {
  answeringQuestionId.value = null;
  answerContent.value = '';
  if (answerModal.value) {
    answerModal.value.close();
  }
};

// 답변 성공/실패 메시지 자동 삭제
const clearAnswerMessage = () => {
  if (answerSuccess.value) {
    setTimeout(() => {
      answerSuccess.value = false;
    }, 3000); // 3초 후 자동으로 메시지 삭제
  }
  
  if (answerUpdateSuccess.value) {
    setTimeout(() => {
      answerUpdateSuccess.value = false;
    }, 3000); // 3초 후 자동으로 메시지 삭제
  }
};

// 답변 제출
const submitAnswer = async (questionId) => {
  if (!answerContent.value.trim()) {
    answerError.value = '답변 내용을 입력해주세요.';
    return;
  }
  
  try {
    // 초기화
    answerSuccess.value = false;
    answerError.value = '';
    
    const result = await qnaStore.createAnswerAction(
      questionId,  // 파라미터로 전달받은 questionId 사용
      answerContent.value
    );
    
    if (result.success) {
      // 성공 후 바로 처리
      answeringQuestionId.value = null;
      answerContent.value = '';
      if (answerModal.value) {
        answerModal.value.close();
      }
      
      // 질문 목록 즉시 새로고침하여 답변 표시
      await fetchQuestions();
    } else {
      answerError.value = result.error || '알 수 없는 오류가 발생했습니다.';
    }
  } catch (error) {
    answerError.value = error.message || '알 수 없는 오류가 발생했습니다.';
  }
};

// 질문 삭제 처리
const deleteQuestion = async (questionId) => {
  // 삭제 상태 초기화
  deleteSuccess.value = false;
  deleteError.value = '';
  
  try {
    const result = await qnaStore.deleteQuestionAction(questionId);
    
    if (result.success) {
      // 성공 상태 설정
      deleteSuccess.value = true;
      clearSuccessMessage();
      await fetchQuestions(); // 리스트 다시 불러오기 (삭제인 경우 리스트를 새로 불러와야 함)
    } else {
      // 삭제 실패 시 서버 오류 내용 저장
      deleteError.value = result.error || '알 수 없는 오류가 발생했습니다.';
      
      // 오류의 상세 내용이 있는지 확인하여 포함
      if (result.details) {
        deleteError.value += ` (${result.details})`;
      }
      
      // 서버 응답 구체적인 오류 정보 추가
      if (result.statusCode) {
        deleteError.value += ` [Status: ${result.statusCode}]`;
      }
    }
  } catch (error) {
    // 예외 문제 발생 시 오류 정보 저장
    deleteError.value = error.message || '알 수 없는 오류가 발생했습니다.';
    console.error('질문 삭제 중 오류:', error);
  }
};

// 새 질문 작성
const createQuestion = () => {
  router.push({ name: 'qna-create' });
};

</script>

<style scoped>
/* 스타일링 */
.qna-management {
  width: 100%;
  padding: 20px;
}

.qna-management h1 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
}

.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.create-btn {
  padding: 8px 16px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.items-per-page-buttons {
  display: flex;
  margin-left: 5px;
}

.item-count-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  font-size: 0.8rem;
}

.search-type {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 5px;
}

.search-input {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 5px;
}

.search-btn {
  padding: 8px 16px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.reset-btn {
  padding: 8px 16px;
  background-color: #9e9e9e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* QnA 아코디언 스타일 */
.question-list {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.qna-header {
  display: flex;
  background-color: #f2f7fd;
  padding: 12px 16px;
  font-weight: bold;
  border-bottom: 1px solid #e0e0e0;
}

.qna-header-item {
  padding: 8px;
}

.qna-accordion {
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  overflow: hidden;
}

.qna-accordion.expanded {
  margin-bottom: 20px;
  z-index: 5;
}

.qna-summary {
  display: flex;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.qna-summary:hover {
  background-color: #f9f9f9;
}

.qna-summary.active {
  background-color: #e6f2ff;
}

.qna-summary.editing {
  background-color: #e8f5e9;
}

.qna-summary-item {
  padding: 8px;
}

/* 열/항목 너비 설정 */
.qna-id {
  width: 8%;
  flex-shrink: 0;
  text-align: center;
}

.qna-title {
  width: 50%;
  flex-grow: 1;
}

.qna-author {
  width: 15%;
  flex-shrink: 0;
}

.qna-date {
  width: 15%;
  flex-shrink: 0;
}

.qna-status {
  width: 12%;
  flex-shrink: 0;
  text-align: center;
}

/* 상태 스타일 */
.status-answered {
  color: #2e7d32;
  font-weight: bold;
  padding: 4px 8px;
  background-color: #e8f5e9;
  border-radius: 12px;
  display: inline-block;
}

.status-waiting {
  color: #f57c00;
  font-weight: bold;
  padding: 4px 8px;
  background-color: #fff3e0;
  border-radius: 12px;
  display: inline-block;
}

/* Dialog 스타일 */
.qna-dialog {
  width: 100%;
  margin: 0;
  padding: 0;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  max-height: 500px;
  position: relative;
  z-index: 10;
}

.qna-dialog[open] {
  display: block;
  background-color: white;
  position: relative;
  z-index: 10;
  margin-bottom: 0;
  border-bottom: 1px solid #e0e0e0;
  overflow: hidden;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.qna-dialog-content {
  padding: 20px;
  position: relative;
  background-color: white;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
  max-height: 350px;
  overflow-y: auto;
}

.qna-detail-section,
.qna-answer-section {
  padding: 15px;
  margin-bottom: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.qna-detail-section h3,
.qna-answer-section h3 {
  margin-top: 0;
  color: #455a64;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.qna-answer-section {
  background-color: #fcfeff;
  border-left: 4px solid #1976d2;
}

.answer-info {
  margin-top: 10px;
  font-size: 0.85em;
  color: #757575;
  text-align: right;
}

.qna-dialog-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 버튼 스타일 */
.edit-button,
.delete-button,
.close-button,
.save-button,
.cancel-button,
.answer-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  margin: 0 3px;
  color: white;
}

.save-button {
  background-color: #4caf50;
}

.cancel-button {
  background-color: #9e9e9e;
}

.edit-button {
  background-color: #2196f3;
}

.delete-button {
  background-color: #e74c3c;
}

.answer-button {
  background-color: #9b59b6;
}

.close-button {
  background-color: #7f8c8d;
}

/* 답변 모달 스타일 */
.answer-modal {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 20px;
  width: 90%;
  max-width: 600px;
}

.answer-modal::backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

.answer-modal-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.answer-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.answer-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.submit-answer-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

.cancel-answer-button {
  background-color: #7f8c8d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

.answer-success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.answer-error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.save-success-message,
.save-error-message {
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.save-success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.save-error-message {
  background-color: #ffebee;
  color: #c62828;
}

.success-icon,
.error-icon {
  margin-right: 8px;
  font-weight: bold;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

.pagination-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination-btn.active {
  background-color: #2196f3;
  color: white;
  border-color: #2196f3;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.edit-button:hover,
.delete-button:hover,
.close-button:hover,
.save-button:hover,
.cancel-button:hover,
.answer-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 로딩, 오류 및 빈 상태 스타일 */
.loading,
.error,
.empty-state {
  padding: 40px;
  text-align: center;
  border-radius: 8px;
  background-color: white;
  margin: 20px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #2196f3;
  margin: 0 auto 15px;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  color: #d32f2f;
}

.retry-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 10px;
  cursor: pointer;
}

/* 검색 영역 스타일 */
.filters {
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group label {
  margin-right: 5px;
  font-weight: 500;
  color: #555;
}

.search-type {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 15px;
  background-color: white;
}

.search-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

.answer-type-select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 120px;
  margin-right: 10px;
}

.search-btn,
.reset-btn,
.create-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.search-btn {
  background-color: #1976d2;
  color: white;
}

.reset-btn {
  background-color: #9e9e9e;
  color: white;
  margin-left: 5px;
}

.create-btn {
  background-color: #4caf50;
  color: white;
  padding: 10px 16px;
  margin-right: auto;
}

.search-btn:hover,
.reset-btn:hover,
.create-btn:hover {
  opacity: 0.9;
}

.items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.items-per-page-selector span {
  margin-right: 10px;
  font-weight: 500;
  color: #555;
}

.items-per-page-buttons {
  display: flex;
  gap: 5px;
}

.item-count-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.item-count-btn.active {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination-btn {
  padding: 8px 12px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 10px;
  font-weight: 500;
  color: #555;
}

.edit-textarea {
  width: 100%;
  min-height: 150px;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 10px;
}

.edit-textarea:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.25);
}

.save-success-message,
.save-error-message {
  margin: 15px 0;
  padding: 10px 15px;
  border-radius: 4px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.save-success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #43a047;
}

.save-error-message {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #e53935;
}

.success-icon,
.error-icon {
  font-style: normal;
  margin-right: 10px;
  font-weight: bold;
  font-size: 16px;
}

/* 애니메이션 효과 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
