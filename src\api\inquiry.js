import apiClient from './index';

// 문의 목록 조회 (새 API)
export const inquiryList = async (pageRequest) => {
  // 파라미터를 직접 전달 (개별 파라미터 개별 전송)
  return apiClient.get('/inquiries/list', {params: pageRequest});
};

// 문의 상세 조회
export const getInquiryById = async (id) => {
  return apiClient.get(`/inquiries/${id}`);
};

// 문의 삭제
export const deleteInquiry = async (id) => {
  return apiClient.delete(`/inquiries/${id}`);
};

// 문의 상태 변경
export const changeInquiryStatus = async (id, status) => {
  return apiClient.put(`/inquiries/${id}/status`, { newStatus: status });
};

// 답글 추가
export const addComment = async (inquiryId, commentContent, attachments = []) => {
  // FormData 객체 생성
  const formData = new FormData();
  
  // 답글 내용을 문자열 파라미터로 추가 (@RequestParam 방식)
  formData.append('commentContent', commentContent);
  
  // 첨부파일 추가 (attachments가 존재하는 경우)
  if (attachments && attachments.length > 0) {
    attachments.forEach(file => {
      formData.append('attachments', file);
    });
  }
  
  // API 요청
  return apiClient.post(`/inquiries/${inquiryId}/comments`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 파일 업로드
export const uploadFile = async (formData) => {
  const file = formData.get('file');
  if (!file) return Promise.resolve({ data: null });
  
  return Promise.resolve({ 
    data: { 
      id: `file-${Date.now()}`, 
      name: file.name, 
      url: `/mock/${file.name}`, 
      size: file.size, 
      type: file.type 
    } 
  });
};

// 문의사항 첨부파일 다운로드
export const downloadInquiryAttachment = async (attachmentId, originalFileName) => {
  
  try {
    // 다운로드 경로
    const downloadPath = `/download/inquiry-attachment/${attachmentId}`;
    
    // apiClient를 사용하여 blob 형태로 파일 다운로드
    const response = await apiClient.get(downloadPath, {
      responseType: 'blob' // 중요: 바이너리 데이터를 받기 위해 필요
    });
    
    // 다운로드 링크 생성 및 클릭
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // 다운로드 파일명 설정 - 원본 파일명 사용
    link.download = originalFileName || `attachment-${attachmentId}`;
    
    // 문서에 링크 추가, 클릭 후 삭제
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 링크 URL 메모리 해제
    window.URL.revokeObjectURL(url);
    
    return { success: true };
  } catch (error) {
    console.error('첨부파일 다운로드 오류:', error);
    throw error;
  }
};

// 댓글(답글) 첨부파일 다운로드
export const downloadCommentAttachment = async (attachmentId, originalFileName) => {
  
  try {
    // 다운로드 경로 (문의사항과 동일한 URL 사용)
    const downloadPath = `/download/inquiry-attachment/${attachmentId}`;
    
    // apiClient를 사용하여 blob 형태로 파일 다운로드
    const response = await apiClient.get(downloadPath, {
      responseType: 'blob' // 중요: 바이너리 데이터를 받기 위해 필요
    });
    
    // 다운로드 링크 생성 및 클릭
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // 다운로드 파일명 설정 - 원본 파일명 사용
    link.download = originalFileName || `attachment-${attachmentId}`;
    
    // 문서에 링크 추가, 클릭 후 삭제
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 링크 URL 메모리 해제
    window.URL.revokeObjectURL(url);
    
    return { success: true };
  } catch (error) {
    console.error('댓글 첨부파일 다운로드 오류:', error);
    throw error;
  }
};

// 첨부파일 삭제
export const deleteAttachment = async (attachmentId) => {
  
  try {
    // 삭제 API 호출
    const response = await apiClient.delete(`/remove/inquiry-attachment/${attachmentId}`);
    return response.data;
  } catch (error) {
    console.error('첨부파일 삭제 오류:', error);
    throw error;
  }
};

// 문의 저장 (multipart/form-data)
export const saveInquiry = async (formData) => {
  
  // inquiryId가 있으면 수정 경로로 PUT 요청, 없으면 생성 경로로 POST 요청
  const inquiryId = formData.get('inquiryId');
  
  if (inquiryId) {
    // 수정 요청 (PUT 메서드 사용)
    return apiClient.put(`/inquiries/${inquiryId}/modify`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  } else {
    // 새 문의 생성 요청 (POST 메서드 사용)
    return apiClient.post('/inquiries/save', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
};

// 답글 수정
export const updateComment = async (inquiryId, commentId, formData) => {
  return apiClient.put(`/inquiries/${inquiryId}/comments/${commentId}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 답글 삭제
export const deleteComment = async (inquiryId, commentId) => {
  return apiClient.delete(`/inquiries/${inquiryId}/comments/${commentId}`);
};
