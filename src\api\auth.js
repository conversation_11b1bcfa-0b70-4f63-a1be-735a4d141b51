import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';

/**
 * 사용자를 로그인합니다.
 * @param {string} userEmail - 사용자 이메일
 * @param {string} password - 사용자 비밀번호
 * @returns {Promise<object>} 로그인 성공 시 토큰 정보와 상태, 실패 시 에러 발생
 */
export const loginUser = async (userEmail, password) => {
  try {
    const response = await apiClient.post('/auth/login', {
      userEmail,
      password,
    });

    // 백엔드 응답 구조에 따라 success 필드 확인
    if (response.data && response.data.success) {
      // 성공 응답 처리
      return {
        ...response.data.data,
        userEmail: response.data.data?.userEmail || userEmail, // 응답에 userEmail이 없으면 요청 시 사용한 값 사용
        message: response.data.message, // 메시지가 있으면 함께 반환
        status: 'NORMAL' // 기본 상태는 정상
      };
    } else {
      // 성공하지 않은 경우 (success: false)

      // 비밀번호 변경 필요 에러 코드 확인
      if (response.data?.error?.code === 'PASSWORD_CHANGE_REQUIRED') {
        // 비밀번호 강제 변경이 필요한 특별한 경우
        return {
          status: 'FORCE_PASSWORD_CHANGE',
          userEmail: userEmail,
          message: response.data.error.message || '초기 로그인입니다. 비밀번호를 변경해야 합니다.'
        };
      }

      // 그 외 일반 에러
      throw new Error(response.data?.error?.message || '로그인에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '로그인 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * Refresh Token을 사용하여 새로운 Access Token을 발급받습니다.
 * HttpOnly 쿠키 방식에서는 브라우저가 자동으로 쿠키를 전송합니다.
 * @returns {Promise<object>} 성공 시 새 토큰 정보, 실패 시 에러 발생
 */
export const refreshToken = async () => {
  try {
    // 요청 본문 없이 POST 요청 (쿠키는 자동으로 전송됨)
    const response = await apiClient.post('/auth/refresh');

    if (response.data && response.data.success) {
      return response.data.data; // 새로운 accessToken 등 포함된 객체 반환
    } else {
      throw new Error(response.data?.error?.message || '토큰 갱신에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '토큰 갱신 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 서버에 로그아웃 요청을 보냅니다.
 * 백엔드는 요청 헤더의 Access Token을 보고 해당 사용자의 Refresh Token (쿠키)을 무효화합니다.
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const logoutUser = async () => {
  try {
    // 로그아웃 요청 시에도 Authorization 헤더 필요 (인터셉터에서 추가될 것임)
    const response = await apiClient.post('/auth/logout');

    if (response.data && response.data.success) {
      return true;
    } else {
      // 실패 메시지가 있다면 사용, 없으면 기본 메시지
      throw new Error(response.data?.message || response.data?.error?.message || '로그아웃 요청에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    // 로그아웃 실패 시에도 클라이언트에서는 로그아웃 처리를 하는 것이 일반적
    const errorMessage = handleApiError(error, '로그아웃 중 오류가 발생했습니다.');
    // 여기서는 에러를 throw하여 호출부에서 알 수 있도록 함
    throw new Error(errorMessage);
  }
};

/**
 * 현재 로그인된 사용자의 프로필 정보를 가져옵니다.
 * 백엔드는 요청에 포함된 쿠키(Refresh Token)를 검증하고,
 * 유효하다면 사용자 정보와 새 Access Token을 반환할 수 있습니다.
 * @returns {Promise<object>} 성공 시 { user, accessToken } 객체, 실패 시 에러 발생
 */
export const fetchUserProfile = async () => {
  try {
    // GET 요청 (쿠키는 자동으로 전송됨, 필요 시 Access Token도 인터셉터에서 추가됨)
    const response = await apiClient.get('/users/profile'); // <<< 엔드포인트는 실제 백엔드 경로로 수정 필요

    if (response.data && response.data.success && response.data.data) {
      // 성공 응답의 data 필드 (사용자 정보 객체)를 반환
      return response.data.data;
    } else {
      // 백엔드가 success: false 또는 data 필드 없이 응답한 경우
      throw new Error(response.data?.error?.message || '사용자 프로필 로딩에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    // 인터셉터에서 로그아웃 처리될 수 있으므로, 여기서는 에러를 다시 throw하여 호출 측에서 인지하도록 함
    const errorMessage = handleApiError(error, '사용자 프로필 로딩 중 오류 발생');
    throw new Error(errorMessage);
  }
};

/**
 * 초기 비밀번호를 변경합니다. (최초 로그인 시 강제 변경)
 * 이 API는 로그인 상태가 아니므로 Authorization 헤더 없이 호출됩니다.
 * @param {string} userEmail - 사용자 이메일
 * @param {string} currentPassword - 현재 비밀번호
 * @param {string} newPassword - 새 비밀번호
 * @returns {Promise<object>} 성공 시 응답 데이터, 실패 시 에러 발생
 */
export const changeInitialPassword = async (userEmail, currentPassword, newPassword) => {
  try {
    // 인터셉터에서 자동으로 Authorization 헤더를 추가하지 않도록 별도의 설정 필요
    const response = await apiClient.post('/auth/change-initial-password', {
      userEmail,
      currentPassword,
      newPassword
    }, {
      headers: {
        // Authorization 헤더를 명시적으로 제거 (인터셉터에서 추가하지 않도록)
        Authorization: ''
      }
    });

    // 성공 응답 처리 (200 OK, success: true)
    if (response.data && response.data.success) {
      // 서버에서 메시지를 제공하는 경우 사용, 아니면 기본 메시지 사용
      const message = response.data.message || '비밀번호가 성공적으로 변경되었습니다. 다시 로그인해주세요.';

      return {
        success: true,
        message: message
      };
    } else {
      // 서버가 success: false를 반환한 경우 (예상치 못한 상황)
      throw new Error(response.data?.error?.message || '초기 비밀번호 변경에 실패했습니다.');
    }
  } catch (error) {
    console.error('초기 비밀번호 변경 오류:', error);

    // HTTP 상태 코드에 따른 에러 처리
    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;

      // 401 인증 오류 (현재 비밀번호 불일치 등)
      if (status === 401) {
        // 인증 실패 에러 코드 확인
        if (errorData?.error?.code === 'AUTHENTICATION_FAILED') {
          // 서버에서 받은 에러 메시지를 그대로 사용하여 예외 발생
          throw new Error(errorData?.error?.message || '현재 비밀번호가 일치하지 않습니다.');
        } else {
          // 그 외 401 에러
          throw new Error(errorData?.error?.message || '인증에 실패했습니다.');
        }
      }

      // 400 유효성 검사 오류
      if (status === 400) {
        throw new Error(errorData?.error?.message || '입력값이 유효하지 않습니다.');
      }

      // 기타 에러
      throw new Error(errorData?.error?.message || '초기 비밀번호 변경 중 오류가 발생했습니다.');
    }

    // 네트워크 오류 등 기타 예외
    const errorMessage = handleApiError(error, '초기 비밀번호 변경 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};