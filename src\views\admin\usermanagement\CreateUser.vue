<template>
  <div class="create-user-container">
    <!-- 제목을 동적으로 변경 -->
    <h1>{{ pageTitle }}</h1>
    <form @submit.prevent="submitForm" class="user-form">
      <div class="form-group">
        <label for="userEmail">이메일 (아이디):</label>
        <!-- 수정 모드에서는 이메일 변경 불가 -->
        <input type="email" id="userEmail" v-model="formData.userEmail" required :disabled="isEditMode">
      </div>
      <div class="form-group">
        <label for="name">이름:</label>
        <input type="text" id="name" v-model="formData.name" required>
      </div>
      <div class="form-group">
        <label for="roleId">역할:</label>
        <select id="roleId" v-model="formData.roleId" required>
          <option value="" disabled>역할 선택</option>
          <option v-for="role in availableRoles" :key="role.value" :value="role.value">{{ role.label }}</option>
        </select>
        <small v-if="authStore.user?.roleId === 'PROJECT_ADMIN' && !isEditMode" class="role-info">
          프로젝트 관리자는 서브 관리자와 뷰어만 생성할 수 있습니다.
        </small>
        <small v-if="isEditMode && authStore.user?.roleId === 'PROJECT_ADMIN' && route.query.originalRole === 'PROJECT_ADMIN' && route.params.userEmail !== currentUserEmail" class="role-info warning">
          프로젝트 관리자는 다른 프로젝트 관리자의 역할을 변경할 수 없습니다.
        </small>
        <small v-if="isEditMode && route.params.userEmail === currentUserEmail && formData.roleId !== route.query.originalRole" class="role-info warning">
          자신의 역할을 변경하면 현재 페이지에 대한 접근 권한이 변경될 수 있습니다.
        </small>
      </div>
      <div class="form-group">
        <label for="description">설명 (선택 사항):</label>
        <textarea id="description" v-model="formData.description"></textarea>
      </div>
      <div class="form-group">
        <label for="status">상태:</label>
        <select id="status" v-model="formData.status">
          <option value="ACTIVE">활성</option>
          <option value="INACTIVE">비활성</option>
          <option value="LOCKED">잠금</option>
        </select>
      </div>
      <!-- 비밀번호 필드는 수정 모드에서만 표시 -->
      <div class="form-group password-section" v-if="isEditMode">
        <h3>비밀번호 변경</h3>

        <div class="form-group">
          <label for="newPassword">새 비밀번호:</label>
          <input type="password" id="newPassword" v-model="passwordData.newPassword" placeholder="새 비밀번호 입력">
        </div>

        <div class="form-group">
          <label for="confirmPassword">비밀번호 확인:</label>
          <input type="password" id="confirmPassword" v-model="passwordData.confirmPassword" placeholder="비밀번호 확인">
        </div>

        <div class="password-action">
          <button type="button" @click="handlePasswordChange" class="password-change-btn"
            :disabled="!passwordData.newPassword || !passwordData.confirmPassword || passwordData.newPassword !== passwordData.confirmPassword || isPasswordChanging">
            {{ isPasswordChanging ? '변경 중...' : '비밀번호 변경' }}
          </button>
        </div>

        <small>비밀번호를 변경하려면 새 비밀번호와 확인용 비밀번호를 입력하고 변경 버튼을 클릭하세요.</small>

        <div v-if="passwordData.newPassword && passwordData.confirmPassword && passwordData.newPassword !== passwordData.confirmPassword" class="password-message">
          비밀번호가 일치하지 않습니다.
        </div>

        <div v-if="passwordChangeMessage" class="password-message">
          {{ passwordChangeMessage }}
        </div>
      </div>

      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div class="form-actions">
        <!-- 버튼 텍스트를 동적으로 변경 -->
        <button type="submit" :disabled="isLoading">{{ isLoading ? (isEditMode ? '수정 중...' : '생성 중...') : submitButtonText }}</button>
        <button type="button" @click="goToList" :disabled="isLoading">취소</button>
      </div>
    </form>
  </div>
</template>

<script setup>
// computed, onMounted, useRoute 추가
import { ref, computed, onMounted } from 'vue';
// useRoute 임포트
import { useRouter, useRoute } from 'vue-router';
// getUserByEmail, updateUser, changePassword API 함수 임포트
import { createUser, getUserByEmail, updateUser, changePassword } from '@/api/user';
// 인증 스토어 임포트
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
// route 객체 가져오기
const route = useRoute();
// 인증 스토어 가져오기
const authStore = useAuthStore();

// 관리자 정보 폼 데이터
const formData = ref({
  userIdx: null, // 수정 시 필요한 경우 사용
  userEmail: '',
  name: '',
  roleId: '', // 필수
  description: '', // 선택
  status: 'ACTIVE' // 기본값 ACTIVE
});

// 비밀번호 관련 데이터
const passwordData = ref({
  newPassword: '',
  confirmPassword: ''
});

const isLoading = ref(false);
const isPasswordChanging = ref(false); // 비밀번호 변경 중 상태
const errorMessage = ref('');
const passwordChangeMessage = ref(''); // 비밀번호 변경 관련 메시지

// 수정 모드 여부 확인 (라우트 파라미터 userEmail 유무)
const isEditMode = computed(() => !!route.params.userEmail);

// 페이지 제목 계산
const pageTitle = computed(() => isEditMode.value ? '관리자 수정' : '새 관리자 생성');

// 제출 버튼 텍스트 계산
const submitButtonText = computed(() => isEditMode.value ? '수정하기' : '관리자 생성');

// 현재 로그인한 관리자의 이메일
// 자기 자신인지 확인하기 위해 사용
// UserManagement.vue와 동일한 방식으로 구현
// 자기 자신의 정보를 수정할 때는 역할 변경 제한을 적용하지 않음
const currentUserEmail = computed(() => authStore.user?.userEmail || '');

// 현재 로그인한 관리자의 권한에 따라 선택 가능한 역할 옵션 계산
const availableRoles = computed(() => {
  const userRole = authStore.user?.roleId;

  // 자기 자신의 정보를 수정하는 경우에는 역할 변경 제한을 적용하지 않음
  if (isEditMode.value && route.params.userEmail === currentUserEmail.value) {
    // 자기 자신의 정보를 수정하는 경우에는 역할 변경 가능
    // 단, 자신의 역할을 변경하면 권한이 변경되어 문제가 발생할 수 있으므로 주의 메시지 표시
    return [
      { value: 'PROJECT_ADMIN', label: '프로젝트 관리자' },
      { value: 'SUB_ADMIN', label: '서브 관리자' },
      { value: 'VIEWER', label: '뷰어' }
    ];
  }

  // 수정 모드에서 프로젝트 관리자가 다른 프로젝트 관리자를 수정하는 경우
  // 역할 변경을 방지하기 위해 현재 역할만 표시
  if (isEditMode.value && userRole === 'PROJECT_ADMIN' && route.query.originalRole === 'PROJECT_ADMIN' && route.params.userEmail !== currentUserEmail.value) {
    return [
      { value: 'PROJECT_ADMIN', label: '프로젝트 관리자' }
    ];
  }

  // 슈퍼 관리자는 모든 역할 생성 가능 (SUPER_ADMIN 제외)
  if (userRole === 'SUPER_ADMIN') {
    return [
      { value: 'PROJECT_ADMIN', label: '프로젝트 관리자' },
      { value: 'SUB_ADMIN', label: '서브 관리자' },
      { value: 'VIEWER', label: '뷰어' }
    ];
  }

  // 프로젝트 관리자는 하위 역할만 생성 가능
  if (userRole === 'PROJECT_ADMIN') {
    return [
      { value: 'SUB_ADMIN', label: '서브 관리자' },
      { value: 'VIEWER', label: '뷰어' }
    ];
  }

  // 기본적으로 빈 배열 반환 (권한 없음)
  return [];
});

// 컴포넌트 마운트 시 관리자 정보 로드 (수정 모드일 경우)
onMounted(async () => {
  if (isEditMode.value) {
    const userEmail = route.params.userEmail; // 파라미터 이름 변경
    isLoading.value = true;
    errorMessage.value = '';
    try {
      // getUserByEmail API 호출
      const userData = await getUserByEmail(userEmail);
      // API 응답에서 비밀번호는 제외되므로, 비밀번호 필드는 비워둡니다.
      // userIdx는 업데이트 시 필요할 수 있으므로 저장합니다. (updateUser API가 userIdx를 요구한다면)
      formData.value = {
        ...userData,
        userIdx: userData.userIdx, // 응답에 userIdx가 있다고 가정하고 저장
        password: '' // 비밀번호 필드는 비움 (수정 시 입력하면 업데이트)
      };

      // 원래 역할을 쿼리 파라미터로 저장 (수정 시 권한 검증에 사용)
      router.replace({
        name: route.name,
        params: route.params,
        query: { ...route.query, originalRole: userData.roleId }
      });

      // 현재 관리자의 권한에 따라 수정 가능 여부 확인
      const currentUserRole = authStore.user?.roleId;

      // 현재 로그인한 관리자의 이메일을 가져와서 자기 자신인지 확인
      const loggedInUserEmail = authStore.user?.userEmail || '';

      if (currentUserRole === 'PROJECT_ADMIN' && userData.roleId === 'PROJECT_ADMIN') {
        setTimeout(() => {
          alert('프로젝트 관리자는 다른 프로젝트 관리자의 역할을 변경할 수 없습니다. \n다른 정보만 수정 가능합니다.');
        }, 500);
      }
    } catch (error) {
      errorMessage.value = `관리자 정보 로딩 실패: ${error.message}`;
      console.error('Load user error:', error);
      // 에러 발생 시 목록으로 돌려보내는 등의 처리 추가 가능
    } finally {
      isLoading.value = false;
    }
  }
});


const submitForm = async () => {
  isLoading.value = true;
  errorMessage.value = '';

  // 자기 자신의 역할을 변경하려는 경우 추가 확인
  if (isEditMode.value &&
      formData.value.userEmail === currentUserEmail.value &&
      formData.value.roleId !== route.query.originalRole) {
    const confirmChange = confirm('자신의 역할을 변경하면 현재 페이지에 대한 접근 권한이 변경될 수 있습니다. \n계속 진행하시겠습니까?');
    if (!confirmChange) {
      isLoading.value = false;
      return;
    }
  }

  try {
    // 현재 관리자 권한 확인
    const currentUserRole = authStore.user?.roleId;
    const loggedInUserEmail = authStore.user?.userEmail || '';

    // 권한 검증
    if (currentUserRole === 'PROJECT_ADMIN') {
      // 생성 모드에서 프로젝트 관리자가 프로젝트 관리자를 생성하려는 경우 차단
      if (!isEditMode.value && formData.value.roleId === 'PROJECT_ADMIN') {
        errorMessage.value = '프로젝트 관리자는 다른 프로젝트 관리자를 생성할 수 없습니다.';
        isLoading.value = false;
        return;
      }

      // 수정 모드에서 프로젝트 관리자가 다른 관리자의 역할을 프로젝트 관리자로 변경하려는 경우 차단
      // 단, 자기 자신의 정보를 수정하는 경우는 제외
      if (isEditMode.value && formData.value.roleId === 'PROJECT_ADMIN' &&
          formData.value.roleId !== route.query.originalRole &&
          formData.value.userEmail !== loggedInUserEmail) {
        errorMessage.value = '프로젝트 관리자는 다른 관리자의 역할을 프로젝트 관리자로 변경할 수 없습니다.';
        isLoading.value = false;
        return;
      }
    }

    if (isEditMode.value) {
      // 수정 모드: updateUser 호출
      // 서버 요청 형식에 맞게 데이터 구성
      const updatePayload = {
        userEmail: formData.value.userEmail,
        name: formData.value.name,
        roleId: formData.value.roleId,
        description: formData.value.description || '',
        status: formData.value.status || 'ACTIVE' // 기본값 ACTIVE
      };

      // 프로젝트 ID 추가
      if (authStore.user?.roleId === 'SUPER_ADMIN') {
        // SUPER_ADMIN인 경우
        if (formData.value.projectId) {
          // 폼에서 직접 선택한 프로젝트 ID가 있으면 사용
          updatePayload.projectId = formData.value.projectId;
        } else if (authStore.currentProject && authStore.currentProject.projectId) {
          // 현재 선택된 프로젝트가 있고 전체 프로젝트 모드가 아니면 해당 프로젝트 ID 사용
          updatePayload.projectId = authStore.currentProject.projectId;
        }
      } else if (authStore.user?.roleId === 'PROJECT_ADMIN') {
        // PROJECT_ADMIN인 경우 현재 프로젝트 ID 사용
        if (authStore.currentProject && authStore.currentProject.projectId) {
          updatePayload.projectId = authStore.currentProject.projectId;
        }
      }

      // 비밀번호 변경은 별도의 버튼으로 처리하므로 여기서는 제외

      // 관리자 정보 업데이트 API 호출
      await updateUser(updatePayload);
      alert('관리자 정보가 성공적으로 수정되었습니다.');
    } else {
      // 생성 모드: createUser 호출
      // 생성 시 비밀번호 필드를 빈 값으로 포함
      const createPayload = {
        userEmail: formData.value.userEmail,
        name: formData.value.name,
        roleId: formData.value.roleId,
        description: formData.value.description || '',
        status: formData.value.status || 'ACTIVE', // 기본값 ACTIVE
        password: '' // 비밀번호 필드를 빈 값으로 추가
      };

      // status 값이 설정되어 있는지 확인
      if (!createPayload.status) {
        createPayload.status = 'ACTIVE'; // 기본값으로 ACTIVE 설정
      }

      if (authStore.user?.roleId === 'SUPER_ADMIN') {
        // SUPER_ADMIN인 경우
        if (formData.value.projectId) {
          // 폼에서 직접 선택한 프로젝트 ID가 있으면 사용
          createPayload.projectId = formData.value.projectId;
        } else if (authStore.currentProject && authStore.currentProject.projectId) {
          // 현재 선택된 프로젝트가 있고 전체 프로젝트 모드가 아니면 해당 프로젝트 ID 사용
          createPayload.projectId = authStore.currentProject.projectId;
        }
      } else if (authStore.user?.roleId === 'PROJECT_ADMIN') {
        // PROJECT_ADMIN인 경우 현재 프로젝트 ID 사용
        if (authStore.currentProject && authStore.currentProject.projectId) {
          createPayload.projectId = authStore.currentProject.projectId;
        }
      }

      await createUser(createPayload);
      alert('새 관리자가 성공적으로 생성되었습니다.');
    }

    // 성공 시 목록 페이지로 이동
    router.push({ name: 'admin-user-management' });
  } catch (error) {
    // 에러 메시지를 좀 더 구체적으로 표시
    errorMessage.value = `${isEditMode.value ? '수정' : '생성'} 실패: ${error.message}`;
    console.error(`User ${isEditMode.value ? 'update' : 'create'} error:`, error);
  } finally {
    isLoading.value = false;
  }
};

// 비밀번호 변경 처리 함수
const handlePasswordChange = async () => {
  // 비밀번호 입력 확인
  if (!passwordData.value.newPassword) {
    passwordChangeMessage.value = '새 비밀번호를 입력해주세요.';
    return;
  }

  if (!passwordData.value.confirmPassword) {
    passwordChangeMessage.value = '비밀번호 확인을 입력해주세요.';
    return;
  }

  // 비밀번호 일치 확인
  if (passwordData.value.newPassword !== passwordData.value.confirmPassword) {
    passwordChangeMessage.value = '비밀번호가 일치하지 않습니다.';
    return;
  }

  isPasswordChanging.value = true;
  passwordChangeMessage.value = '';

  try {
    // 비밀번호 변경 API 호출
    await changePassword(formData.value.userEmail, passwordData.value.newPassword);

    // 성공 메시지 표시
    alert('비밀번호가 성공적으로 변경되었습니다.');

    // 비밀번호 필드 초기화
    passwordData.value.newPassword = '';
    passwordData.value.confirmPassword = '';
  } catch (error) {
    // 에러 메시지 표시
    alert(`비밀번호 변경 실패: ${error.message}`);
    console.error('Password change error:', error);
  } finally {
    isPasswordChanging.value = false;
  }
};

const goToList = () => {
  router.push({ name: 'admin-user-management' });
};
</script>

<style scoped>
.create-user-container {
  max-width: 600px;
  margin: 30px auto;
  padding: 25px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.user-form .form-group {
  margin-bottom: 20px;
}

.user-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

.user-form input[type="text"],
.user-form input[type="email"],
.user-form select,
.user-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1em;
}

.user-form textarea {
  min-height: 80px;
  resize: vertical;
}

.user-form small {
  display: block;
  margin-top: 5px;
  font-size: 0.85em;
  color: #777;
}

.error-message {
  color: #e74c3c;
  background-color: #fdd;
  border: 1px solid #e74c3c;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  text-align: center;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.form-actions button {
  padding: 12px 25px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.form-actions button[type="submit"] {
  background-color: #3498db;
  color: white;
}

.form-actions button[type="submit"]:hover {
  background-color: #2980b9;
}

.form-actions button[type="submit"]:disabled {
  background-color: #a9d6f5;
  cursor: not-allowed;
}

.form-actions button[type="button"] {
  background-color: #e0e0e0;
  color: #333;
}

.form-actions button[type="button"]:hover {
  background-color: #bdbdbd;
}

.form-actions button[type="button"]:disabled {
  background-color: #f0f0f0;
  color: #aaa;
  cursor: not-allowed;
}

/* 비밀번호 변경 관련 스타일 */
.password-section {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.password-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2em;
}

.password-action {
  margin-top: 15px;
  margin-bottom: 10px;
}

.password-change-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.password-change-btn:hover {
  background-color: #218838;
}

.password-change-btn:disabled {
  background-color: #a3d7b0;
  cursor: not-allowed;
}

.password-message {
  color: #dc3545;
  font-size: 0.85em;
  margin-top: 5px;
}

.role-info {
  color: #0066cc;
  font-style: italic;
  margin-top: 5px;
}

.role-info.warning {
  color: #cc6600;
  font-weight: bold;
}
</style>
