<template>
  <div class="inquiry-detail-view">
    <!-- 로딩 표시 -->
    <div v-if="isLoading" class="text-center my-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">로딩 중...</span>
      </div>
    </div>

    <!-- 오류 메시지 -->
    <div v-else-if="error" class="alert alert-danger my-3">
      {{ error }}
    </div>

    <!-- 문의가 없는 경우 -->
    <div v-else-if="!inquiry" class="alert alert-warning my-3">
      해당 문의를 찾을 수 없습니다.
    </div>

    <!-- 문의 상세 정보 -->
    <div v-else class="inquiry-detail">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ inquiry.title }}</h1>
        <div>
          <span
            class="badge"
            :class="{
              'bg-warning': inquiry.inquiryStatus === 'PENDING',
              'bg-primary': inquiry.inquiryStatus === 'PROCESSING',
              'bg-success': inquiry.inquiryStatus === 'COMPLETED'
            }"
          >
            {{ getStatusDisplayName(inquiry.inquiryStatus) }}
          </span>
          <span class="badge bg-secondary ms-2">{{ getInquiryTypeDisplayName(inquiry.type) }}</span>
        </div>
      </div>

      <div class="mb-4 inquiry-info">
        <div class="row">
          <div class="col-md-6">
            <p><strong>작성자:</strong> {{ inquiry.author }}</p>
            <p><strong>프로젝트:</strong> {{ inquiry.projectName || '전체 프로젝트' }}</p>
          </div>
          <div class="col-md-6 text-md-end">
            <p><strong>작성일:</strong> {{ formatDate(inquiry.createdAt) }}</p>
            <p v-if="inquiry.updatedAt && inquiry.updatedAt !== inquiry.createdAt">
              <strong>수정일:</strong> {{ formatDate(inquiry.updatedAt) }}
            </p>
          </div>
        </div>
      </div>
      <h3>문의내용</h3>
      <!-- 문의 내용 -->
      <div class="card mb-4">
        <div class="card-body">
          
          <div class="inquiry-content">
            {{ inquiry.content }}
          </div>
        </div>
      </div>

      <!-- 첨부파일 목록 -->
      <div v-if="inquiry.attachments && inquiry.attachments.length > 0" class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">첨부파일</h5>
        </div>
        <div class="card-body">
          <div class="list-group">
            <div
              v-for="attachment in inquiry.attachments"
              :key="attachment.attachmentId"
              class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
            >
              <a 
                href="#" 
                @click.prevent="downloadAttachment(attachment.attachmentId)" 
                class="text-decoration-none text-dark flex-grow-1"
              >
                <i class="bi bi-file-earmark me-2"></i>
                {{ attachment.originalFileName }}
                <span class="text-muted ms-2">({{ formatFileSize(attachment.fileSize) }})</span>
              </a>
              <button 
                class="btn btn-sm btn-outline-danger ms-2" 
                title="첨부파일 삭제"
                @click="confirmDeleteAttachment(attachment)"
              >
                X
              </button>
            </div>
          </div>
        </div>
      </div>

      

      <!-- 답글 섹션 -->
      <div class="comments-section">
        <h3 class="mb-3">답글 ({{ (inquiry.comments || []).length }}개)</h3>
        
        <!-- 답글 목록 -->
        <div v-if="inquiry.comments && inquiry.comments.length > 0" class="comments-list mb-4">
          <div
            v-for="comment in inquiry.comments"
            :key="comment.commentId || comment.id"
            class="card mb-3"
          >
            <div class="card-header d-flex justify-content-between align-items-center">
              <span><strong>{{ comment.userEmail || comment.author }}</strong></span>
              <div>
                <small>{{ formatDate(comment.createdAt) }}</small>
                <!-- 수정 버튼: 자신이 작성한 댓글만 수정 가능 -->
                <button 
                  v-if="canEditComment(comment)" 
                  @click="startEditingComment(comment)" 
                  class="btn btn-sm btn-outline-secondary ms-2"
                  v-show="!isEditingComment || editingCommentId !== (comment.commentId || comment.id)"
                >
                  수정
                </button>
                <!-- 삭제 버튼: 자신이 작성한 댓글만 삭제 가능 -->
                <button 
                  v-if="canEditComment(comment)" 
                  @click="confirmDeleteComment(comment)" 
                  class="btn btn-sm btn-outline-danger ms-2"
                  v-show="!isEditingComment || editingCommentId !== (comment.commentId || comment.id)"
                >
                  삭제
                </button>
              </div>
            </div>
            <!-- 일반 모드: 댓글 표시 -->
            <div class="card-body" v-if="!isEditingComment || editingCommentId !== (comment.commentId || comment.id)">
              <p class="card-text">{{ comment.commentContent || comment.content }}</p>
              
              <!-- 첨부파일 표시 -->
              <div v-if="comment.attachments && comment.attachments.length > 0" class="mt-2">
                <p class="mb-1 fw-bold">첨부파일:</p>
                <div class="attachment-list">
                  <div v-for="attachment in comment.attachments" :key="attachment.attachmentId" class="attachment-item d-flex justify-content-between align-items-center mb-1">
                    <a href="#" @click.prevent="downloadCommentAttachment(attachment.attachmentId, attachment.originalFileName || attachment.name)">
                      {{ attachment.originalFileName || attachment.name }}
                    </a>
                    <button 
                      class="btn btn-sm btn-outline-danger ms-2 py-0" 
                      title="첨부파일 삭제"
                      @click="confirmDeleteAttachment(attachment)"
                    >
                      X
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 수정 모드: 수정 폼 표시 -->
            <div class="card-body" v-else>
              <div class="form-group mb-3">
                <textarea
                  v-model="editingCommentText"
                  class="form-control"
                  rows="3"
                  placeholder="답글을 입력하세요..."
                ></textarea>
              </div>
              
              <!-- 기존 첨부파일 목록 -->
              <div v-if="editingCommentAttachments.length > 0" class="mb-3">
                <p class="text-muted mb-2">기존 첨부파일:</p>
                <ul class="list-group">
                  <li v-for="attachment in editingCommentAttachments" :key="attachment.attachmentId" class="list-group-item d-flex justify-content-between align-items-center">
                    <span>{{ attachment.originalFileName || attachment.name }}</span>
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" v-model="attachment.toDelete" :id="'attachment-' + attachment.attachmentId">
                      <label class="form-check-label" :for="'attachment-' + attachment.attachmentId">삭제</label>
                    </div>
                  </li>
                </ul>
              </div>
              
              <!-- 새 첨부파일 추가 -->
              <div class="form-group mb-3">
                <label for="editFileAttachments" class="form-label">새 첨부파일 추가 (선택사항)</label>
                <input
                  type="file"
                  id="editFileAttachments"
                  class="form-control"
                  multiple
                  @change="handleEditFileChange"
                />
              </div>
              
              <!-- 새 첨부파일 목록 표시 -->
              <div v-if="editingCommentNewFiles.length > 0" class="mb-3">
                <p class="text-muted mb-2">새 첨부파일:</p>
                <ul class="list-group">
                  <li v-for="(file, index) in editingCommentNewFiles" :key="index" class="list-group-item d-flex justify-content-between align-items-center">
                    <span>{{ file.name }} ({{ formatFileSize(file.size) }})</span>
                    <button type="button" class="btn btn-sm btn-outline-danger" @click="removeEditFile(index)">
                      삭제
                    </button>
                  </li>
                </ul>
              </div>
              
              <div class="d-flex">
                <button
                  @click="updateComment"
                  class="btn btn-primary me-2"
                  :disabled="isSubmittingComment"
                >
                  <span v-if="isSubmittingComment" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  수정 완료
                </button>
                <button
                  @click="cancelEditingComment"
                  class="btn btn-outline-secondary"
                  :disabled="isSubmittingComment"
                >
                  취소
                </button>
              </div>
              
              <!-- 오류 메시지 표시 -->
              <div v-if="commentError" class="alert alert-danger mt-3">
                {{ commentError }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 답글이 없는 경우 -->
        <div v-else class="alert alert-light mb-4">
          아직 답글이 없습니다.
        </div>
        
        <!-- 답글 작성 폼 (권한이 있는 경우만 표시) -->
        <div v-if="canComment" class="comment-form">
          <h4>답글 작성</h4>
          <div class="form-group mb-3">
            <textarea
              v-model="newComment"
              class="form-control"
              rows="3"
              placeholder="답글을 입력하세요..."
            ></textarea>
          </div>
          
          <!-- 파일 첨부 기능 -->
          <div class="form-group mb-3">
            <label for="fileAttachments" class="form-label">첨부파일 (선택사항)</label>
            <input
              type="file"
              id="fileAttachments"
              class="form-control"
              multiple
              @change="handleFileChange"
            />
          </div>
          
          <!-- 첨부파일 목록 표시 -->
          <div v-if="selectedFiles.length > 0" class="selected-files mb-3">
            <p class="text-muted mb-2">선택된 파일:</p>
            <ul class="list-group">
              <li v-for="(file, index) in selectedFiles" :key="index" class="list-group-item d-flex justify-content-between align-items-center">
                <span>{{ file.name }} ({{ formatFileSize(file.size) }})</span>
                <button type="button" class="btn btn-sm btn-outline-danger" @click="removeFile(index)">
                  삭제
                </button>
              </li>
            </ul>
          </div>
          
          <div class="d-flex justify-content-between">
            <button
              @click="addComment"
              class="btn btn-primary"
              :disabled="isSubmittingComment"
            >
              <span v-if="isSubmittingComment" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              답글 등록
            </button>
          </div>
          
          <!-- 오류 메시지 표시 -->
          <div v-if="commentError" class="alert alert-danger mt-3">
              {{ commentError }}
            </div>
          </div>

          <!-- 버튼 그룹 -->
          <div class="action-buttons mb-4">
            <button
              v-if="canChangeStatus"
              class="btn btn-outline-primary me-2"
              type="button"
              @click="openStatusChangeModal"
            >
              상태 변경
            </button>
            <router-link
              v-if="canEdit"
              :to="`/inquiries/${inquiryId}/edit`"
              class="btn btn-outline-secondary me-2"
            >
              수정하기
            </router-link>
            <button
              v-if="canDelete"
              class="btn btn-outline-danger me-2"
              @click="showDeleteModal = true"
            >
              삭제하기
            </button>
            <router-link to="/inquiries" class="btn btn-outline-dark">
              목록으로
            </router-link>
          </div>
      </div>
    </div>

    <!-- 상태 변경 모달 (Vue스타일) -->
    <div v-if="showStatusChangeModal" class="modal-overlay">
      <div class="modal-wrapper">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">문의 상태 변경</h5>
            <button 
              type="button" 
              class="close-button" 
              @click="showStatusChangeModal = false"
            >
              &times;
            </button>
          </div>
          <div class="modal-body">
            <div class="form-group mb-3">
              <label for="statusSelect" class="form-label mb-2">새 상태 선택:</label>
              <select 
                id="statusSelect" 
                v-model="newStatus" 
                class="custom-select"
              >
                <option value="PENDING">접수됨</option>
                <option value="PROCESSING">처리중</option>
                <option value="COMPLETED">완료</option>
              </select>
              <div class="status-description text-muted mt-2" v-if="newStatus">
                <div>{{ getStatusDescription(newStatus) }}</div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showStatusChangeModal = false"
            >
              취소
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="changeInquiryStatus"
            >
              변경하기
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 삭제 확인 모달 (Vue스타일) -->
    <div v-if="showDeleteModal" class="modal-overlay">
      <div class="modal-wrapper">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">문의 삭제 확인</h5>
            <button
              type="button"
              class="close-button"
              @click="showDeleteModal = false"
            >
              &times;
            </button>
          </div>
          <div class="modal-body">
            정말로 이 문의를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showDeleteModal = false"
            >
              취소
            </button>
            <button
              type="button"
              class="btn btn-danger"
              @click="deleteInquiry"
            >
              삭제하기
            </button>
          </div>
        </div>
      </div>
    </div>
    

  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getInquiryById, changeInquiryStatus as inquiryApiChangeStatus, deleteInquiry as inquiryApiDeleteInquiry, addComment as inquiryApiAddComment, updateComment as inquiryApiUpdateComment, deleteComment as inquiryApiDeleteComment, downloadInquiryAttachment as inquiryApiDownloadAttachment, downloadCommentAttachment as inquiryApiDownloadCommentAttachment, deleteAttachment as inquiryApiDeleteAttachment } from '@/api/inquiry';
import { useAuthStore } from '@/stores/auth';

export default {
  name: 'InquiryDetailView',
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    const inquiryId = computed(() => route.params.id);
    const inquiry = ref(null);
    const isLoading = ref(false);
    const error = ref(null);
    const newComment = ref('');
    const newStatus = ref('');
    
    // 답글 관련 변수
    const selectedFiles = ref([]);         // 선택된 첨부파일 목록
    const isSubmittingComment = ref(false); // 답글 제출 중 상태 표시
    const commentError = ref(null);        // 답글 오류 저장
    
    // 답글 수정 관련 변수
    const isEditingComment = ref(false);   // 댓글 수정 모드 여부
    const editingCommentId = ref(null);    // 현재 수정 중인 댓글 ID
    const editingCommentText = ref('');    // 수정 중인 댓글 내용
    const editingCommentAttachments = ref([]);  // 수정 중인 댓글의 기존 첨부파일
    const editingCommentNewFiles = ref([]);     // 수정 시 추가할 새 파일
    
    // 모달 상태 관리 변수
    const showStatusChangeModal = ref(false);
    const showDeleteModal = ref(false);
  
    // 인증 스토어 가져오기
    const authStore = useAuthStore();
    
    // 권한 체크 구현
    const canEdit = computed(() => {
      // 로그인한 사용자와 문의 작성자 비교
      if (!authStore.user || !inquiry.value) return false;
      
      // 오직 작성자와 로그인한 사용자의 이메일이 일치하는 경우에만 수정 가능 (SUPER_ADMIN 제외)
      return authStore.user.userEmail === inquiry.value.author;
    });
    
    const canDelete = computed(() => {
      // SUPER_ADMIN은 삭제 가능
      if (!authStore.user) return false;
      
      if (authStore.user.roleId === 'SUPER_ADMIN') return true;
      
      // 작성자는 자신의 문의만 삭제 가능
      return authStore.user.userEmail === inquiry.value?.author;
    });
    
    const canChangeStatus = computed(() => {
      // SUPER_ADMIN만 상태 변경 가능
      if (!authStore.user) return false;
      return authStore.user.roleId === 'SUPER_ADMIN';
    });
    
    const canComment = computed(() => {
      // 로그인한 사용자만 답글 작성 가능
      return !!authStore.user;
    });
    
    // 댓글 수정 권한 확인 함수
    const canEditComment = (comment) => {
      if (!authStore.user) return false;
      
      // 자신이 작성한 댓글만 수정 가능
      const commentAuthor = comment.userEmail || comment.author;
      return authStore.user.userEmail === commentAuthor;
    };
    
    // 라우트 파라미터 변경 감지
    watch(() => route.params.id, (newId, oldId) => {
      if (newId !== oldId) {
        loadInquiry();
      }
    });
    
    // 문의 상세 조회
    const loadInquiry = async () => {
      isLoading.value = true;
      error.value = null;
      
      try {
        // 실제 API 호출
        const response = await getInquiryById(inquiryId.value);
        
        if (response && response.data && response.data.data) {
          // 서버 응답 구조에 맞게 데이터 매핑
          const inquiryData = response.data.data;
          
          // 문의 데이터 갱신
          inquiry.value = {
            id: inquiryData.inquiryId,
            title: inquiryData.inquiryTitle,
            content: inquiryData.inquiryContent,
            inquiryStatus: inquiryData.inquiryStatus,
            type: inquiryData.inquiryType,
            author: inquiryData.userEmail,
            projectId: inquiryData.projectId,
            projectName: inquiryData.projectName,
            createdAt: inquiryData.createdAt,
            updatedAt: inquiryData.updatedAt,
            attachments: inquiryData.attachments ? inquiryData.attachments.map(attachment => ({
              attachmentId: attachment.attachmentId,
              originalFileName: attachment.originalFileName,
              storedFilePath: attachment.storedFilePath,
              fileSize: attachment.fileSize,
              mimeType: attachment.mimeType
            })) : [],
            comments: inquiryData.comments ? inquiryData.comments.map(comment => ({
              id: comment.commentId,
              commentId: comment.commentId,  // 두 가지 방식의 속성명 모두 지원
              content: comment.commentContent,
              commentContent: comment.commentContent,  // 두 가지 방식의 속성명 모두 지원
              author: comment.userEmail,
              userEmail: comment.userEmail,  // 두 가지 방식의 속성명 모두 지원
              createdAt: comment.createdAt,
              attachments: comment.attachments || []  // 첨부파일 정보 추가
            })) : []
          };
          
          // 상태 값 초기화
          newStatus.value = inquiryData.inquiryStatus;
        } else {
          error.value = '서버에서 문의 데이터를 가져오는 데 실패했습니다.';
        }
      } catch (err) {
        error.value = '문의 정보를 불러오는데 실패했습니다.';
        console.error('문의 상세 조회 오류:', err);
      } finally {
        isLoading.value = false;
      }
    };
    
    // 상태값 한글 표시를 위한 매핑 함수
    const getStatusDisplayName = (status) => {
      const statusDisplayMap = {
        'PENDING': '접수됨',
        'PROCESSING': '처리중',
        'COMPLETED': '완료'
      };
      return statusDisplayMap[status] || status;
    };
    
    // 상태값 메시지 가져오기
    const getStatusDescription = (status) => {
      const statusDescriptionMap = {
        'PENDING': '문의가 접수되었지만 아직 처리되지 않았습니다.',
        'PROCESSING': '문의가 현재 처리 중입니다.',
        'COMPLETED': '문의 처리가 완료되었습니다.'
      };
      return statusDescriptionMap[status] || '';
    };
    
    // 상태변경 모달 열기
    const openStatusChangeModal = () => {
      // 현재 문의 상태에 따라 기본값 설정 - 서버에서 받은 값 그대로 사용
      if (inquiry.value && inquiry.value.inquiryStatus) {
        newStatus.value = inquiry.value.inquiryStatus;
      } else {
        newStatus.value = 'PENDING';
      }
      
      // 모달 표시
      showStatusChangeModal.value = true;
    };
    
    // 상태 변경
    const changeInquiryStatus = async () => {
      if (newStatus.value && newStatus.value !== inquiry.value.inquiryStatus) {
        try {
          // API 호출로 상태 변경 요청
          const response = await inquiryApiChangeStatus(inquiryId.value, newStatus.value);
          
          // 서버 응답의 success 필드 확인
          if (response.data && response.data.success === false) {
            // 서버에서 오류를 반환한 경우
            const errorMessage = response.data.error?.message || '알 수 없는 오류가 발생했습니다.';
            console.error('서버 오류:', errorMessage);
            alert('오류: ' + errorMessage);
            return;
          }
          
          // 서버에서 반환한 데이터로 문의 정보 업데이트
          if (response.data && response.data.data) {
            const updatedInquiry = response.data.data;
            
            // 로컬 데이터 업데이트
            inquiry.value.inquiryStatus = updatedInquiry.inquiryStatus;
            inquiry.value.updatedAt = updatedInquiry.updatedAt;
            
            alert('문의 상태가 변경되었습니다.');
          } else {
            // 응답에 데이터가 없는 경우 화면만 업데이트
            inquiry.value.inquiryStatus = newStatus.value;
            inquiry.value.updatedAt = new Date().toISOString();
            alert('문의 상태가 변경되었습니다.');
          }
        } catch (error) {
          console.error('문의 상태 변경 오류:', error);
          
          // 서버 응답에 오류 메시지가 있는지 확인
          if (error.response && error.response.data) {
            const serverError = error.response.data;
            const errorMessage = serverError.error?.message || '문의 상태 변경 중 오류가 발생했습니다.';
            alert('오류: ' + errorMessage);
          } else {
            alert('문의 상태 변경 중 오류가 발생했습니다.');
          }
        } finally {
          showStatusChangeModal.value = false;
        }
      } else {
        // 상태 변경 없을 경우 모달만 닫기
        showStatusChangeModal.value = false;
      }
    };

    const getInquiryTypeDisplayName = (type) => {
      const typeDisplayMap = {
        'ACCOUNT': '계정 문의',
        'TECHNICAL': '기술 문의',
        'USAGE': '이용 문의',
        'ETC': '기타 문의'
      };
      return typeDisplayMap[type] || type;
    };
    
    // 파일 크기 형식화 함수
    const formatFileSize = (size) => {
      if (size < 1024) return size + ' B';
      else if (size < 1048576) return Math.round(size / 1024) + ' KB';
      else return Math.round(size / 1048576 * 10) / 10 + ' MB';
    };
    
    // 파일 변경 이벤트 처리
    const handleFileChange = (event) => {
      if (event.target.files) {
        // 기존 파일에 새 파일 추가
        const newFiles = Array.from(event.target.files);
        selectedFiles.value = [...selectedFiles.value, ...newFiles];
      }
      // 파일 선택 초기화 (동일한 파일 재선택 가능하도록)
      event.target.value = '';
    };
    
    // 파일 삭제 함수
    const removeFile = (index) => {
      selectedFiles.value.splice(index, 1);
    };
    
    // 수정 시 새 파일 삭제 함수
    const removeEditFile = (index) => {
      editingCommentNewFiles.value.splice(index, 1);
    };
    
    // 댓글 수정 시작
    const startEditingComment = (comment) => {
      editingCommentId.value = comment.commentId || comment.id;
      editingCommentText.value = comment.commentContent || comment.content;
      isEditingComment.value = true;
      commentError.value = null;
      
      // 첨부파일 복사 (기존 첨부파일에 toDelete 속성 추가)
      if (comment.attachments && comment.attachments.length > 0) {
        editingCommentAttachments.value = comment.attachments.map(attachment => ({
          ...attachment,
          toDelete: false  // 삭제 체크박스 상태 추가
        }));
      } else {
        editingCommentAttachments.value = [];
      }
      
      // 새 첨부파일 초기화
      editingCommentNewFiles.value = [];
    };
    
    // 댓글 수정 취소
    const cancelEditingComment = () => {
      isEditingComment.value = false;
      editingCommentId.value = null;
      editingCommentText.value = '';
      editingCommentAttachments.value = [];
      editingCommentNewFiles.value = [];
      commentError.value = null;
    };
    
    // 수정 폼에서 파일 변경 이벤트 처리
    const handleEditFileChange = (event) => {
      if (event.target.files) {
        // 기존 파일에 새 파일 추가
        const newFiles = Array.from(event.target.files);
        editingCommentNewFiles.value = [...editingCommentNewFiles.value, ...newFiles];
      }
      // 파일 선택 초기화 (동일한 파일 재선택 가능하도록)
      event.target.value = '';
    };
    
    // 답글 추가
    const addComment = async () => {
      // 답글 내용이 없는 경우 알림 표시
      if (!newComment.value.trim()) {
        alert('답글 내용을 입력해주세요.');
        return;
      }
      
      try {
        commentError.value = null;
        isSubmittingComment.value = true;
        
        // 실제 API 호출
        const response = await inquiryApiAddComment(
          inquiryId.value,
          newComment.value,
          selectedFiles.value
        );
        
        // 서버 응답 구조 확인 (data.data.success 형식)
        if (response.data && response.data.data && response.data.data.success === false) {
          // 서버에서 오류를 반환한 경우
          const errorMessage = response.data.data.error?.message || '알 수 없는 오류가 발생했습니다.';
          console.error('서버 오류:', errorMessage);
          commentError.value = errorMessage;
          return;
        }
        
        // 성공적으로 답글이 등록된 경우 - 중첩 구조 처리 (data.data.data)
        if (response.data && response.data.data && response.data.data.data) {
          const newCommentData = response.data.data.data;
          
          // 기존 댓글 배열이 없으면 생성
          if (!inquiry.value.comments) {
            inquiry.value.comments = [];
          }
          
          // 서버에서 반환한 답글 데이터 추가
          inquiry.value.comments.push(newCommentData);
          
          // 입력 초기화
          newComment.value = '';
          selectedFiles.value = [];
          
          alert('답글이 성공적으로 등록되었습니다.');
        } else {
          // status가 2xx면 성공으로 간주
          if (response.status >= 200 && response.status < 300) {
            // 응답에 data가 있는지 확인
            let commentData;
            if (response.data.data && response.data.data.data) {
              commentData = response.data.data.data;
            } else if (response.data.data) {
              commentData = response.data.data;
            }
            
            // 기존 댓글 배열이 없으면 생성
            if (!inquiry.value.comments) {
              inquiry.value.comments = [];
            }
            
            // 답글 데이터가 있는 경우만 추가
            if (commentData) {
              inquiry.value.comments.push(commentData);
            }
            
            // 입력 초기화
            newComment.value = '';
            selectedFiles.value = [];
            
            alert('답글이 성공적으로 등록되었습니다.');
          } else {
            commentError.value = '답글 등록 중 오류가 발생했습니다.';
          }
        }
      } catch (error) {
        console.error('답글 추가 오류:', error);
        
        // 서버 응답에 오류 메시지가 있는지 확인
        if (error.response && error.response.data) {
          const serverError = error.response.data;
          // 중첩 구조 처리
          const errorMessage = 
            (serverError.data && serverError.data.error?.message) || 
            serverError.error?.message || 
            '답글 추가 중 오류가 발생했습니다.';
          commentError.value = errorMessage;
        } else {
          commentError.value = '답글 추가 중 오류가 발생했습니다.';
        }
      } finally {
        isSubmittingComment.value = false;
      }
    };
    
    // 문의 삭제
    const deleteInquiry = async () => {
      try {
        // 실제 API 호출
        const response = await inquiryApiDeleteInquiry(inquiryId.value);
        // 서버 응답의 success 필드 확인
        if (response.data && response.data.success === false) {
          // 서버에서 오류를 반환한 경우
          const errorMessage = response.data.error?.message || '알 수 없는 오류가 발생했습니다.';
          console.error('서버 오류:', errorMessage);
          alert('오류: ' + errorMessage);
          return;
        }
        
        alert('문의가 성공적으로 삭제되었습니다.');
        router.push('/inquiries'); // 목록 페이지로 이동
      } catch (error) {
        console.error('문의 삭제 오류:', error);
        
        // 서버 응답에 오류 메시지가 있는지 확인
        if (error.response && error.response.data) {
          const serverError = error.response.data;
          const errorMessage = serverError.error?.message || '문의 삭제 중 오류가 발생했습니다.';
          alert('오류: ' + errorMessage);
        } else {
          alert('문의 삭제 중 오류가 발생했습니다.');
        }
      } finally {
        showDeleteModal.value = false; // 모달 닫기
      }
    };
    
    // 삭제 확인 모달 표시
    const showDeleteConfirmation = () => {
      // Bootstrap 모달을 통한 삭제 확인
      const myModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
      myModal.show();
    };
    
    // 첨부파일 삭제 확인
    const confirmDeleteAttachment = (attachment) => {
      const fileName = attachment.originalFileName || attachment.name || '첨부파일';
      if (confirm(`"${fileName}" 파일을 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.`)) {
        deleteAttachment(attachment.attachmentId);
      }
    };
    
    // 첨부파일 삭제 실행
    const deleteAttachment = async (attachmentId) => {
      if (!attachmentId) return;
      
      try {
        // 삭제 API 호출
        await inquiryApiDeleteAttachment(attachmentId);
        
        // 첨부파일 목록 업데이트 (문의사항)
        if (inquiry.value && inquiry.value.attachments) {
          inquiry.value.attachments = inquiry.value.attachments.filter(
            item => item.attachmentId !== attachmentId
          );
        }
        
        // 첨부파일 목록 업데이트 (댓글)
        if (inquiry.value && inquiry.value.comments) {
          inquiry.value.comments.forEach(comment => {
            if (comment.attachments) {
              comment.attachments = comment.attachments.filter(
                item => item.attachmentId !== attachmentId
              );
            }
          });
        }
        
        // 상태 알림
        alert('첨부파일이 삭제되었습니다.');
      } catch (error) {
        console.error('첨부파일 삭제 오류:', error);
        alert('첨부파일 삭제 중 오류가 발생했습니다.');
      }
    };
    
    // 첨부파일 다운로드
    const downloadAttachment = async (attachmentId) => {
      try {
        // 첨부파일 찾기
        const attachment = inquiry.value.attachments.find(att => att.attachmentId === attachmentId);
        if (!attachment) {
          throw new Error('첨부파일 정보를 찾을 수 없습니다.');
        }
        
        // 다운로드 API 호출
        await inquiryApiDownloadAttachment(attachmentId, attachment.originalFileName);
      } catch (error) {
        alert('파일 다운로드 중 오류가 발생했습니다.');
        console.error('파일 다운로드 오류:', error);
      }
    };
    
    // 댓글(답글) 첨부파일 다운로드
    const downloadCommentAttachment = async (attachmentId, originalFileName) => {
      try {
        
        // 다운로드 API 호출 (문의사항과 동일한 URL 사용)
        await inquiryApiDownloadCommentAttachment(attachmentId, originalFileName);
      } catch (error) {
        alert('댓글 첨부파일 다운로드 중 오류가 발생했습니다.');
        console.error('댓글 첨부파일 다운로드 오류:', error);
      }
    };
    
    // 날짜 형식화
    const formatDate = (dateString) => {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      return date.toLocaleDateString('ko-KR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };
    
    // 컴포넌트 마운트 시 문의 상세 로드
    onMounted(() => {
      loadInquiry();
    });
    
    // 댓글 수정 API 호출
    const updateComment = async () => {
      // 댓글 내용이 없는 경우 알림 표시
      if (!editingCommentText.value.trim()) {
        alert('답글 내용을 입력해주세요.');
        return;
      }
      
      try {
        commentError.value = null;
        isSubmittingComment.value = true;
        
        // 삭제할 첨부파일 ID 목록 생성
        const deletedAttachmentIds = editingCommentAttachments.value
          .filter(attachment => attachment.toDelete)
          .map(attachment => attachment.attachmentId);
        
        // FormData 생성
        const formData = new FormData();
        formData.append('commentContent', editingCommentText.value);
        
        // 삭제할 첨부파일 ID가 있으면 추가
        if (deletedAttachmentIds.length > 0) {
          deletedAttachmentIds.forEach(id => {
            formData.append('deletedAttachmentIds', id);
          });
        }
        
        // 새 첨부파일 추가
        if (editingCommentNewFiles.value.length > 0) {
          editingCommentNewFiles.value.forEach(file => {
            formData.append('newAttachments', file);
          });
        }
        
        // 실제 API 호출 (PUT 요청)
        const response = await inquiryApiUpdateComment(inquiryId.value, editingCommentId.value, formData);
        
        if (response.data && response.data.success === false) {
          // 서버에서 오류를 반환한 경우
          const errorMessage = response.data.error?.message || '알 수 없는 오류가 발생했습니다.';
          console.error('서버 오류:', errorMessage);
          throw new Error(errorMessage);
        }
        
        // 응답 처리
        const result = response.data;
        
        // 성공 시 댓글 데이터 업데이트
        if (result.success && result.data) {
          // 기존 댓글 찾기
          const commentIndex = inquiry.value.comments.findIndex(
            c => (c.commentId || c.id) === editingCommentId.value
          );
          
          if (commentIndex !== -1) {
            // 응답 데이터로 댓글 업데이트
            inquiry.value.comments[commentIndex] = {
              commentId: result.data.commentId,
              id: result.data.commentId,
              commentContent: result.data.commentContent,
              content: result.data.commentContent,
              userEmail: result.data.userEmail,
              author: result.data.userEmail,
              createdAt: result.data.createdAt,
              attachments: result.data.attachments || []
            };
          }
          
          // 수정 모드 종료
          cancelEditingComment();
          
          alert('답글이 성공적으로 수정되었습니다.');
        }
      } catch (error) {
        console.error('답글 수정 오류:', error);
        commentError.value = error.message || '답글 수정 중 오류가 발생했습니다.';
      } finally {
        isSubmittingComment.value = false;
      }
    };
    
    // 답글 삭제 확인
    const confirmDeleteComment = (comment) => {
      const commentId = comment.commentId || comment.id;
      if (confirm('답글을 삭제하시겠습니까?')) {
        deleteComment(commentId);
      }
    };
    
    // 답글 삭제 API 호출
    const deleteComment = async (commentId) => {
      try {
        isSubmittingComment.value = true;
        commentError.value = null;
        
        // API 호출
        const response = await inquiryApiDeleteComment(inquiryId.value, commentId);
        
        // 응답 처리
        if (response.data && response.data.success) {
          // 성공적으로 답글 삭제 시, comments 배열에서 해당 답글 제거
          inquiry.value.comments = inquiry.value.comments.filter(
            comment => (comment.commentId !== commentId && comment.id !== commentId)
          );
          
          alert('답글이 삭제되었습니다.');
        } else if (response.data && response.data.success === false) {
          // 서버에서 오류를 반환한 경우
          const errorMessage = response.data.error?.message || '답글 삭제 중 오류가 발생했습니다.';
          commentError.value = errorMessage;
          console.error('서버 오류:', errorMessage);
        }
      } catch (error) {
        console.error('답글 삭제 오류:', error);
        commentError.value = error.message || '답글 삭제 중 오류가 발생했습니다.';
      } finally {
        isSubmittingComment.value = false;
      }
    };
    
    return {
      inquiryId,
      inquiry,
      isLoading,
      error,
      newComment,
      newStatus,
      canEdit,
      canDelete,
      canChangeStatus,
      canComment,
      canEditComment,
      changeInquiryStatus,
      addComment,
      deleteInquiry,
      showDeleteConfirmation,
      downloadAttachment,
      downloadCommentAttachment,
      confirmDeleteAttachment,
      deleteAttachment,
      formatDate,
      // 추가한 모달 관련 상태 변수
      showStatusChangeModal,
      showDeleteModal,
      // 추가한 모달 관련 함수
      openStatusChangeModal,
      // 상태값 관련 함수
      getStatusDisplayName,
      getStatusDescription,
      getInquiryTypeDisplayName,
      // 파일 첨부 관련 변수와 함수
      selectedFiles,
      isSubmittingComment,
      commentError,
      handleFileChange,
      removeFile,
      formatFileSize,
      // 답글 수정 관련 변수와 함수
      isEditingComment,
      editingCommentId,
      editingCommentText,
      editingCommentAttachments,
      editingCommentNewFiles,
      startEditingComment,
      cancelEditingComment,
      handleEditFileChange,
      removeEditFile,
      updateComment,
      // 답글 삭제 관련 함수
      confirmDeleteComment,
      deleteComment
    };
  }
};
</script>

<style scoped>
.inquiry-detail-view {
  padding: 20px;
  max-width: 960px;
  margin: 0 auto;
}

/* 문의 정보 스타일 */
.inquiry-info {
  color: #4a5568;
  margin-bottom: 20px;
}

/* 문의 내용 스타일 */
.card {
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 20px;
}

.card-header {
  background-color: #f5f5f5;
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
}

.card-body {
  padding: 15px;
}

.inquiry-content {
  white-space: pre-line;
}

/* 첨부파일 스타일 */
.list-group-item {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-top: 0;
}

.list-group-item:first-child {
  border-top: 1px solid #ddd;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.list-group-item:last-child {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

/* 뱃지 스타일 */
.badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.badge.bg-warning {
  background-color: #ffeeba !important;
  color: #856404;
}

.badge.bg-primary {
  background-color: #b8daff !important;
  color: #004085;
}

.badge.bg-success {
  background-color: #c3e6cb !important;
  color: #155724;
}

.badge.bg-secondary {
  background-color: #d6d8db !important;
  color: #383d41;
}

/* 버튼 스타일 */
.action-buttons {
  margin: 24px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.btn {
  font-weight: 500;
  padding: 5px 8px;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  border: none;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.btn-outline-primary {
  background-color: #eef2ff;
  color: #4f46e5;
  border: 1px solid #e0e7ff;
}

.btn-outline-primary:hover {
  background-color: #4f46e5;
  border-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.2);
}

.btn-outline-secondary {
  background-color: #f1f5f9;
  color: #334155;
  border: 1px solid #e2e8f0;
}

.btn-outline-secondary:hover {
  background-color: #334155;
  border-color: #334155;
  color: white;
  box-shadow: 0 4px 8px rgba(51, 65, 85, 0.2);
}

.btn-outline-danger {
  background-color: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-outline-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: white;
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.2);
}

.btn-outline-dark {
  background-color: #f8fafc;
  color: #1e293b;
  border: 1px solid #e2e8f0;
}

.btn-outline-dark:hover {
  background-color: #1e293b;
  border-color: #1e293b;
  color: white;
  box-shadow: 0 4px 8px rgba(30, 41, 59, 0.2);
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background-color: #4338ca;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
  background-color: #64748b;
  color: white;
}

.btn-secondary:hover {
  background-color: #475569;
  box-shadow: 0 4px 8px rgba(100, 116, 139, 0.3);
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

/* 답글 섹션 스타일 */
.comments-section {
  margin-top: 30px;
}

.comments-section h3 {
  font-size: 1.25rem;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

.comments-list .card {
  margin-bottom: 15px;
}

/* 답글 작성 폼 스타일 */
.comment-form {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  border: 1px solid #ddd;
}

.comment-form h4 {
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 15px;
}

.form-group {
  margin-bottom: 15px;
}

/* 셀렉트 박스 스타일 */
.custom-select {
  display: block;
  width: 100%;
  padding: 12px 16px;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  color: #1e293b;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  appearance: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.custom-select:hover {
  border-color: #cbd5e1;
}

.custom-select:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
}

.form-label {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.95rem;
}

.status-description {
  font-size: 0.875rem;
  color: #64748b;
  padding: 8px 0;
  border-top: 1px dashed #e2e8f0;
}

.form-control {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 알림 메시지 */
.alert {
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid transparent;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffeeba;
  color: #856404;
}

/* 모달 스타일 - Vue 방식 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-wrapper {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 480px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  transform: translateY(0);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content {
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 24px;
  border-bottom: 1px solid #eef2f6;
  background-color: #f9fafc;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  font-size: 22px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #eef2f6;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>