import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [
    vue(),
    // VueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)) // <<< [추가] `@`를 src 디렉토리로 설정
    }
  },
  // <<< [추가] 개발 서버 설정
  server: {
    port: 9998, // 사용할 포트 번호
    host: true, // 외부 네트워크에서 접근 가능하게 하려면 주석 해제

    // 히스토리 모드 지원을 위한 미들웨어 추가
    configureServer: (server) => {
      server.middlewares.use((req, _res, next) => {
        // 정적 파일 요청이 아닌 경우 index.html로 리다이렉션
        if (!req.url.match(/\.(js|css|ico|png|jpg|jpeg|gif|svg|json)$/)) {
          // 정적 파일이 아닌 모든 요청은 index.html로 전달
          req.url = '/'; // index.html을 제공하도록 URL 변경
        }

        next();
      });
    },
  },

  // 빌드 설정
  build: {
    // 생성된 정적 자산을 위한 경로
    outDir: 'dist',
  },

  // 히스토리 모드 지원 (새로고침 시 404 문제 해결)
  base: '/'
})
