// src/api/interceptors.js
import apiClient from './index';
import { useAuthStore } from '@/stores/auth'; // 스토어 접근
import { refreshToken as apiRefreshToken } from '@/api/auth'; // API 함수
import router from '@/router'; // 필요시 라우터 접근 (리다이렉션용)

// 토큰 갱신 관련 상태 변수
let isRefreshing = false;
let failedQueue = [];

/**
 * 대기 중인 요청 큐를 처리하는 함수
 * @param {Error|null} error - 에러 객체 또는 null (성공 시)
 * @param {string|null} token - 새로운 액세스 토큰 또는 null (실패 시)
 */
const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  // 큐 초기화
  failedQueue = [];
};

/**
 * 토큰 갱신 후 로그아웃 처리 함수
 * @param {Error} error - 토큰 갱신 실패 에러
 * @param {Object} authStore - 인증 스토어 인스턴스
 */
const handleTokenRefreshFailure = (error, authStore) => {
  console.error('토큰 갱신 실패:', error);
  // 갱신 실패 시 대기열 처리 (실패)
  processQueue(error, null);

  // 로그인 페이지가 아니고 랜딩 페이지도 아닌 경우에만 로그아웃 처리 및 리다이렉션 수행
  const currentRouteName = router.currentRoute.value.name;
  if (currentRouteName !== 'login' && currentRouteName !== 'public-landing-page') {
    authStore.logout();
    router.push('/login').catch(err => {
      console.error('로그인 페이지 리다이렉션 실패:', err);
    });
  } else if (currentRouteName === 'public-landing-page') {
    // 랜딩 페이지에서는 로그아웃만 하고 리다이렉션은 하지 않음
    authStore.logout();
  }
  return Promise.reject(error);
};

/**
 * Axios 인터셉터 설정 함수
 */
export function setupInterceptors() {
  const authStore = useAuthStore();

  // --- 요청 인터셉터 --- (헤더에 토큰 추가 및 projectId 파라미터 조건부 추가)
  apiClient.interceptors.request.use(
    (config) => {
      const token = authStore.accessToken;
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }

      // 프로젝트 ID가 있는 경우에만 URL 파라미터로 추가
      const currentProjectId = authStore.currentProject?.projectId;
      if (currentProjectId !== undefined && currentProjectId !== null) {
        if (config.params) {
          config.params.projectId = currentProjectId;
        } else {
          config.params = { projectId: currentProjectId };
        }
      }
      // 선택된 프로젝트가 없으면 projectId 파라미터를 보내지 않음

      return config;
    },
    (error) => Promise.reject(error)
  );

  // --- 응답 인터셉터 --- (401 처리 및 토큰 갱신)
  apiClient.interceptors.response.use(
    (response) => response, // 정상 응답은 그대로 반환
    async (error) => {
      const originalRequest = error.config;

      // 401 에러 처리 조건 확인
      const isUnauthorized = error.response?.status === 401;
      const isNotRetry = !originalRequest._retry;
      const isNotRefreshRequest = originalRequest.url !== '/auth/refresh';
      const isNotLoginRequest = originalRequest.url !== '/auth/login';

      // 초기 비밀번호 변경 요청인지 확인
      const isInitialPasswordChangeRequest = originalRequest.url === '/auth/change-initial-password';

      // 랜딩 페이지 관련 API 요청인지 확인
      const isLandingPageRequest = originalRequest.url.includes('/public/landing/');

      // 이벤트 관련 API 요청인지 확인
      const isEventPageRequest = originalRequest.url.includes('/public/event/');

      // 랜딩 페이지 관련 요청이거나 현재 경로가 랜딩 페이지인 경우 인증 오류 무시
      if (isUnauthorized && (isLandingPageRequest 
        || router.currentRoute.value.name === 'public-landing-page' 
        || isEventPageRequest 
        || router.currentRoute.value.name === 'public-event-page')) {
        return Promise.reject(error); // 인터셉터에서 추가 처리 없이 에러 그대로 반환
      }

      // 초기 비밀번호 변경 요청에서 401 인증 오류는 특별 처리
      if (isUnauthorized && isInitialPasswordChangeRequest) {
        return Promise.reject(error); // 인터셉터에서 추가 처리 없이 에러 그대로 반환
      }

      // 토큰 갱신이 필요한 경우
      if (isUnauthorized && isNotRetry && isNotRefreshRequest && isNotLoginRequest) {
        // 이미 다른 요청이 토큰 갱신 중이라면, 현재 요청은 대기열에 추가
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return apiClient(originalRequest);
          }).catch(err => Promise.reject(err));
        }

        // 토큰 갱신 시작
        originalRequest._retry = true;
        isRefreshing = true;

        try {
          // 토큰 갱신 요청
          const refreshResponse = await apiRefreshToken();
          const newAccessToken = refreshResponse.accessToken;

          // 새 토큰 설정
          authStore.setAccessToken(newAccessToken);

          // 대기 중인 모든 요청 처리 (성공)
          processQueue(null, newAccessToken);

          // 현재 실패했던 원래 요청의 헤더를 새 토큰으로 변경
          originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;

          // 원래 요청 재시도
          return apiClient(originalRequest);
        } catch (refreshError) {
          return handleTokenRefreshFailure(refreshError, authStore);
        } finally {
          isRefreshing = false; // 토큰 갱신 상태 해제
        }
      }

      // 401 에러가 아니거나 처리할 수 없는 에러는 그대로 반환
      return Promise.reject(error);
    }
  );
}
