<template>
  <div class="statistics-layout">
    <h1>통계</h1>

    <div v-if="canViewStatistics">
      <div class="statistics-nav">
        <router-link :to="{ name: 'statistics-qr' }" class="nav-link">QR코드 통계</router-link>
        <router-link :to="{ name: 'statistics-admin' }" class="nav-link">관리자 통계</router-link>
        <router-link :to="{ name: 'statistics-usage' }" class="nav-link">사용 통계</router-link>
      </div>

      <div class="statistics-content">
        <router-view />
      </div>
    </div>
    
    <div v-else class="project-select-message">
      <div class="message-container">
        <p>프로젝트를 선택해주세요.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth';
import { computed } from 'vue';

const authStore = useAuthStore();

// SUPER_ADMIN이거나 현재 프로젝트가 선택되어 있으면 통계를 볼 수 있음
const canViewStatistics = computed(() => {
  return authStore.isSuperAdmin || authStore.currentProject;
});
</script>

<style scoped>
.statistics-layout {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: #333;
}

.statistics-nav {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.nav-link {
  padding: 8px 16px;
  text-decoration: none;
  color: #333;
  border-radius: 4px;
  transition: all 0.3s;
  font-weight: 500;
}

.nav-link:hover, .nav-link.router-link-active {
  background-color: #e9ecef;
  color: #007bff;
}

.statistics-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.project-select-message {
  margin-top: 30px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.message-container {
  padding: 40px 20px;
}

.message-container p {
  font-size: 18px;
  color: #666;
  margin: 0;
}

@media (max-width: 768px) {
  .statistics-nav {
    flex-direction: column;
    gap: 10px;
  }

  .nav-link {
    padding: 10px;
  }
}
</style>
