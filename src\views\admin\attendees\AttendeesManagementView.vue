<template>
  <div class="attendees-management">
    <h1>참가자 관리</h1>

    <div class="filters">
      <div class="filter-group">
        <label for="eventFilter">이벤트 선택:</label>
        <select id="eventFilter" v-model="selectedEventId" @change="onEventChange">
          <option value="">모든 이벤트</option>
          <!-- 이벤트 목록이 events.value에 올바르게 할당되면 여기가 채워집니다 -->
          <option v-for="event in events" :key="event.eventId" :value="event.eventId">
            {{ event.eventName }}
          </option>
        </select>
        
        <!-- 페이지당 항목 수 선택 UI -->
        <div class="items-per-page-selector">
          <span>페이지당 항목:</span>
          <div class="items-per-page-buttons">
            <button 
              @click="changeItemsPerPage(10)" 
              :class="['item-count-btn', itemsPerPage === 10 ? 'active' : '']">
              10개
            </button>
            <button 
              @click="changeItemsPerPage(30)" 
              :class="['item-count-btn', itemsPerPage === 30 ? 'active' : '']">
              30개
            </button>
            <button 
              @click="changeItemsPerPage(50)" 
              :class="['item-count-btn', itemsPerPage === 50 ? 'active' : '']">
              50개
            </button>
          </div>
        </div>
      </div>

      <div class="filter-group">
        <label for="searchTypeSelect">검색 유형:</label>
        <select id="searchTypeSelect" v-model="searchType">
          <option v-for="type in availableSearchTypes.length > 0 ? availableSearchTypes : searchTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>

        <label for="searchInput">검색어:</label>
        <input
          id="searchInput"
          v-model="searchQuery"
          @input="handleSearch"
          @keyup.enter="searchAttendees"
          placeholder="검색어 입력"
        />

        <div class="button-group">
          <button @click="searchAttendees" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>
    </div>

    <div class="attendees-table-container">
      <!-- 참석 권한 변경 버튼 영역 -->
      <div class="bulk-actions" v-if="attendees.length > 0">
        <div class="selected-count">
          {{ selectedAttendees.length }}명 선택됨
        </div>
        <div class="action-buttons">
          <button
            class="bulk-action-btn confirm-btn"
            @click="updateAttendanceConfirm('Y')"
            :disabled="selectedAttendees.length === 0"
          >
            참석가능 처리
          </button>
          <button
            class="bulk-action-btn reject-btn"
            @click="updateAttendanceConfirm('N')"
            :disabled="selectedAttendees.length === 0"
          >
            참석불가 처리
          </button>
        </div>
      </div>

      <table class="attendees-table" v-if="attendees.length > 0">
        <thead>
          <tr>
            <th class="checkbox-column">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
                :indeterminate.prop="isIndeterminate"
              >
            </th>
            <th>번호</th>
            <th>이름</th>
            <th>이메일</th>
            <th>연락처</th>
            <th>이벤트</th>
            <th>고유 번호</th>
            <th>등록일시</th>
            <th>참석 권한 여부</th>
            <th>실제 참석 상태</th>
            <th>기능</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(attendee, index) in paginatedAttendees" :key="attendee.id">
            <td class="checkbox-column">
              <input
                type="checkbox"
                :value="attendee.id"
                v-model="selectedAttendeeIds"
              >
            </td>
            <td>{{ calculateIndex(index) }}</td>
            <td>{{ attendee.attendeeName }}</td>
            <td>{{ attendee.attendeeEmail }}</td>
            <td>{{ attendee.attendeeContact }}</td>
            <td>{{ attendee.eventName }}</td>
            <td>{{ attendee.confirmationCode }}</td>
            <td>{{ formatDate(attendee.createdAt) }}</td>
            <td>
              <span class="status-badge" :class="getAttendedConfirmClass(attendee.attendedConfirmYn)">
                {{ getAttendedConfirmText(attendee.attendedConfirmYn) }}
              </span>
            </td>
            <td>
              <span class="status-badge" :class="getStatusClass(attendee.attendedYn)">
                {{ getStatusText(attendee.attendedYn) }}
              </span>
            </td>
            <td>
              <button class="action-btn view-btn" @click="viewAttendee(attendee)">상세</button>
              <button class="action-btn attend-btn" @click="updateAttendanceStatus(attendee.id, true)">참석처리</button>
              <button class="action-btn not-attend-btn" @click="updateAttendanceStatus(attendee.id, false)">미참석처리</button>
              <button class="action-btn delete-btn" @click="confirmDelete(attendee)">삭제</button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-else-if="isLoading" class="loading-message">
        <div class="loading-spinner"></div>
        <p>참가자 정보를 불러오는 중입니다...</p>
      </div>

      <div v-else-if="authStore.user?.roleId !== 'SUPER_ADMIN' && !authStore.currentProject" class="no-data-message">
        <p>프로젝트를 선택해주세요.</p>
      </div>

      <div v-else class="no-data-message">
        <p v-if="selectedEventId">선택한 이벤트에 참가자 정보가 없습니다.</p>
        <p v-else>참가자 정보가 없습니다.</p>
      </div>
    </div>

    <!-- 페이지네이션 -->
    <div class="pagination" v-if="totalPages > 1">
      <button
        @click="goToPage(0)"
        :disabled="currentPage === 0"
        class="pagination-btn"
      >
        &laquo;
      </button>
      <button
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage === 0"
        class="pagination-btn"
      >
        &lt;
      </button>

      <span class="page-info">{{ displayPage }} / {{ totalPages }}</span>

      <button
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage === totalPages - 1"
        class="pagination-btn"
      >
        &gt;
      </button>
      <button
        @click="goToPage(totalPages - 1)"
        :disabled="currentPage === totalPages - 1"
        class="pagination-btn"
      >
        &raquo;
      </button>
    </div>

    <!-- 참가자 상세 정보 모달 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <h2>참가자 상세 정보</h2>
        <div class="modal-close" @click="closeModal">&times;</div>

        <div class="attendee-details" v-if="selectedAttendee">
          <div class="detail-row">
            <span class="detail-label">이름:</span>
            <span class="detail-value">{{ selectedAttendee.attendeeName }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">이메일:</span>
            <span class="detail-value">{{ selectedAttendee.attendeeEmail }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">연락처:</span>
            <span class="detail-value">{{ selectedAttendee.attendeeContact }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">이벤트:</span>
            <span class="detail-value">{{ selectedAttendee.eventName }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">고유 번호:</span>
            <span class="detail-value status-badge" :class="getStatusClass(selectedAttendee.confirmationCode)">
              {{ selectedAttendee.confirmationCode }}
            </span>
          </div>
          <div class="detail-row">
            <span class="detail-label">등록일시:</span>
            <span class="detail-value">{{ formatDate(selectedAttendee.createdAt) }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">참석 권한 여부:</span>
            <span class="detail-value status-badge" :class="getAttendedConfirmClass(selectedAttendee.attendedConfirmYn)">
              {{ getAttendedConfirmText(selectedAttendee.attendedConfirmYn) }}
            </span>
          </div>
          <div class="detail-row">
            <span class="detail-label">실제 참석 상태:</span>
            <span class="detail-value status-badge" :class="getStatusClass(selectedAttendee.attendedYn)">
              {{ getStatusText(selectedAttendee.attendedYn) }}
            </span>
          </div>
          <div class="detail-row">
            <span class="detail-label">참석 상태 변경:</span>
            <div class="detail-value">
              <button class="action-btn attend-btn" @click="updateAttendanceStatus(selectedAttendee.id, true)">참석처리</button>
              <button class="action-btn not-attend-btn" @click="updateAttendanceStatus(selectedAttendee.id, false)">미참석처리</button>
            </div>
          </div>

          <h3>항목 정보</h3>
          <div class="additional-info">
            <div v-if="selectedAttendee.submissionData && Object.keys(selectedAttendee.submissionData).length === 0" class="no-data-message">
              추가한 항목 정보가 없습니다.
            </div>
            <div v-else-if="selectedAttendee.submissionData" v-for="(value, key) in selectedAttendee.submissionData" :key="key" class="detail-row">
              <span class="detail-label">항목 이름:</span>
              <span class="detail-value field-id">{{ key }}</span>
              <span class="detail-label">입력한 값:</span>
              <span class="detail-value">{{ value }}</span>
            </div>
            <div v-else class="no-data-message">
              항목 정보를 불러올 수 없습니다.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content delete-modal" @click.stop>
        <h2>참가자 삭제</h2>
        <div class="modal-close" @click="cancelDelete">&times;</div>

        <p>다음 참가자 정보를 삭제하시겠습니까?</p>
        <p><strong>{{ selectedAttendee?.attendeeName }}</strong> ({{ selectedAttendee?.attendeeEmail }})</p>

        <div class="modal-actions">
          <button class="cancel-btn" @click="cancelDelete">취소</button>
          <button class="confirm-delete-btn" @click="deleteAttendee">삭제</button>
        </div>
      </div>
    </div>

    <!-- 참석 상태 변경 확인 모달 -->
    <div v-if="showAttendanceModal" class="modal-overlay" @click="cancelAttendanceUpdate">
      <div class="modal-content attendance-modal" @click.stop>
        <h2>참석 상태 변경</h2>
        <div class="modal-close" @click="cancelAttendanceUpdate">&times;</div>

        <p>다음 참가자의 참석 상태를 변경하시겠습니까?</p>
        <p><strong>{{ getAttendeeNameById(attendanceStatusToUpdate.attendeeId) }}</strong></p>
        <p>변경할 상태: <strong>{{ attendanceStatusToUpdate.attended ? '참석완료' : '미참석' }}</strong></p>

        <div class="modal-actions">
          <button class="cancel-btn" @click="cancelAttendanceUpdate">취소</button>
          <button
            class="confirm-btn"
            :class="attendanceStatusToUpdate.attended ? 'confirm-attend-btn' : 'confirm-not-attend-btn'"
            @click="confirmAttendanceUpdate"
          >
            {{ attendanceStatusToUpdate.attended ? '참석처리' : '미참석처리' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';
import apiClient from '@/api/index';
import { getAllAttendees } from '@/api/attendees';

// 상태 관리
const authStore = useAuthStore();
const isLoading = ref(false);
const attendees = ref([]);
const events = ref([]);
const selectedEventId = ref('');
const searchQuery = ref('');
const appliedSearchQuery = ref(''); // 실제 검색에 적용된 검색어
const searchType = ref('attendeeName'); // 기본 검색 타입 설정
const appliedSearchType = ref('attendeeName'); // 실제 검색에 적용된 검색 타입
const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록

// 기본 검색 유형 목록 (서버에서 제공하지 않을 경우 사용)
const searchTypes = [
  { value: 'attendeeName', label: '이름' },
  { value: 'attendeeEmail', label: '이메일' },
  { value: 'attendeeContact', label: '연락처' },
  { value: 'confirmationCode', label: '고유 번호' },
  { value: 'registrationDate', label: '등록일시' }
];
const currentPage = ref(0); // 서버 페이지네이션은 0부터 시작
const itemsPerPage = ref(10);
const totalElements = ref(0);
const totalPages = ref(0);
const eventDetails = ref(null);

// 참석자 선택 관련 상태
const selectedAttendeeIds = ref([]);
const isUpdatingAttendance = ref(false);

// 모달 상태
const showDetailModal = ref(false);
const showDeleteModal = ref(false);
const showAttendanceModal = ref(false);
const selectedAttendee = ref(null);
const attendanceStatusToUpdate = ref({ attendeeId: null, attended: false });

// 전체 선택 여부 계산
const isAllSelected = computed(() => {
  return attendees.value.length > 0 && selectedAttendeeIds.value.length === attendees.value.length;
});

// 부분 선택 여부 계산 (인디터미네이트 상태)
const isIndeterminate = computed(() => {
  return selectedAttendeeIds.value.length > 0 && selectedAttendeeIds.value.length < attendees.value.length;
});

// 선택된 참석자 객체 배열
const selectedAttendees = computed(() => {
  return attendees.value.filter(attendee => selectedAttendeeIds.value.includes(attendee.id));
});

// 클라이언트 측 검색 기능 (서버에서 검색 API가 제공되면 서버 검색으로 변경 가능)
const filteredAttendees = computed(() => {
  if (!appliedSearchQuery.value) return attendees.value;

  const query = appliedSearchQuery.value.toLowerCase();

  return attendees.value.filter(attendee => {
    // 검색 타입에 따라 다른 필드 검색
    switch (appliedSearchType.value) {
      case 'attendeeName':
        return attendee.attendeeName && attendee.attendeeName.toLowerCase().includes(query);
      case 'attendeeEmail':
        return attendee.attendeeEmail && attendee.attendeeEmail.toLowerCase().includes(query);
      case 'attendeeContact':
        return attendee.attendeeContact && attendee.attendeeContact.toLowerCase().includes(query);
      case 'confirmationCode':
        return attendee.confirmationCode && attendee.confirmationCode.toLowerCase().includes(query);
      case 'registrationDate':
      case 'createdAt':
        return attendee.createdAt && attendee.createdAt.toLowerCase().includes(query);
      default:
        // 기본적으로 이름, 이메일, 연락처 검색
        return (attendee.attendeeName && attendee.attendeeName.toLowerCase().includes(query)) ||
               (attendee.attendeeEmail && attendee.attendeeEmail.toLowerCase().includes(query)) ||
               (attendee.attendeeContact && attendee.attendeeContact.toLowerCase().includes(query));
    }
  });
});

// 페이지 번호 표시용 (1부터 시작하는 페이지 번호)
const displayPage = computed(() => {
  return currentPage.value + 1;
});

// 페이지네이션은 서버에서 처리하므로 추가 계산이 필요 없음
const paginatedAttendees = computed(() => {
  // 검색 기능을 사용하는 경우 클라이언트 측에서 필터링
  if (searchQuery.value) {
    return filteredAttendees.value;
  }
  // 검색하지 않는 경우 서버에서 가져온 데이터 그대로 사용
  return attendees.value;
});

// 인덱스 계산 함수
const calculateIndex = (index) => {
  // 마지막 번호부터 시작하도록 변경
  return totalElements.value - (currentPage.value * itemsPerPage.value + index);
};

// 페이지당 항목 수 변경 함수
const changeItemsPerPage = (count) => {
  itemsPerPage.value = count;
  currentPage.value = 0; // 첫 페이지로 이동
  
  // 선택한 이벤트가 있으면 해당 이벤트의 참가자 목록 로드
  // 없으면 모든 참가자 목록 로드
  if (selectedEventId.value) {
    loadAttendees();
  } else {
    loadAllAttendees();
  }
};

// 날짜 관련 검색 타입인지 확인하는 함수
const isDateSearchType = (type) => {
  // 날짜 관련 필드명 목록
  const dateFields = ['registrationDate', 'createdAt'];
  return dateFields.includes(type);
};

// 날짜 형식 변환 함수 (2025. 05. 17. -> 2025-05-17)
const formatDateForSearch = (dateString) => {
  if (!dateString) return '';

  // 정규식을 사용하여 날짜 형식 변환
  // 2025. 05. 17. 또는 2025.05.17. 또는 2025.5.17. 등의 형식을 처리
  const dateMatch = dateString.match(/(\d{4})[.\s-]*(\d{1,2})[.\s-]*(\d{1,2})/);

  if (dateMatch) {
    const year = dateMatch[1];
    // 월과 일이 한 자리 수인 경우 앞에 0을 추가
    const month = dateMatch[2].padStart(2, '0');
    const day = dateMatch[3].padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 매칭되지 않으면 원래 문자열 반환
  return dateString;
};

// 검색 버튼 클릭 시 실행되는 함수
const searchAttendees = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동

  // 날짜 관련 검색 타입인 경우 날짜 형식 변환
  if (searchType.value && isDateSearchType(searchType.value) && searchQuery.value) {
    // 원본 검색어 저장
    const originalQuery = searchQuery.value;
    // 변환된 날짜 형식으로 검색어 업데이트
    const formattedDate = formatDateForSearch(originalQuery);

    if (formattedDate !== originalQuery) {
      searchQuery.value = formattedDate;
    }
  }

  // 검색 쿼리와 타입을 적용
  appliedSearchQuery.value = searchQuery.value;
  appliedSearchType.value = searchType.value;

};

// 검색 초기화 함수
const resetSearch = () => {
  searchQuery.value = '';
  appliedSearchQuery.value = '';
  // 서버에서 제공하는 검색 유형이 있으면 첫 번째 값을 사용, 없으면 기본값 사용
  if (availableSearchTypes.value.length > 0) {
    searchType.value = availableSearchTypes.value[0].value;
    appliedSearchType.value = availableSearchTypes.value[0].value;
  } else {
    searchType.value = 'attendeeName';
    appliedSearchType.value = 'attendeeName';
  }
  currentPage.value = 0;

  // 선택한 이벤트가 있으면 해당 이벤트의 참가자 목록 로드
  // 없으면 모든 참가자 목록 로드
  if (selectedEventId.value) {
    loadAttendees();
  } else {
    loadAllAttendees();
  }
};

// 검색 처리 (디바운스 적용)
let searchTimeout;
const handleSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // 검색어가 비어있을 때는 아무 동작도 하지 않음
    // 사용자가 직접 검색 버튼을 클릭하거나 Enter 키를 눌러야 데이터가 로드됨
    // 검색어 입력 중에는 화면이 바뀌지 않음
  }, 300);
};

// 이벤트 목록 로드
const loadEvents = async () => {
  try {
    isLoading.value = true;

    // SUPER_ADMIN인 경우 프로젝트 ID 없이도 이벤트 목록을 로드할 수 있음
    const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

    // 현재 프로젝트의 이벤트 목록 가져오기
    const projectId = authStore.currentProject?.projectId;

    // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
    if (!isSuperAdmin && !projectId) {
      console.error('프로젝트 ID가 없습니다.');
      events.value = [];
      return;
    }

    // API 호출 파라미터 준비 - 서버 요구사항에 맞게 수정
    const paginationParams = {
      page: 0,
      size: 100, // 충분히 큰 값으로 설정하여 모든 이벤트를 가져옴
      sort: "registrationDate,desc" // 등록일시 기준 내림차순 정렬 (배열이 아닌 문자열로 전송)
    };

    // 이벤트 목록 API 호출 - 서버 요구사항에 맞게 수정
    const response = await apiClient.get(`/events`, {
      params: {
        projectId: projectId,
        page: paginationParams.page,
        size: paginationParams.size,
        sort: paginationParams.sort
      }
    });

    if (response && response.data && response.data.success === true) {
      if (response.data.data && Array.isArray(response.data.data.content)) {
        events.value = response.data.data.content;
      }
      else if (Array.isArray(response.data.data)) {
        events.value = response.data.data;
      } else {
        // Unexpected structure inside response.data.data
        console.error('DEBUG: response.data.success was true, but response.data.data structure is unexpected or content array is missing.', response.data.data);
        console.error('API 응답 형식이 예상과 다릅니다. data.data.content 배열을 찾을 수 없습니다.', response.data);
        events.value = [];
      }
    } else {
      // response.data.success is not true or response.data is invalid
      console.error('DEBUG: response.data.success was not true or response.data was invalid.', response?.data);
      console.error('이벤트 목록을 불러오는 데 실패했습니다.', response?.data?.message || 'API 응답 실패 또는 형식이 올바르지 않음');
      events.value = [];
    }
  } catch (error) {
    console.error('이벤트 목록 로드 실패:', error);
    if (error.response) {
      console.error('서버 응답:', error.response.data);
      console.error('상태 코드:', error.response.status);
    }
    events.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 참가자 목록 로드
const loadAttendees = async () => {
  try {
    isLoading.value = true;

    // 이벤트 ID가 없으면 로드하지 않음
    if (!selectedEventId.value) {
      attendees.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
      eventDetails.value = null;
      return;
    }

    // 페이지네이션 파라미터 준비
    const params = {
      page: currentPage.value,
      size: itemsPerPage.value,
      sort: 'registrationDate,desc' // 등록일시 기준 내림차순 정렬
    };

    // API 호출
    const response = await apiClient.get(`/events/${selectedEventId.value}/attendees`, { params });

    // 응답 데이터 처리
    if (response && response.data) {
      // 이벤트 상세 정보 처리
      if (response.data.eventDetails) {
        eventDetails.value = response.data.eventDetails;
      } else {
        console.warn('이벤트 정보가 없습니다.');
        eventDetails.value = null;
      }

      // 참가자 데이터 처리
      if (response.data.attendees) {
        // 참가자 데이터 객체
        const attendeesData = response.data.attendees;

        // 페이지네이션 정보 업데이트
        totalPages.value = attendeesData.totalPages || 0;
        totalElements.value = attendeesData.totalElements || 0;

        // 참가자 데이터 업데이트
        if (attendeesData.content && Array.isArray(attendeesData.content)) {

          // 참가자 데이터 매핑
          attendees.value = attendeesData.content.map(attendee => ({
            id: attendee.attendeeId,
            attendeeName: attendee.attendeeName || '-',
            attendeeEmail: attendee.attendeeEmail || '-',
            attendeeContact: attendee.attendeeContact || '-',
            eventId: selectedEventId.value ? parseInt(selectedEventId.value) : null,
            eventName: eventDetails.value?.eventName || '',
            confirmationCode: attendee.confirmationCode,
            createdAt: attendee.registrationDate,
            attendedConfirmYn: attendee.attendedConfirmYn || 'N',
            attendedYn: attendee.attendedYn || 'PENDING',
            submissionData: attendee.submissionData || {}
          }));
        } else {
          console.warn('참가자 데이터 content 배열이 없거나 배열이 아닙니다.');
          attendees.value = [];
        }
      } else {
        console.error('응답에 attendees 객체가 없습니다.', response.data);
        attendees.value = [];
        totalPages.value = 0;
        totalElements.value = 0;
      }
    } else {
      console.error('응답 데이터가 없습니다.', response);
      attendees.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
      eventDetails.value = null;
    }
  } catch (error) {
    console.error('참가자 목록 로드 실패:', error);
    if (error.response) {
      console.error('서버 응답:', error.response.data);
      console.error('상태 코드:', error.response.status);
    }
    attendees.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
    eventDetails.value = null;
  } finally {
    isLoading.value = false;
  }
};

// 참가자 상세 정보 보기
const viewAttendee = (attendee) => {
  selectedAttendee.value = attendee;
  showDetailModal.value = true;
};

// 참가자 삭제 확인
const confirmDelete = (attendee) => {
  selectedAttendee.value = attendee;
  showDeleteModal.value = true;
};

// 참가자 삭제 취소
const cancelDelete = () => {
  showDeleteModal.value = false;
  selectedAttendee.value = null;
};

// 참가자 삭제 실행
const deleteAttendee = async () => {
  try {
    if (!selectedAttendee.value || !selectedAttendee.value.id) {
      alert('삭제할 참가자 정보가 없습니다.');
      return;
    }

    isLoading.value = true;

    // 이벤트 ID 가져오기
    let eventId = selectedEventId.value;

    // 선택한 이벤트가 없는 경우, 참가자의 eventId 사용
    if (!eventId && selectedAttendee.value && selectedAttendee.value.eventId) {
      eventId = selectedAttendee.value.eventId;
    }

    // 이벤트 ID 검사
    if (!eventId) {
      alert('이벤트 ID를 찾을 수 없습니다. 이벤트를 선택하거나 다른 참가자를 선택해주세요.');
      return;
    }

    // API 호출
    await apiClient.delete(`/events/${eventId}/attendees/${selectedAttendee.value.id}`);

    // 삭제 후 목록 다시 불러오기
    if (selectedEventId.value) {
      // 선택한 이벤트가 있는 경우
      await loadAttendees();
    } else {
      // 선택한 이벤트가 없는 경우
      await loadAllAttendees();
    }

    showDeleteModal.value = false;
    selectedAttendee.value = null;

    // 성공 메시지 표시
    alert('참가자 정보가 삭제되었습니다.');
  } catch (error) {
    console.error('참가자 삭제 실패:', error);

    // 서버 오류 메시지 표시
    if (error.response && error.response.data && error.response.data.error) {
      alert(`삭제 실패: ${error.response.data.error.message || '서버 오류가 발생했습니다.'}`);
    } else {
      alert('참가자 삭제에 실패했습니다. 다시 시도해주세요.');
    }
  } finally {
    isLoading.value = false;
  }
};

// 모달 닫기
const closeModal = () => {
  showDetailModal.value = false;
  selectedAttendee.value = null;
};

// 상태 텍스트 변환
const getAttendedConfirmText = (attendedYn) => {
  const statusMap = {
    'Y': '참석가능',
    'N': '참석불가'
  };
  return statusMap[attendedYn] || attendedYn;
};

// 상태 클래스 변환
const getAttendedConfirmClass = (attendedYn) => {
  const classMap = {
    'Y': 'status-attended',
    'N': 'status-cancelled'
  };
  return classMap[attendedYn] || '';
};

// 상태 텍스트 변환
const getStatusText = (attendedYn) => {
  const statusMap = {
    'PENDING': '대기중',
    'REGISTERED': '등록됨',
    'CONFIRMED': '확인됨',
    'CANCELLED': '취소됨',
    'ATTENDED': '참석완료',
    'Y': '참석완료',
    'N': '불참'
  };
  return statusMap[attendedYn] || attendedYn;
};

// 상태 클래스 변환
const getStatusClass = (attendedYn) => {
  const classMap = {
    'PENDING': 'status-pending',
    'REGISTERED': 'status-registered',
    'CONFIRMED': 'status-confirmed',
    'CANCELLED': 'status-cancelled',
    'ATTENDED': 'status-attended',
    'Y': 'status-attended',
    'N': 'status-cancelled'
  };
  return classMap[attendedYn] || '';
};

// 날짜 포맷팅
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return new Intl.DateTimeFormat('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// 페이지 이동 함수
const goToPage = (page) => {
  // 페이지 범위 검사
  if (page < 0 || page >= totalPages.value) return;

  // 페이지 변경 및 데이터 로드
  currentPage.value = page;

  // 선택한 이벤트가 있으면 해당 이벤트의 참가자 목록 로드
  // 없으면 모든 참가자 목록 로드
  if (selectedEventId.value) {
    loadAttendees();
  } else {
    loadAllAttendees();
  }

  // 상단으로 스크롤
  window.scrollTo(0, 0);
};

// 페이지 변경 시 상단으로 스크롤
watch(currentPage, () => {
  window.scrollTo(0, 0);
});

// 모든 참가자 목록 로드
const loadAllAttendees = async () => {
  try {
    isLoading.value = true;

    // 페이지네이션 파라미터 준비
    const params = {
      page: currentPage.value,
      size: itemsPerPage.value,
      sort: 'registrationDate,desc' // 등록일시 기준 내림차순 정렬
    };

    // SUPER_ADMIN인 경우 프로젝트 ID 없이도 모든 참가자를 가져올 수 있음
    const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

    // 현재 프로젝트 ID 가져오기
    const projectId = authStore.currentProject?.projectId;

    // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
    if (!isSuperAdmin && !projectId) {
      console.error('프로젝트 ID가 없습니다.');
      attendees.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
      return;
    }

    let response;

    // SUPER_ADMIN인 경우 모든 참가자를 가져오는 API 호출
    if (isSuperAdmin) {
      // 선택적 파라미터 추가
      if (projectId) {
        params.projectId = projectId;
      }

      if (selectedEventId.value) {
        params.eventId = selectedEventId.value;
      }

      // 공개 객체를 일반 객체로 변환하여 전달
      const plainParams = {
        page: params.page,
        size: params.size,
        sort: params.sort
      };

      // 선택적 파라미터 추가
      if (params.projectId) plainParams.projectId = params.projectId;
      if (params.eventId) plainParams.eventId = params.eventId;

      response = await getAllAttendees(plainParams);
    } else {
      // 일반 사용자는 프로젝트별 참가자 목록 요청

      // API 호출 - 모든 참가자 목록 가져오기
      const token = authStore.accessToken;
      const headers = {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      response = await apiClient.get(`/events/${projectId}/all-attendees`, {
        params,
        headers
      });

      // 일반 관리자의 경우 response.data를 response로 변환하여 SUPER_ADMIN과 동일한 구조로 처리
      response = { success: response.data.success, data: response.data.data };
    }


    // 새로운 API 응답 구조 처리: { success: true, data: { ... } }
    if (response && response.success && response.data) {
      const responseData = response.data;

      // 검색 유형 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        availableSearchTypes.value = responseData.availableSearchTypes;

        // 검색 유형이 없는 경우 기본값 설정
        if (availableSearchTypes.value.length > 0 && !searchType.value) {
          searchType.value = availableSearchTypes.value[0].value;
          appliedSearchType.value = availableSearchTypes.value[0].value;
        }
      }

      // 페이지네이션 정보 업데이트
      totalPages.value = responseData.totalPages || 0;
      totalElements.value = responseData.totalElements || 0;

      // 참가자 데이터 업데이트 (content 배열 사용)
      if (responseData.content && Array.isArray(responseData.content)) {
        attendees.value = responseData.content.map(attendee => ({
          id: attendee.attendeeId,
          attendeeName: attendee.attendeeName || '-',
          attendeeEmail: attendee.attendeeEmail || '-',
          attendeeContact: attendee.attendeeContact || '-',
          eventId: attendee.eventId,
          eventName: attendee.eventName || '-',
          confirmationCode: attendee.confirmationCode,
          createdAt: attendee.registrationDate,
          attendedConfirmYn: attendee.attendedConfirmYn || 'N',
          attendedYn: attendee.attendedYn || 'PENDING',
          submissionData: attendee.submissionData || {}
        }));

      } else {
        console.warn('참가자 데이터가 없습니다.');
        attendees.value = [];
        totalPages.value = 0;
        totalElements.value = 0;
      }
    } else {
      console.warn('서버 응답에 데이터가 없습니다.');
      attendees.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
    }
  } catch (error) {
    console.error('모든 참가자 목록 로드 실패:', error);
    if (error.response) {
      console.error('서버 응답:', error.response.data);
      console.error('상태 코드:', error.response.status);
    }
    attendees.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 이벤트 변경 시 호출되는 함수
const onEventChange = () => {
  // 페이지 초기화
  currentPage.value = 0;

  if (selectedEventId.value) {
    // 선택한 이벤트의 참가자 목록 로드
    loadAttendees();
  } else {
    // 이벤트 선택을 취소한 경우 모든 참가자 목록 로드
    loadAllAttendees();
  }
};

// 전체 선택 토글 함수
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 전체 선택 해제
    selectedAttendeeIds.value = [];
  } else {
    // 전체 선택
    selectedAttendeeIds.value = attendees.value.map(attendee => attendee.id);
  }
};

// 참석 권한 변경 함수
const updateAttendanceConfirm = async (status) => {
  if (selectedAttendeeIds.value.length === 0) {
    alert('선택된 참석자가 없습니다.');
    return;
  }

  try {
    isUpdatingAttendance.value = true;

    // 참석 권한 변경 API 호출 - 수정된 엔드포인트 사용
    const response = await apiClient.put(`/admin/attendees/confirm-status`, {
      attendeeIds: selectedAttendeeIds.value,
      attendedConfirmYn: status
    });


    // 새로운 API 응답 구조 처리: { success: true, data: { ... } }
    if (response.data && response.data.success) {
    } else if (response.data && !response.data.success && response.data.error) {
      // 오류 처리
      throw new Error(response.data.error.message || '참석 권한 변경에 실패했습니다.');
    }

    // 성공 메시지 표시
    const statusText = status === 'Y' ? '참석가능' : '참석불가';
    alert(`선택한 참석자 ${selectedAttendeeIds.value.length}명의 참석 권한이 ${statusText}(으)로 변경되었습니다.`);

    // 선택 초기화
    selectedAttendeeIds.value = [];

    // 목록 다시 불러오기
    if (selectedEventId.value) {
      await loadAttendees();
    } else {
      await loadAllAttendees();
    }
  } catch (error) {
    console.error('참석 권한 변경 실패:', error);

    // 서버 오류 메시지 표시
    if (error.response && error.response.data && error.response.data.error) {
      alert(`변경 실패: ${error.response.data.error.message || '서버 오류가 발생했습니다.'}`);
    } else {
      alert('참석 권한 변경에 실패했습니다. 다시 시도해주세요.');
    }
  } finally {
    isUpdatingAttendance.value = false;
  }
};

// 참가자 ID로 이름 찾기
const getAttendeeNameById = (attendeeId) => {
  const attendee = attendees.value.find(a => a.id === attendeeId);
  return attendee ? attendee.attendeeName : '알 수 없음';
};

// 참석 상태 변경 모달 표시
const updateAttendanceStatus = (attendeeId, attended) => {
  attendanceStatusToUpdate.value = { attendeeId, attended };
  showAttendanceModal.value = true;
};

// 참석 상태 변경 취소
const cancelAttendanceUpdate = () => {
  showAttendanceModal.value = false;
  attendanceStatusToUpdate.value = { attendeeId: null, attended: false };
};

// 참석 상태 변경 확인
const confirmAttendanceUpdate = async () => {
  const { attendeeId, attended } = attendanceStatusToUpdate.value;

  if (!attendeeId) {
    alert('참석자 ID가 없습니다.');
    return;
  }

  try {
    isLoading.value = true;
    showAttendanceModal.value = false; // 모달 닫기


    // 참석 상태 변경 API 호출
    const response = await apiClient.put(`/admin/attendees/attend/manual/${attendeeId}`, {
      attended: attended
    });

    // 새로운 API 응답 구조 처리: { success: true, data: { ... } }
    if (response.data && response.data.success) {
      // 로컬 데이터 업데이트
      const attendeeIndex = attendees.value.findIndex(a => a.id === attendeeId);
      if (attendeeIndex !== -1) {
        attendees.value[attendeeIndex].attendedYn = attended ? 'Y' : 'N';
      }

      // 선택된 참가자가 있고, 그 ID가 변경된 참가자와 같으면 상태 업데이트
      if (selectedAttendee.value && selectedAttendee.value.id === attendeeId) {
        selectedAttendee.value.attendedYn = attended ? 'Y' : 'N';
      }

      // 성공 메시지 표시
      const statusText = attended ? '참석완료' : '미참석';
      alert(`참가자의 참석 상태가 ${statusText}(으)로 변경되었습니다.`);

      // 목록 다시 불러오기 - 데이터 일관성 보장
      if (selectedEventId.value) {
        await loadAttendees();
      } else {
        await loadAllAttendees();
      }
    } else if (response.data && !response.data.success && response.data.error) {
      // 오류 처리
      throw new Error(response.data.error.message || '참석 상태 변경에 실패했습니다.');
    }
  } catch (error) {
    console.error('참석 상태 변경 실패:', error);

    // 서버 오류 메시지 표시
    if (error.response && error.response.data && error.response.data.error) {
      alert(`변경 실패: ${error.response.data.error.message || '서버 오류가 발생했습니다.'}`);
    } else {
      alert('참석 상태 변경에 실패했습니다. 다시 시도해주세요.');
    }
  } finally {
    isLoading.value = false;
    attendanceStatusToUpdate.value = { attendeeId: null, attended: false };
  }
};

// 프로젝트 변경 이벤트 핸들러
const handleProjectChange = () => {
  // 프로젝트 변경 시 검색 초기화
  resetSearch();
  loadEvents();
};

// 컴포넌트 마운트 시 데이터 로드 및 이벤트 리스너 등록
onMounted(async () => {
  // 이벤트 목록 로드
  await loadEvents();

  // 처음 접속 시 모든 참가자 목록 로드
  await loadAllAttendees();

  // 프로젝트 변경 이벤트 리스너 등록
  window.addEventListener('project-changed', handleProjectChange);
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  window.removeEventListener('project-changed', handleProjectChange);
});

// 이벤트 변경 또는 페이지 변경 시 선택 초기화
watch([selectedEventId, currentPage], () => {
  selectedAttendeeIds.value = [];
});

// 검색어 적용 또는 정렬 변경 시 선택 초기화
watch([appliedSearchQuery, appliedSearchType], () => {
  // 검색 조건 변경 시 첫 페이지로 이동
  currentPage.value = 0;
  selectedAttendeeIds.value = [];
});
</script>

<style scoped>
.attendees-management {
  padding: 20px;
}

h1 {
  margin-bottom: 24px;
  color: #333;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 24px;
  background-color: #f0f4f8;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e0e7ef;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 250px;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #444;
  font-size: 14px;
}

.filter-group select,
.filter-group input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group select:hover,
.filter-group input:hover {
  border-color: #aaa;
}

.filter-group select {
  background-color: white;
  cursor: pointer;
}

.filter-group .button-group {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.search-btn:hover {
  background-color: #0b7dda;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.search-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(33, 150, 243, 0.4);
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.reset-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.4);
}

.reset-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(244, 67, 54, 0.4);
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.25);
}

.attendees-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.attendees-table {
  width: 100%;
  border-collapse: collapse;
}

.attendees-table th,
.attendees-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.attendees-table th {
  background-color: #f8f8f8;
  font-weight: 600;
  color: #333;
}

.attendees-table tr:hover {
  background-color: #f9f9f9;
}

.checkbox-column {
  width: 40px;
  text-align: center;
}

.checkbox-column input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f7ff;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
  border-bottom: 1px solid #d0e3ff;
}

.selected-count {
  font-weight: 500;
  color: #1976d2;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.bulk-action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bulk-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.confirm-btn {
  background-color: #e0f2f1;
  color: #00796b;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #b2dfdb;
}

.reject-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.reject-btn:hover:not(:disabled) {
  background-color: #ffcdd2;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background-color: #fff8e1;
  color: #ffa000;
}

.status-registered {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-confirmed {
  background-color: #e8f5e9;
  color: #388e3c;
}

.status-cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.status-attended {
  background-color: #e0f2f1;
  color: #00796b;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background-color: #bbdefb;
}

.attend-btn {
  background-color: #e0f2f1;
  color: #00796b;
}

.attend-btn:hover {
  background-color: #b2dfdb;
}

.not-attend-btn {
  background-color: #fff3e0;
  color: #e65100;
}

.not-attend-btn:hover {
  background-color: #ffe0b2;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

.loading-message,
.no-data-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 24px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #333;
}

.attendee-details {
  margin-top: 16px;
}

.detail-row {
  display: flex;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.detail-label {
  width: 120px;
  font-weight: 500;
  color: #555;
}

.detail-value {
  flex: 1;
}

.additional-info {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
}

.field-id {
  font-family: monospace;
  font-size: 0.9em;
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  color: #666;
  word-break: break-all;
}

.no-data-message {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 10px;
}

.delete-modal, .attendance-modal {
  max-width: 400px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-delete-btn {
  padding: 8px 16px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-delete-btn:hover {
  background-color: #d32f2f;
}

.confirm-attend-btn {
  padding: 8px 16px;
  background-color: #00796b;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-attend-btn:hover {
  background-color: #00695c;
}

.confirm-not-attend-btn {
  padding: 8px 16px;
  background-color: #e65100;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-not-attend-btn:hover {
  background-color: #d84315;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    padding: 12px;
  }

  .filter-group {
    width: 100%;
    min-width: auto;
    margin-bottom: 12px;
  }

  .filter-group select,
  .filter-group input {
    width: 100%;
  }

  .filter-group .button-group {
    flex-direction: column;
    gap: 8px;
  }

  .search-btn,
  .reset-btn {
    width: 100%;
  }

  .attendees-table th,
  .attendees-table td {
    padding: 8px;
    font-size: 13px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
    margin-bottom: 4px;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .action-buttons {
    width: 100%;
  }
}

/* 페이지당 항목 선택 UI 스타일 */
.items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.items-per-page-buttons {
  display: flex;
  margin-left: 5px;
}

.item-count-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  font-size: 0.8rem;
}

.item-count-btn.active {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}
</style>
