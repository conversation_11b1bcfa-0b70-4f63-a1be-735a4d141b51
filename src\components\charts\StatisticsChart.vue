<template>
  <div class="chart-container">
    <!-- 차트 캔버스는 항상 렌더링하되 필요에 따라 숨김 처리 -->
    <div class="chart-wrapper" :class="{ 'hidden': loading || error || !hasData }">
      <canvas ref="chartCanvas" class="chart-canvas"></canvas>
      <div v-if="hasData" class="chart-info">
        <p><small>데이터 포인트: {{ chartData.labels.length }}개</small></p>
      </div>
    </div>
    
    <!-- 로딩, 에러, 데이터 없음 상태 표시 -->
    <div v-if="loading" class="chart-overlay chart-loading">
      <div class="loading-spinner"></div>
      <p>차트 데이터를 불러오는 중입니다...</p>
    </div>
    
    <div v-else-if="error" class="chart-overlay chart-error">
      <p>{{ error }}</p>
      <button @click="$emit('retry')" class="retry-btn">다시 시도</button>
    </div>
    
    <div v-else-if="!hasData" class="chart-overlay no-data">
      <p>표시할 데이터가 없습니다. 다른 기간을 선택해 주세요.</p>
      <div class="debug-info">
        <p><small>디버그 정보: {{ JSON.stringify(chartData) }}</small></p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  chartTitle: {
    type: String,
    default: '통계 차트'
  },
  chartType: {
    type: String,
    default: 'line'
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
});

defineEmits(['retry']);

const chartCanvas = ref(null);
let chart = null;

const hasData = computed(() => {

  // 차트 데이터 객체 확인
  if (!props.chartData) {
    console.warn('chartData가 없음');
    return false;
  }

  // labels 배열 확인
  if (!props.chartData.labels || !Array.isArray(props.chartData.labels) || props.chartData.labels.length === 0) {
    console.warn('labels 배열이 없거나 비어있음:', props.chartData.labels);
    return false;
  }

  // datasets 배열 확인
  if (!props.chartData.datasets || !Array.isArray(props.chartData.datasets) || props.chartData.datasets.length === 0) {
    console.warn('datasets 배열이 없거나 비어있음:', props.chartData.datasets);
    return false;
  }

  // 모든 dataset에 data 배열이 있는지 확인
  const allDatasetsHaveData = props.chartData.datasets.every(dataset => {
    if (!dataset.data || !Array.isArray(dataset.data)) {
      console.warn('dataset에 data 배열이 없음:', dataset);
      return false;
    }
    return true;
  });

  if (!allDatasetsHaveData) {
    return false;
  }

  return true;
});

const createChart = () => {

  if (!chartCanvas.value) {
    console.error('차트 캔버스 요소가 없습니다.');
    return;
  }

  if (!hasData.value) {
    console.warn('차트 데이터가 없습니다:', props.chartData);
    return;
  }

  try {
    // 기존 차트가 있으면 파괴
    if (chart) {
      chart.destroy();
    }


    // 새 차트 생성
    const ctx = chartCanvas.value.getContext('2d');
    chart = new Chart(ctx, {
      type: props.chartType,
      data: props.chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false, // 비율 유지 안함
        layout: {
          padding: {
            top: 10,
            right: 10,
            bottom: 10,
            left: 10
          }
        },
        plugins: {
          legend: {
            position: 'top',
            labels: {
              boxWidth: 12,
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          },
          title: {
            display: true,
            text: props.chartTitle,
            font: {
              size: 16
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: '날짜'
            },
            ticks: {
              maxRotation: 45, // 레이블 회전
              minRotation: 45,
              autoSkip: true,
              maxTicksLimit: 15 // 최대 표시 레이블 수
            }
          },
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: '개수'
            }
          }
        }
      }
    });

  } catch (err) {
    console.error('차트 생성 중 오류 발생:', err);
  }
};

// chartData 또는 chartTitle이 변경될 때마다 차트 업데이트
watch([() => props.chartData, () => props.chartTitle, () => props.chartType], () => {

  // DOM이 완전히 렌더링될 때까지 기다린 후 차트 생성
  setTimeout(() => {
    if (chartCanvas.value) {
      createChart();
    } else {
      console.warn('watch 내부 setTimeout - 차트 캔버스가 여전히 없음');

      // 추가 지연 후 다시 시도
      setTimeout(() => {
        if (chartCanvas.value) {
          createChart();
        } else {
          console.error('추가 지연 후에도 차트 캔버스가 없음');
        }
      }, 500);
    }
  }, 100);
}, { deep: true });

// 컴포넌트가 마운트될 때 차트 생성
onMounted(() => {

  // DOM이 완전히 렌더링될 때까지 기다리는 재귀 함수
  const tryCreateChart = (attempts = 0, maxAttempts = 5) => {
    if (attempts >= maxAttempts) {
      console.error(`최대 시도 횟수(${maxAttempts})를 초과했습니다. 차트 생성 실패.`);
      return;
    }

    if (chartCanvas.value && hasData.value) {
      createChart();
    } else {
      // 지연 후 다시 시도
      setTimeout(() => {
        tryCreateChart(attempts + 1, maxAttempts);
      }, 200 * (attempts + 1)); // 점점 더 긴 지연 시간
    }
  };

  // 첫 번째 시도 시작
  setTimeout(() => {
    tryCreateChart();
  }, 100);
});
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 400px; /* 고정 높이 설정 */
  width: 100%;
  margin-top: 20px;
  overflow: hidden; /* 내용이 넘치는 경우 숨김 처리 */
}

.chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.hidden {
  visibility: hidden;
}

.chart-error {
  color: #d32f2f;
}

.no-data {
  color: #666;
  font-style: italic;
}

.debug-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #999;
  max-height: 100px;
  overflow: auto;
}

.chart-wrapper {
  position: relative;
  height: 350px; /* 차트 래퍼 높이 설정 */
  width: 100%;
}

.chart-canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

.chart-info {
  margin-top: 10px;
  text-align: right;
  color: #999;
  font-size: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.retry-btn:hover {
  background-color: #b71c1c;
}
</style>
