<template>
  <div class="statistics-view">
    <div v-if="!canViewStatistics" class="project-select-message">
      <div class="message-container">
        <p>프로젝트를 선택해주세요.</p>
      </div>
    </div>
    
    <div v-else>
      <div class="filters">
        <div class="filter-group">
          <label for="startDate">시작 날짜:</label>
          <input
            id="startDate"
            type="date"
            v-model="startDate"
            class="date-input"
            @change="validateDates"
          />
          
          <label for="endDate">종료 날짜:</label>
          <input
            id="endDate"
            type="date"
            v-model="endDate"
            class="date-input"
            :min="startDate"
            @change="validateDates"
          />
          
          <button @click="searchStatistics" class="search-btn" :disabled="!isValidDateRange">검색</button>
        </div>
        <div v-if="dateError" class="date-error-message">
          {{ dateError }}
        </div>
      </div>

    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>통계 데이터를 불러오는 중입니다...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="loadUsageStatistics" class="retry-button">다시 시도</button>
    </div>

    <div v-if="!isLoading && !error && isChartLoading" class="loading-container chart-loading">
      <div class="loading-spinner"></div>
      <p>차트를 생성하는 중입니다...</p>
      <div class="loading-progress">잠시만 기다려주세요</div>
    </div>

    <div v-if="!isLoading && !error && usageStats" class="statistics-container">
      <!-- 요약 정보 -->
      <div class="stat-card">
        <h2>요약 정보</h2>
        <div v-if="usageStats" class="summary-grid">
          <div class="summary-item"><strong>기간:</strong> {{ usageStats.periodStartDate }} ~ {{ usageStats.periodEndDate }}</div>
          <div class="summary-item"><strong>총 요청 수:</strong> {{ usageStats.totalRequests?.toLocaleString() }}</div>
          <div class="summary-item"><strong>관리자 요청 수:</strong> {{ usageStats.adminRequests?.toLocaleString() }}</div>
          <div class="summary-item"><strong>일반/익명 사용자 요청 수:</strong> {{ usageStats.generalUserRequests?.toLocaleString() }}</div>
          <div class="summary-item"><strong>고유 관리자 수:</strong> {{ usageStats.uniqueAdminUsers?.toLocaleString() }}</div>
          <div class="summary-item"><strong>고유 익명 세션 수:</strong> {{ usageStats.uniqueAnonymousSessions?.toLocaleString() }}</div>
        </div>
        <div v-else-if="!isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">ℹ️</div>
          <div class="no-data-text">요약 정보가 없습니다</div>
        </div>
      </div>

      <!-- 시간대별 요청 수 -->
      <div class="stat-card">
        <h2>시간대별 요청 수</h2>
        <div class="chart-container" v-show="usageStats?.hourlyUsage?.length && !isChartLoading">
          <div class="chart-wrapper" id="hourlyUsageChartContainer">
            <canvas ref="hourlyUsageChartCanvas"></canvas>
          </div>
        </div>
        <div v-if="isChartLoading && usageStats?.hourlyUsage?.length" class="chart-placeholder">
          <div class="mini-spinner"></div>
          <span>차트를 생성하는 중입니다...</span>
        </div>
        <div v-if="(!usageStats?.hourlyUsage?.length) && !isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">📊</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">시간대별 요청 수 데이터가 없습니다.</div>
        </div>
      </div>

      <!-- 상위 요청 URL -->
      <div class="stat-card">
        <h2>상위 요청 URL (Top {{ usageStats?.topUrls?.length || 0 }})</h2>
        <div class="chart-container" v-show="usageStats?.topUrls?.length && !isChartLoading">
          <div class="chart-wrapper" id="topUrlsChartContainer">
            <canvas ref="topUrlsChartCanvas"></canvas>
          </div>
        </div>
        <div v-if="isChartLoading && usageStats?.topUrls?.length" class="chart-placeholder">
          <div class="mini-spinner"></div>
          <span>차트를 생성하는 중입니다...</span>
        </div>
        <div v-if="(!usageStats?.topUrls?.length) && !isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">🔗</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">상위 요청 URL 데이터가 없습니다.</div>
        </div>
      </div>

      <!-- 상위 사용자 활동 -->
      <div class="stat-card">
        <h2>상위 사용자 활동 (Top {{ usageStats?.topUserActivity?.length || 0 }})</h2>
        <div v-if="usageStats?.topUserActivity?.length" class="data-table">
          <table>
            <thead><tr><th>사용자 식별자</th><th>요청 수</th></tr></thead>
            <tbody>
              <tr v-for="item in usageStats.topUserActivity" :key="item.userIdentifier">
                <td>{{ item.userIdentifier }}</td>
                <td>{{ item.requestCount?.toLocaleString() }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else-if="!isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">👥</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">상위 사용자 활동 데이터가 없습니다.</div>
        </div>
      </div>

      <!-- 상위 리퍼러 -->
      <div class="stat-card">
        <h2>상위 접속 URL (Top {{ usageStats?.topReferers?.length || 0 }})</h2>
        <div v-if="usageStats?.topReferers?.length" class="data-table">
          <table>
            <thead><tr><th>URL</th><th>요청 수</th></tr></thead>
            <tbody>
              <tr v-for="item in usageStats.topReferers" :key="item.referer">
                <td>{{ item.referer || '(Direct)' }}</td>
                <td>{{ item.count?.toLocaleString() }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else-if="!isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">↪️</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">상위 리퍼러 데이터가 없습니다.</div>
        </div>
      </div>

      <!-- 상위 사용자 에이전트 -->
      <div class="stat-card">
        <h2>상위 사용자 도구 (Top {{ usageStats?.topUserAgents?.length || 0 }})</h2>
        <div v-if="usageStats?.topUserAgents?.length" class="data-table">
          <table>
            <thead><tr><th>사용자 도구</th><th>요청 수</th></tr></thead>
            <tbody>
              <tr v-for="item in usageStats.topUserAgents" :key="item.userAgent">
                <td>{{ item.userAgent }}</td>
                <td>{{ item.count?.toLocaleString() }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else-if="!isLoading && !isChartLoading" class="no-data">
          <div class="no-data-icon">💻</div>
          <div class="no-data-text">데이터가 없습니다</div>
          <div class="no-data-subtext">상위 사용자 에이전트 데이터가 없습니다.</div>
        </div>
      </div>

    </div>
    <div v-else-if="!isLoading && !error && !usageStats" class="no-data full-page-no-data">
        <div class="no-data-icon">🤷</div>
        <div class="no-data-text">통계 데이터를 표시할 수 없습니다.</div>
        <div class="no-data-subtext">프로젝트를 선택했는지 확인하거나, 다시 시도해주세요.</div>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch, onBeforeUnmount } from 'vue';
import { ElMessage, ElLoading, ElButton } from 'element-plus';
import Chart from 'chart.js/auto';
import { useAuthStore } from '@/stores/auth'; // 수정된 임포트
import { getUsageStatistics } from '@/api/statistics';

const authStore = useAuthStore(); // 수정된 스토어 사용
const currentProjectId = computed(() => authStore.currentProject?.projectId); // 수정된 computed

// SUPER_ADMIN이거나 현재 프로젝트가 선택되어 있으면 통계를 볼 수 있음
const canViewStatistics = computed(() => {
  return authStore.isSuperAdmin || authStore.currentProject;
});

const isLoading = ref(true);
const isChartLoading = ref(false);
const error = ref(null);
const usageStats = ref(null);

// formatDate 함수 정의를 위로 이동
const formatDate = (date) => {
  if (!date) return null;
  const d = new Date(date);
  const year = d.getFullYear();
  const month = (`0${d.getMonth() + 1}`).slice(-2);
  const day = (`0${d.getDate()}`).slice(-2);
  return `${year}-${month}-${day}`;
};

// 날짜 관련 상태
const startDate = ref('');
const endDate = ref('');
const dateError = ref('');
const isValidDateRange = ref(true);

const hourlyUsageChartCanvas = ref(null);
const topUrlsChartCanvas = ref(null);
const statusFamilyChartCanvas = ref(null);

const charts = ref({});

// 날짜를 비교가능한 형식으로 변환 (시간 정보 제거)
const getComparableDate = (dateStr) => {
  const date = new Date(dateStr);
  date.setHours(0, 0, 0, 0);
  return date;
};

// 날짜 유효성 검사 함수 - 미래 날짜 제한 제거
const validateDates = () => {
  dateError.value = '';
  isValidDateRange.value = true;

  if (!startDate.value || !endDate.value) {
    dateError.value = '시작일과 종료일을 모두 입력해주세요.';
    isValidDateRange.value = false;
    return;
  }

  const start = getComparableDate(startDate.value);
  const end = getComparableDate(endDate.value);
  
  if (isNaN(start.getTime())) {
    dateError.value = '유효한 시작 날짜를 입력해주세요.';
    isValidDateRange.value = false;
    return;
  }
  
  if (isNaN(end.getTime())) {
    dateError.value = '유효한 종료 날짜를 입력해주세요.';
    isValidDateRange.value = false;
    return;
  }
  
  if (start > end) {
    dateError.value = '시작일은 종료일보다 이전이어야 합니다.';
    isValidDateRange.value = false;
    return;
  }

  // 미래 날짜 제한 검사 제거함
  
  // 모든 검증 통과
  isValidDateRange.value = true;
};

// 검색 버튼 클릭 시 유효성 검사 후 데이터 로드
const searchStatistics = () => {
  validateDates();
  if (isValidDateRange.value) {
    loadUsageStatistics();
  }
};

const loadUsageStatistics = async () => {
  // 유효성 검사 한 번 더 확인
  if (!isValidDateRange.value) {
    return;
  }

  isLoading.value = true;
  isChartLoading.value = true;
  error.value = null;
  usageStats.value = null;

  try {
    const serverResponse = await getUsageStatistics(startDate.value, endDate.value);
    if (serverResponse && serverResponse.success && serverResponse.data) {
      usageStats.value = serverResponse.data;
      error.value = null;

      if (Object.keys(usageStats.value).length === 0 || 
          (!usageStats.value.hourlyUsage?.length && !usageStats.value.topUrls?.length)) {
        isChartLoading.value = false;
      } else {
        isChartLoading.value = false;
        nextTick(renderCharts);
      }
    } else {
      console.error('Failed to fetch statistics:', serverResponse?.message || 'Invalid response structure');
      error.value = serverResponse?.message || '통계 데이터를 가져오는데 실패했습니다. 서버 응답을 확인해주세요.';
      isChartLoading.value = false;
    }
  } catch (err) {
    console.error('Error loading usage statistics:', err);
    error.value = err.message || '통계 데이터 로딩 중 오류가 발생했습니다.';
    isChartLoading.value = false;
  } finally {
    isLoading.value = false;
  }
};

const renderCharts = async () => {
  // 기존 차트 파괴
  Object.values(charts.value).forEach(chart => {
    if (chart && typeof chart.destroy === 'function') {
      chart.destroy();
    }
  });
  charts.value = {}; // 차트 참조 초기화

  if (!usageStats.value || Object.keys(usageStats.value).length === 0 || 
      (!usageStats.value.hourlyUsage?.length && !usageStats.value.topUrls?.length)) {
    // isChartLoading.value = false; // 더 이상 여기서 관리 안함
    return;
  }

  // DOM 업데이트를 기다리기 위해 nextTick 사용 (v-show 변경 후 DOM 안정화)
  await nextTick(); // await 추가

  const chartColors = ['#4BC0C0', '#FF6384', '#36A2EB', '#FFCE56', '#9966FF', '#FF9F40', '#FFCD56', '#C9CBCF'];
  const chartBackgroundColors = [
    'rgba(75, 192, 192, 0.6)', 'rgba(255, 99, 132, 0.6)', 'rgba(54, 162, 235, 0.6)',
    'rgba(255, 206, 86, 0.6)', 'rgba(153, 102, 255, 0.6)', 'rgba(255, 159, 64, 0.6)',
    'rgba(255, 205, 86, 0.6)', 'rgba(201, 203, 207, 0.6)'
  ];

  try {
    if (usageStats.value.hourlyUsage && usageStats.value.hourlyUsage.length > 0 && hourlyUsageChartCanvas.value) {
      const ctx = hourlyUsageChartCanvas.value.getContext('2d');
      if (ctx) {
        charts.value.hourlyUsage = new Chart(ctx, {
          type: 'line',
          data: {
            labels: usageStats.value.hourlyUsage.map(item => `${String(item.hour).padStart(2, '0')}:00`),
            datasets: [{
              label: '시간대별 요청 수',
              data: usageStats.value.hourlyUsage.map(item => item.requestCount),
              borderColor: chartColors[0],
              backgroundColor: chartBackgroundColors[0],
              tension: 0.1,
              fill: true
            }]
          },
          options: {
            animation: false, // 애니메이션 비활성화
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: true, position: 'top' }, tooltip: { mode: 'index', intersect: false } },
            scales: { y: { beginAtZero: true, title: { display: true, text: '요청 수' } }, x: { title: { display: true, text: '시간' } } }
          }
        });
      }
    } 

    if (usageStats.value.topUrls && usageStats.value.topUrls.length > 0 && topUrlsChartCanvas.value) {
      const ctx = topUrlsChartCanvas.value.getContext('2d');
      if (ctx) {
        charts.value.topUrls = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: usageStats.value.topUrls.map(item => item.url.length > 30 ? item.url.substring(0, 27) + '...' : item.url),
            datasets: [{
              label: '요청 수',
              data: usageStats.value.topUrls.map(item => item.requestCount),
              backgroundColor: chartBackgroundColors.slice(0, usageStats.value.topUrls.length),
              borderColor: chartColors.slice(0, usageStats.value.topUrls.length),
              borderWidth: 1
            }]
          },
          options: {
            animation: false, // 애니메이션 비활성화
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false }, tooltip: { mode: 'index', intersect: false } },
            scales: { x: { beginAtZero: true, title: { display: true, text: '요청 수' } }, y: { ticks: { autoSkip: false } } }
          }
        });
      }
    } 
  } catch (e) {
    console.error('차트 렌더링 중 오류:', e);
    error.value = '차트 렌더링 중 오류가 발생했습니다.';
  }
};

onMounted(() => {
  const endDateValue = new Date();
  const startDateValue = new Date();
  startDateValue.setDate(endDateValue.getDate() - 6);
  
  startDate.value = formatDate(startDateValue);
  endDate.value = formatDate(endDateValue);
  
  validateDates();
  
  // SUPER_ADMIN이거나 현재 프로젝트가 선택되어 있을 때만 통계 로드
  if (canViewStatistics.value) {
    loadUsageStatistics();
  } else {
    isLoading.value = false;
  }
});

// 프로젝트 변경 시 데이터 다시 로드
watch(() => authStore.currentProject, () => {
  if (canViewStatistics.value && isValidDateRange.value) {
    loadUsageStatistics();
  } else if (!canViewStatistics.value) {
    isLoading.value = false;
  }
}, { deep: true });

watch(currentProjectId, (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId) {
    loadUsageStatistics();
  } else if (!newProjectId) {
    // 프로젝트 선택이 해제되면 전역/기본 통계를 로드하도록 시도
    loadUsageStatistics();
  }
});

onBeforeUnmount(() => {
  Object.values(charts.value).forEach(chart => {
    if (chart && typeof chart.destroy === 'function') {
      chart.destroy();
    }
  });
  charts.value = {}; // 참조 제거
});

</script>

<style scoped>
.project-select-message {
  margin-top: 30px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.message-container {
  padding: 40px 20px;
}

.message-container p {
  font-size: 18px;
  color: #666;
  margin: 0;
}

.statistics-view {
  padding: 2rem;
  background-color: #f4f6f8;
  min-height: 100vh;
}

/* 필터 스타일 업데이트 */
.filters {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  gap: 10px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.date-input:invalid {
  border-color: #ff4d4f;
  background-color: #fff1f0;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover:not(:disabled) {
  background-color: #0b7dda;
}

.search-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.date-error-message {
  color: #ff4d4f;
  font-size: 14px;
  margin-top: 5px;
  padding: 5px 0;
}

.loading-container,
.error-container,
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.loading-container.chart-loading {
  margin-top: 1rem;
}

.full-page-no-data {
    min-height: 300px; /* Ensure it takes some space */
}

.loading-spinner,
.mini-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.mini-spinner {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

.loading-progress {
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.5rem;
}

.error-message {
  color: #dc3545;
  margin-bottom: 1rem;
  font-weight: bold;
}

.retry-button {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #0056b3;
}

.statistics-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background-color: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.stat-card h2 {
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
    font-size: 0.95rem;
}

.summary-item {
    background-color: #f9f9f9;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    border: 1px solid #e9e9e9;
}
.summary-item strong {
    color: #0056b3;
}

.chart-container {
  position: relative;
  /* min-height: 300px; */ /* Adjust as needed, or set specific height for chart-wrapper */
  flex-grow: 1; /* Allows chart container to fill space if card is flex column */
  display: flex;
  flex-direction: column; /* Ensure chart wrapper takes space */
}

.chart-wrapper {
  position: relative;
  width: 100%;
  min-height: 300px; /* Ensure canvas has drawing space */
  max-height: 450px; /* Optional: prevent very tall charts */
  margin-bottom: 1rem;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #666;
}

.no-data {
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #777;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
  flex-grow: 1; /* Takes available space in stat-card */
}

.no-data-icon {
  font-size: 2.5rem; /* 아이콘의 기본 크기 (이모지/폰트 아이콘용) */
  width: 2.5rem;     /* 컨테이너 너비를 font-size와 일치시켜 정사각형 형태 유지 */
  height: 2.5rem;    /* 컨테이너 높이를 font-size와 일치 */
  line-height: 1;    /* 텍스트 아이콘의 수직 정렬 및 크기 제어에 중요 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  opacity: 0.7;
  overflow: hidden;    /* 내용이 컨테이너를 초과할 경우 숨김 */
}

/* .no-data-icon 내부에 직접 이미지나 SVG 태그가 사용될 경우를 위한 스타일 */
.no-data-icon > img,
.no-data-icon > svg {
  display: block; /* 크기 조정을 위해 블록 요소로 처리 */
  max-width: 100%;
  max-height: 100%;
  width: auto; /* 종횡비 유지 */
  height: auto; /* 종횡비 유지 */
  object-fit: contain; /* 컨테이너 내부에 맞게 이미지 비율 유지하며 채움 */
}

.no-data-text {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.no-data-subtext {
  font-size: 0.9rem;
  color: #999;
}

.data-table {
  width: 100%;
  margin-top: 1rem;
  overflow-x: auto; /* For responsiveness */
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th,
.data-table td {
  padding: 0.6rem 0.8rem;
  text-align: left;
  border-bottom: 1px solid #e9e9e9;
}

.data-table th {
  background-color: #f7f9fc;
  font-weight: 600;
  color: #333;
}

.data-table tbody tr:nth-child(even) {
  background-color: #fcfdff;
}

.data-table tbody tr:hover {
  background-color: #f0f4f8;
}

.no-data-cell {
  text-align: center;
  color: #888;
  padding: 1rem;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .statistics-container {
    grid-template-columns: 1fr; /* Stack cards on smaller screens */
  }
  .stat-card h2 {
    font-size: 1.2rem;
  }
  .summary-grid {
    grid-template-columns: 1fr;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
