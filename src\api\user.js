import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 사용자 목록을 가져옵니다.
 * @param {number} projectId - 프로젝트 ID (선택적)
 * @param {object} paginationParams - 페이지네이션 파라미터 (선택적)
 * @returns {Promise<Object>} 사용자 목록을 포함한 응답 객체 Promise
 */
export const getUsers = async (projectId = null, paginationParams = {}) => {
  try {
    // 기본 파라미터 설정
    const params = {
      page: 0,
      size: 100,
      sort: 'userEmail,asc',
      ...paginationParams
    };

    // projectId가 있으면 쿼리 파라미터로 추가
    if (projectId) {
      params.projectId = projectId;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    // 새로운 서버 응답 구조: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    const response = await executeApiCall(
      () => apiClient.get('/users/list', { params }),
      '사용자 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사용자 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새 사용자를 생성합니다.
 * @param {object} userData - 사용자 데이터 ({ userEmail, name, password, roleId, description })
 * @returns {Promise<object>} 성공 시 생성된 사용자 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const createUser = async (userData) => {
  try {
    const response = await apiClient.post('/users/create', userData);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data; // 성공 응답 전체 또는 필요한 부분 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || '사용자 생성에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사용자 생성 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 특정 사용자 정보를 이메일로 조회합니다.
 * @param {string} userEmail - 조회할 사용자의 이메일
 * @returns {Promise<object>} 사용자 정보 Promise
 */
export const getUserByEmail = async (userEmail) => {
  try {
    const response = await apiClient.get(`/users/${userEmail}`);
    if (response.data && response.data.success && response.data.data) {
      return response.data.data; // 사용자 객체 반환
    } else {
      throw new Error(response.data?.error?.message || '사용자 정보를 가져오는 데 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사용자 정보 조회 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 사용자 정보를 업데이트합니다.
 * @param {object} userData - 업데이트할 사용자 데이터 ({ userIdx, userEmail, name, roleId, description, status 등 })
 * @returns {Promise<object>} 성공 시 업데이트된 사용자 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const updateUser = async (userData) => {
  try {
    // userEmail을 경로 파라미터로 사용하고, 나머지 데이터는 요청 본문에 포함
    const { userEmail, ...updateData } = userData;

    // 서버 요청 형식에 맞게 데이터 구성
    const requestData = {
      name: updateData.name,
      roleId: updateData.roleId,
      description: updateData.description || '',
      status: updateData.status || 'ACTIVE' // 기본값 설정
    };

    // projectId가 있으면 요청 데이터에 추가
    if (updateData.projectId) {
      requestData.projectId = updateData.projectId;
    }

    // PUT 요청으로 /api/way/users/modify/{userEmail} 엔드포인트 호출
    const response = await apiClient.put(`/users/modify/${userEmail}`, requestData);

    if (response.data && response.data.success) {
      return response.data; // 성공 응답 반환
    } else {
      throw new Error(response.data?.message || response.data?.error?.message || '사용자 업데이트에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사용자 업데이트 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 사용자를 삭제합니다.
 * @param {string} userEmail - 삭제할 사용자의 이메일
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const deleteUser = async (userEmail) => {
  try {
    // DELETE 요청으로 /api/way/users/{userEmail} 엔드포인트 호출
    const response = await apiClient.delete(`/users/${userEmail}`);

    if (response.data && response.data.success) {
      return true; // 성공 시 true 반환
    } else {
      throw new Error(response.data?.message || response.data?.error?.message || '사용자 삭제에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사용자 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 사용자의 비밀번호를 변경합니다.
 * @param {string} userEmail - 비밀번호를 변경할 사용자의 이메일
 * @param {string} newPassword - 새 비밀번호
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const changePassword = async (userEmail, newPassword) => {
  try {
    // PUT 요청으로 /api/way/users/{userEmail}/password 엔드포인트 호출
    // 'newPassword' 키로 전송
    const response = await apiClient.put(`/users/${userEmail}/password`, { newPassword });

    if (response.data && response.data.success) {
      return true; // 성공 시 true 반환
    } else {
      throw new Error(response.data?.message || response.data?.error?.message || '비밀번호 변경에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '비밀번호 변경 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트 관리자 권한을 가진 사용자 목록을 가져옵니다.
 * @returns {Promise<Array>} 프로젝트 관리자 목록 Promise
 */
export const getProjectAdmins = async () => {
  try {
    const params = {
      onlyProjectSearch: true,
    };
    // 전체 사용자 목록 가져오기
    const response = await getUsers(null, params);
    let users = [];

    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.success && response.data) {
      if (response.data.content && Array.isArray(response.data.content)) {
        users = response.data.content;
      } else if (Array.isArray(response.data)) {
        users = response.data;
      }
    }

    // PROJECT_ADMIN 권한을 가진 사용자만 필터링
    return users.filter(user => user.roleId === 'PROJECT_ADMIN' && user.status === 'ACTIVE');
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '프로젝트 관리자 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 할당 가능한 프로젝트 관리자 목록을 가져옵니다.
 * 이미 다른 프로젝트에 할당된 관리자는 제외합니다.
 * @returns {Promise<Array>} 할당 가능한 프로젝트 관리자 목록 Promise
 */
export const getAvailableProjectAdmins = async () => {
  try {
    // 할당 가능한 프로젝트 관리자 목록 API 호출
    const response = await executeApiCall(
      () => apiClient.get('/users/available-project-admins'),
      '할당 가능한 프로젝트 관리자 목록을 가져오는 데 실패했습니다.'
    );
    // 서버 응답에서 data 부분 추출
    return response.data || [];
  } catch (error) {
    console.error('할당 가능한 프로젝트 관리자 목록 오류:', error);
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '할당 가능한 프로젝트 관리자 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 프로젝트 수정 시 할당 가능한 프로젝트 관리자 목록을 가져옵니다.
 * 현재 프로젝트에 할당된 관리자와 다른 프로젝트에 할당되지 않은 관리자를 반환합니다.
 * @param {number} projectId - 프로젝트 ID
 * @returns {Promise<Array>} 할당 가능한 프로젝트 관리자 목록 Promise
 */
export const getAvailableProjectAdminsForEdit = async (projectId) => {
  try {
    // 프로젝트 수정 시 할당 가능한 프로젝트 관리자 목록 API 호출
    const response = await executeApiCall(
      () => apiClient.get(`/users/available-project-admins/${projectId}`),
      '할당 가능한 프로젝트 관리자 목록을 가져오는 데 실패했습니다.'
    );
    // 서버 응답에서 data 부분 추출
    return response.data || [];
  } catch (error) {
    console.error(`프로젝트 수정용 관리자 목록 오류:`, error);
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '할당 가능한 프로젝트 관리자 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};