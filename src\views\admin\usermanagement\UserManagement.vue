<template>
  <div class="user-management">
    <h1>관리자 관리</h1>

    <!-- 프로젝트가 없고 SUPER_ADMIN도 아닌 경우 -->
    <div v-if="!shouldShowUserList" class="no-projects-message">
      <p>할당된 프로젝트가 없습니다.</p>
    </div>

    <!-- SUPER_ADMIN이거나 프로젝트가 있는 경우에만 관리자 목록 표시 -->
    <div v-else class="user-list-container">
      <div class="filters">
        <div class="filter-group">
          <button @click="navigateToCreateUser" class="create-btn">새 관리자 추가</button>
          
          <!-- 페이지당 항목 수 선택 UI -->
          <div class="items-per-page-selector">
            <span>페이지당 항목:</span>
            <div class="items-per-page-buttons">
              <button 
                @click="changeItemsPerPage(10)" 
                :class="['item-count-btn', itemsPerPage === 10 ? 'active' : '']">
                10개
              </button>
              <button 
                @click="changeItemsPerPage(30)" 
                :class="['item-count-btn', itemsPerPage === 30 ? 'active' : '']">
                30개
              </button>
              <button 
                @click="changeItemsPerPage(50)" 
                :class="['item-count-btn', itemsPerPage === 50 ? 'active' : '']">
                50개
              </button>
            </div>
          </div>
        </div>
        <div class="filter-group">
          <label for="searchTypeSelect">검색 유형:</label>
          <select id="searchTypeSelect" v-model="searchType">
            <option v-for="type in availableSearchTypes.length > 0 ? availableSearchTypes : searchTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </option>
          </select>

          <label for="searchInput">검색어:</label>
          <input
            id="searchInput"
            type="text"
            v-model="searchQuery"
            placeholder="검색어 입력"
            @input="handleSearch"
            @keyup.enter="searchUsers"
          />

          <button @click="searchUsers" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>

      <div class="user-table-container">
        <table v-if="filteredUsers.length > 0" class="user-table">
          <thead>
            <tr>
              <th>번호</th>
              <th @click="sortBy('userEmail')">이메일(ID) <span v-if="sortKey === 'userEmail'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('name')">이름 <span v-if="sortKey === 'name'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('roleId')">역할 <span v-if="sortKey === 'roleId'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('status')">상태 <span v-if="sortKey === 'status'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('createDate')">생성일 <span v-if="sortKey === 'createDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>작업</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(user, index) in filteredUsers" :key="user.userEmail" :class="{ 'current-user-row': user.userEmail === currentUserEmail }">
              <td>{{ calculateIndex(index) }}</td>
              <td>{{ user.userEmail }}{{ user.userEmail === currentUserEmail ? ' (나)' : '' }}</td>
              <td>{{ user.name }}</td>
              <td>{{ formatRole(user.roleId) }}</td>
              <td>{{ formatStatus(user.status) }}</td>
              <td>{{ formatDate(user.createDate) }}</td>
              <td class="actions">
                <button @click="editUser(user.userEmail)" class="action-btn edit-btn">수정</button>
                <button
                  @click="confirmDeleteUser(user)"
                  class="action-btn delete-btn"
                  :disabled="user.roleId === 'SUPER_ADMIN' || user.userEmail === currentUserEmail"
                >
                  삭제
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-else-if="isLoading" class="loading-message">
          <div class="loading-spinner"></div>
          <p>관리자 정보를 불러오는 중입니다...</p>
        </div>

        <div v-else-if="error" class="error-message">
          <p>{{ error }}</p>
        </div>

        <div v-else class="no-data-message">
          <p>관리자 데이터가 없습니다.</p>
        </div>
      </div>

      <!-- 페이지네이션 -->
      <div class="pagination" v-if="users.length > 0">
        <button
          @click="goToPage(0)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &laquo;
        </button>
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &lt;
        </button>

        <span class="page-info">{{ displayPage }} / {{ totalPages || 1 }}</span>

        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &gt;
        </button>
        <button
          @click="goToPage(totalPages - 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &raquo;
        </button>
      </div>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay">
      <div class="modal-content">
        <h3>관리자 삭제 확인</h3>
        <p><strong>{{ selectedUser?.name }}</strong> ({{ selectedUser?.userEmail }}) 관리자를 삭제하시겠습니까?</p>
        <p class="warning">이 작업은 되돌릴 수 없습니다.</p>
        <div class="modal-actions">
          <button @click="handleDeleteUser" class="confirm-btn" :disabled="isDeleting">
            {{ isDeleting ? '삭제 중...' : '삭제' }}
          </button>
          <button @click="cancelDelete" class="cancel-btn">취소</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getUsers, deleteUser } from '@/api/user';
import { handleApiError, extractErrorMessage } from '@/utils/errorHandler';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const users = ref([]);
const isLoading = ref(true);
const error = ref(null);
const searchQuery = ref('');
const searchType = ref('userEmail'); // 기본 검색 타입 설정
const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록

// 기본 검색 유형 목록 (서버에서 제공하지 않을 경우 사용)
const searchTypes = [
  { value: 'userEmail', label: '이메일' },
  { value: 'name', label: '이름' },
  { value: 'roleId', label: '역할' },
  { value: 'status', label: '상태' },
  { value: 'createDate', label: '생성일' }
];
const showDeleteModal = ref(false);
const selectedUser = ref(null);
const isDeleting = ref(false);

// 개발 환경 여부 확인 (디버깅 정보 표시용)
const isDev = ref(process.env.NODE_ENV === 'development');

// 페이지네이션 관련 상태
const currentPage = ref(0); // 서버 페이지네이션은 0부터 시작
const itemsPerPage = ref(10); // 페이지당 항목 수
const totalElements = ref(0); // 전체 항목 수
const totalPages = ref(1); // 전체 페이지 수

// 페이지 번호 표시용 (1부터 시작하는 페이지 번호)
const displayPage = computed(() => {
  return currentPage.value + 1;
});

// 인덱스 계산 함수
const calculateIndex = (index) => {
  // 마지막 번호부터 시작하도록 변경
  return totalElements.value - (currentPage.value * itemsPerPage.value + index);
};

// 페이지당 항목 수 변경 함수
const changeItemsPerPage = (count) => {
  itemsPerPage.value = count;
  currentPage.value = 0; // 첫 페이지로 이동
  fetchUsers(); // 사용자 목록 다시 로드
};

// 정렬 관련 상태
const sortKey = ref('createDate');
const sortOrder = ref('desc');

// 현재 로그인한 관리자의 이메일
const currentUserEmail = computed(() => authStore.user?.userEmail || '');

// 현재 로그인한 관리자의 역할
const currentUserRole = computed(() => authStore.user?.roleId || '');

// 관리자가 SUPER_ADMIN인지 확인
const isSuperAdmin = computed(() => currentUserRole.value === 'SUPER_ADMIN');

// 관리자 목록을 보여주어야 하는지 확인
// SUPER_ADMIN이거나 프로젝트가 있는 경우 목록 표시
const shouldShowUserList = computed(() => {
  return isSuperAdmin.value || authStore.userProjects.length > 0;
});

// 정렬 함수
const sortBy = (key) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
  // 정렬 변경 시 서버에서 데이터를 다시 가져오도록 fetchUsers 호출
  fetchUsers();
};

// 검색 결과 필터링 -> 수정 (서버에서 필터링된 데이터를 직접 사용)
const filteredUsers = computed(() => {
  // 서버에서 검색 및 정렬을 모두 처리하므로 users.value를 그대로 반환합니다.
  return users.value;
});

// 관리자 목록 불러오기
const fetchUsers = async () => {
  // 관리자 목록을 보여주지 않는 경우 실행하지 않음
  if (!shouldShowUserList.value) {
    return;
  }

  isLoading.value = true;
  error.value = null;

  try {
    let currentProjectId = null;

    // SUPER_ADMIN이 아닌 경우에만 프로젝트 ID 전달
    if (!isSuperAdmin.value) {
      currentProjectId = authStore.currentProject?.projectId || null;
    }

    // 페이지네이션 파라미터 추가
    const params = {
      page: currentPage.value,
      size: itemsPerPage.value,
      sort: `${sortKey.value},${sortOrder.value}`,
      searchType: searchType.value,
      searchKeyword: searchQuery.value,
    };

    // 프로젝트 ID가 있는 경우 추가
    if (currentProjectId) {
      params.projectId = currentProjectId;
    }

    // 날짜 검색 유형인 경우, 날짜 형식을 'YYYY-MM-DD'로 변환
    if (searchQuery.value && isDateSearchType(searchType.value)) {
      params.searchKeyword = formatDateForSearch(searchQuery.value);
    }

    const response = await getUsers(currentProjectId, params);

    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.success && response.data) {
      const responseData = response.data;

      // 페이지네이션 정보 업데이트
      if (responseData.totalPages !== undefined) {
        totalPages.value = responseData.totalPages;
      } else {
        // 서버에서 totalPages가 없는 경우 계산
        const total = responseData.totalElements || (responseData.content?.length || 0);
        totalPages.value = Math.ceil(total / itemsPerPage) || 1;
      }

      totalElements.value = responseData.totalElements || 0;

      // 검색 유형 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        availableSearchTypes.value = responseData.availableSearchTypes;

        // 검색 유형이 없는 경우 기본값 설정
        if (availableSearchTypes.value.length > 0 && !searchType.value) {
          searchType.value = availableSearchTypes.value[0].value;
        }
      }

      // 관리자 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        users.value = responseData.content;

        // 데이터가 있지만 totalPages가 0인 경우 최소 1로 설정
        if (users.value.length > 0 && totalPages.value === 0) {
          totalPages.value = 1;
        }
      } else if (Array.isArray(responseData)) {
        // 이전 응답 구조 처리 (호환성 유지)
        users.value = responseData;
        totalPages.value = 1;
        totalElements.value = responseData.length;
      } else {
        console.warn('관리자 데이터가 없거나 배열이 아닙니다.');
        users.value = [];
      }
    } else {
      // 기존 응답 구조 처리 (호환성 유지)
      users.value = response.data || [];
      totalPages.value = 1;
      totalElements.value = users.value.length;
    }

    // 현재 페이지가 유효한 범위를 벗어났는지 확인
    if (currentPage.value > 0 && totalPages.value > 0 && currentPage.value >= totalPages.value) {
      // 마지막 페이지로 재설정
      currentPage.value = totalPages.value - 1;
    }
  } catch (err) {
    error.value = handleApiError(err, '관리자 목록을 불러오는 중 오류가 발생했습니다.');
    console.error('관리자 목록 조회 실패:', err);
    users.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 페이지 이동 함수
const goToPage = (page) => {
  // 페이지 범위 검사 (totalPages가 0이면 최소 1페이지로 간주)
  const maxPage = Math.max(totalPages.value - 1, 0);
  if (page < 0 || page > maxPage) {
    console.warn(`페이지 범위 초과: ${page}, 최대 페이지: ${maxPage}`);
    return;
  }

  // 페이지 변경 및 데이터 로드
  currentPage.value = page;
  fetchUsers();

  // 상단으로 스크롤
  window.scrollTo(0, 0);
};

// 날짜 관련 검색 타입인지 확인하는 함수
const isDateSearchType = (type) => {
  // 날짜 관련 필드명 목록
  const dateFields = ['createDate', 'updateDate', 'lastLoginDate'];
  return dateFields.includes(type);
};

// 날짜 형식 변환 함수 (2025. 05. 17. -> 2025-05-17)
const formatDateForSearch = (dateString) => {
  if (!dateString) return '';

  // 정규식을 사용하여 날짜 형식 변환
  // 2025. 05. 17. 또는 2025.05.17. 또는 2025.5.17. 등의 형식을 처리
  const dateMatch = dateString.match(/(\d{4})[.\s-]*(\d{1,2})[.\s-]*(\d{1,2})/);

  if (dateMatch) {
    const year = dateMatch[1];
    // 월과 일이 한 자리 수인 경우 앞에 0을 추가
    const month = dateMatch[2].padStart(2, '0');
    const day = dateMatch[3].padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 매칭되지 않으면 원래 문자열 반환
  return dateString;
};

// 검색 버튼 클릭 시 실행되는 함수
const searchUsers = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동
  fetchUsers();
};

// 검색 초기화 함수
const resetSearch = () => {
  searchQuery.value = '';
  searchType.value = availableSearchTypes.value.length > 0 ? availableSearchTypes.value[0].value : 'userEmail'; // 기본 검색 타입으로 리셋
  currentPage.value = 0; // 초기화 시 첫 페이지로 이동
  fetchUsers();
};

// 검색 처리 (디바운스 적용)
let searchTimeout;
const handleSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // 검색어가 비어있을 때는 아무 동작도 하지 않음
    // 관리자가 직접 검색 버튼을 클릭하거나 Enter 키를 눌러야 데이터가 로드됨
    // 검색어 입력 중에는 화면이 바뀌지 않음
  }, 300);
};

// 역할 표시 형식 변환
const formatRole = (roleId) => {
  const roleMap = {
    'SUPER_ADMIN': '슈퍼 관리자',
    'PROJECT_ADMIN': '프로젝트 관리자',
    'SUB_ADMIN': '서브 관리자',
    'VIEWER': '뷰어'
  };

  return roleMap[roleId] || roleId;
};

// 상태 표시 형식 변환
const formatStatus = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'INACTIVE': '비활성',
    'LOCKED': '잠금'
  };

  return statusMap[status] || status;
};

// 날짜 형식 변환
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('ko-KR', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 관리자 생성 페이지로 이동
const navigateToCreateUser = () => {
  router.push({ name: 'admin-user-form' });
};

// 관리자 수정 페이지로 이동
const editUser = (userEmail) => {
  router.push({
    name: 'admin-user-form',
    params: { userEmail }
  });
};

// 삭제 확인 모달 표시
const confirmDeleteUser = (user) => {
  selectedUser.value = user;
  showDeleteModal.value = true;
};

// 삭제 취소
const cancelDelete = () => {
  showDeleteModal.value = false;
  selectedUser.value = null;
};

// 관리자 삭제 처리
const handleDeleteUser = async () => {
  if (!selectedUser.value) return;

  isDeleting.value = true;

  try {
    // 삭제할 관리자 이메일 가져오기
    const userEmail = selectedUser.value.userEmail;

    // 삭제 API 호출
    await deleteUser(userEmail);

    // 성공 메시지 표시
    alert(`${selectedUser.value.userEmail} 관리자가 성공적으로 삭제되었습니다.`);

    // 모달 닫기
    showDeleteModal.value = false;
    selectedUser.value = null;

    // 삭제 후 목록 새로고침
    await fetchUsers();
  } catch (err) {
    // 에러 처리
    error.value = handleApiError(err, '관리자 삭제 중 오류가 발생했습니다.');
    console.error('관리자 삭제 실패:', err);
  } finally {
    isDeleting.value = false;
  }
};

// 정렬, 페이지 또는 페이지당 항목 수 변경 시 데이터 다시 로드
watch([sortKey, sortOrder, itemsPerPage], () => {
  // 정렬 또는 페이지당 항목 수 변경 시 첫 페이지로 이동
  currentPage.value = 0;
  fetchUsers();
});

// 컴포넌트 마운트 시 관리자 목록 로드
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.user-list-container {
  margin-top: 20px;
}

/* 필터 스타일 */
.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group input, .filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background-color: #45a049;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: #0b7dda;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

.user-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.user-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
}

.user-table th {
  background-color: #f2f2f2;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  cursor: pointer;
  user-select: none;
}

.user-table th:hover {
  background-color: #e6e6e6;
}

.user-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #ddd;
}

.user-table tr:hover {
  background-color: #f5f5f5;
}

.current-user-row {
  background-color: #e8f4fd !important;
}

.current-user-row:hover {
  background-color: #d1e9fb !important;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  min-width: 120px;
}

/* 액션 버튼 스타일 */
.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.edit-btn {
  background-color: #fff8e1;
  color: #ffa000;
}

.edit-btn:hover {
  background-color: #ffecb3;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

.delete-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 로딩 및 에러 메시지 스타일 */
.loading-message,
.no-data-message,
.no-projects-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  text-align: center;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin: 20px 0;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

/* 디버깅 정보 스타일 */
.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning {
  color: #f44336;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #F44336;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #da190b;
}

.confirm-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-btn:hover {
  background-color: #d0d0d0;
}

/* 프로젝트 없음 메시지 스타일 */
.no-projects-message {
  margin: 30px 0;
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  font-size: 1.2rem;
  color: #6c757d;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}
/* 페이지당 항목 수 선택 UI 스타일 */
.user-management .items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.user-management .items-per-page-buttons {
  display: flex;
  margin-left: 10px;
}

.user-management .item-count-btn {
  padding: 6px 10px;
  margin: 0 4px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-management .item-count-btn:hover {
  background-color: #e0e0e0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.user-management .item-count-btn.active {
  background-color: #4CAF50 !important;
  color: white !important;
  border-color: #45a049 !important;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
</style>