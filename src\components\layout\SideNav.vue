<!-- src/components/layout/SideNav.vue -->
<template>
  <nav class="side-nav">
    <!-- 알림 벨 컴포넌트: 오른쪽 상단에 배치 -->
    <div class="notification-container">
      <div class="notification-wrapper" v-if="authStore.isAuthenticated">
        <NotificationBell @toggle="toggleNotificationList" />
        <!-- 알림 목록 컴포넌트 -->
        <NotificationList
          v-if="authStore.isAuthenticated"
          :is-open="showNotificationList"
          @close="closeNotificationList"
        />
      </div>
    </div>
    
    <ul>
      <li v-for="route in parentRoutes" :key="route.name" class="parent-item">
        <!-- 이벤트 관리 메뉴 -->
        <template v-if="route.name === 'events-management'">
          <div @click="toggleEvents" class="nav-link clickable">
            {{ route.meta.title }}
            <span class="arrow" :class="{ open: eventsOpen }">▾</span>
          </div>
          <ul v-if="eventsOpen" class="sub-menu">
            <li v-for="child in eventsChildRoutes" :key="child.name" class="sub-item">
              <router-link :to="{ name: child.name }" class="nav-link">{{ child.title }}</router-link>
            </li>
          </ul>
        </template>
        <!-- 통계 메뉴 -->
        <template v-else-if="route.name === 'statistics'">
          <div @click="toggleStatistics" class="nav-link clickable">
            {{ route.meta.title }}
            <span class="arrow" :class="{ open: statisticsOpen }">▾</span>
          </div>
          <ul v-if="statisticsOpen" class="sub-menu">
            <li v-for="child in statisticsChildRoutes" :key="child.name" class="sub-item">
              <router-link :to="{ name: child.name }" class="nav-link">{{ child.title }}</router-link>
            </li>
          </ul>
        </template>
        <!-- QnA 메뉴 (단일 메뉴) -->
        <template v-else-if="route.name === 'qna-management'">
          <router-link :to="{ name: 'qna-management' }" class="nav-link">{{ route.meta.title }}</router-link>
        </template>
        <!-- 기본 메뉴 -->
        <router-link v-else :to="{ name: route.name }" class="nav-link">{{ route.meta.title }}</router-link>
      </li>
    </ul>

    <!-- 프로젝트 정보 표시 -->
    <div v-if="authStore.user" class="project-info" :class="{ 'no-project': !hasProjects }">
      <div v-if="hasProjects" class="project-container">
        <span class="project-label">프로젝트 선택:</span>

        <!-- 프로젝트 선택 드롭다운 -->
        <select
          v-model.number="authStore.selectedProjectIndex"
          class="project-select"
          @change="changeProject"
        >
          <!-- SUPER_ADMIN인 경우 '전체 프로젝트' 옵션 추가 -->
          <option v-if="isSuperAdmin" :value="-1">전체 프로젝트</option>
          <option
            v-for="(project, index) in authStore.userProjects"
            :key="project.projectId"
            :value="index"
          >
            {{ project.projectName }}
          </option>
        </select>
      </div>
      <span v-else class="no-project-name">할당된 프로젝트 없음</span>
    </div>

    <!-- Logout button -->
    <button v-if="authStore.isAuthenticated" @click="logout" class="logout-button">로그아웃</button>
  </nav>
</template>

<script setup>
import { computed, watchEffect, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useNotificationStore } from '@/stores/notificationStore';
import NotificationBell from '@/components/notification/NotificationBell.vue';
import NotificationList from '@/components/notification/NotificationList.vue';

const router = useRouter();
const authStore = useAuthStore();
const notificationStore = useNotificationStore();

// 알림 관련 상태 및 함수
const showNotificationList = ref(false);

// 알림 목록 토글
const toggleNotificationList = () => {
  showNotificationList.value = !showNotificationList.value;
};

// 알림 목록 닫기
const closeNotificationList = () => {
  showNotificationList.value = false;
};

// 컴포넌트 마운트 시 읽지 않은 알림 개수 가져오기
onMounted(async () => {
  if (authStore.isAuthenticated) {
    try {
      await notificationStore.fetchUnreadCountAction();
    } catch (error) {
      console.error('알림 개수를 불러오는 중 오류가 발생했습니다:', error);
    }
  }
});

const userRole = computed(() => authStore.user?.roleId);

// SUPER_ADMIN 여부 확인
const isSuperAdmin = computed(() => {
  return authStore.user?.roleId === 'SUPER_ADMIN';
});

// 프로젝트 관련 계산 속성
const hasProjects = computed(() => {
  return authStore.user?.projects && authStore.user.projects.length > 0;
});

// 현재 프로젝트 ID
const currentProjectId = computed(() => {
  return authStore.currentProject?.projectId || '-';
});

// 프로젝트 변경 함수
const changeProject = () => {
  // 선택된 인덱스를 숫자로 변환 (select 옵션은 문자열로 처리될 수 있음)
  const selectedIndex = Number(authStore.selectedProjectIndex);

  // auth 스토어의 selectProject 함수 호출 (전체 프로젝트 모드도 처리)
  const success = authStore.selectProject(selectedIndex);

  if (success) {
    // 커스텀 이벤트 발생 (모든 페이지에서 이 이벤트를 감지하여 데이터 다시 로드)
    window.dispatchEvent(new CustomEvent('project-changed', {
      detail: {
        projectId: authStore.currentProject?.projectId,
        projectName: authStore.currentProject?.projectName
      }
    }));

    // 현재 페이지가 사용자 관리 페이지인 경우 특별 처리 (새로고침 필요)
    const currentRoute = router.currentRoute.value;
    if (currentRoute.name === 'admin-user-management') {
      // 사용자 관리 페이지는 프로젝트 변경 시 새로고침 필요
      window.location.reload();
      return;
    }
  } else {
    // 실패 시 첫 번째 프로젝트로 되돌림 (SUPER_ADMIN이 아닌 경우)
    if (selectedIndex === -1 && authStore.user?.roleId !== 'SUPER_ADMIN') {
      authStore.selectedProjectIndex = 0;
    }
  }
};

const filteredRoutes = computed(() => {
  const currentRole = userRole.value;
  return router.options.routes.filter(route => {
    // 메뉴 제목이 있고, 인증이 필요한 라우트만 필터링
    if (!route.meta || !route.meta.title || !route.meta.requiresAuth) {
      return false;
    }

    const requiredPermissions = route.meta.permissions;

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    if (!currentRole) {
        return false;
    }

    const hasPermission = requiredPermissions.includes(currentRole);
    return hasPermission;
  });
});

// 가장 상위 레벨 기본 라우트만 필터링
// 이벤트 관리 메뉴가 중복되지 않도록 children이 있는 라우트만 포함
const parentRoutes = computed(() => {
  return filteredRoutes.value.filter(route => {
    // 자식 메뉴가 있는 경우만 포함 (events-management, statistics 등)
    if (route.name === 'events-management' || route.name === 'statistics') {
      return true;
    }
    // 이벤트 관리 관련 라우트는 자식 메뉴를 통해서만 노출
    if (route.path && route.path.includes('/events')) {
      return false;
    }
    return true;
  });
});
const eventsChildRoutes = computed(() => {
  const list = [];
  // 라우터에서 이벤트 관련 자식 라우트 찾기
  const eventsRoute = router.options.routes.find(r => r.name === 'events-management');
  if (eventsRoute && eventsRoute.children) {
    eventsRoute.children.forEach(child => {
      if (child.meta && child.meta.title) {
        list.push({ name: child.name, title: child.meta.title });
      }
    });
  }

  return list;
});
const eventsOpen = ref(false);
const toggleEvents = () => { eventsOpen.value = !eventsOpen.value; };

// 통계 메뉴 관련 상태 및 함수
const statisticsChildRoutes = computed(() => {
  const list = [];
  // 라우터에서 통계 관련 자식 라우트 찾기
  const statsRoute = router.options.routes.find(r => r.name === 'statistics');
  if (statsRoute && statsRoute.children) {
    const currentRole = userRole.value; // 현재 사용자 역할 가져오기
    statsRoute.children.forEach(child => {
      // 자식 라우트에 title 메타 정보가 있고, 현재 사용자가 접근 권한이 있는지 확인
      if (child.meta && child.meta.title) {
        const requiredPermissions = child.meta.permissions;
        if (!requiredPermissions || requiredPermissions.length === 0 || (currentRole && requiredPermissions.includes(currentRole))) {
          list.push({ name: child.name, title: child.meta.title });
        }
      }
    });
  }
  return list;
});

const statisticsOpen = ref(false);

const toggleStatistics = () => {
  statisticsOpen.value = !statisticsOpen.value;
};

// QnA 메뉴 관련 상태 및 함수
const qnaChildRoutes = computed(() => {
  const list = [];

  // QnA 라우트 자식 메뉴 추출
  router.options.routes
    .filter(route => route.name === 'qna-management')
    .forEach(route => {
      if (route.children) {
        route.children.forEach(child => {
          if (child.meta && child.meta.title) {
            list.push({
              name: child.name,
              title: child.meta.title
            });
          }
        });
      }
    });

  return list;
});

const qnaOpen = ref(false);

const toggleQna = () => {
  qnaOpen.value = !qnaOpen.value;
};

// 로그아웃 함수 (Auth 스토어의 logout 액션 호출)
const logout = async () => {
  try {
    await authStore.logout();
    // 로그아웃 성공 후 로그인 페이지로 리다이렉트
    router.push({ name: 'login' });
  } catch (error) {
    console.error('Logout failed:', error);
    // 필요한 경우 사용자에게 에러 메시지 표시
  }
};
</script>

<style scoped>
.side-nav {
  width: 250px;
  background-color: #f8f9fa;
  padding: 20px 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 알림 관련 스타일 */
.notification-container {
  position: fixed;
  top: 15px;
  right: 20px;
  z-index: 1000;
}

.notification-wrapper {
  margin: 0;
  padding: 0;
}

.side-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}

.side-nav li {
  margin-bottom: 5px;
}

.nav-link {
  display: block;
  padding: 12px 20px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s;
  border-radius: 0;
  font-size: 0.95rem;
}

.nav-link:hover,
.nav-link.router-link-active {
  background-color: #e9ecef;
  color: #007bff;
  border-left: 3px solid #007bff;
}

.nav-link.clickable {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.arrow {
  transition: transform 0.3s ease;
  font-size: 0.8rem;
  margin-left: 5px;
}

.arrow.open {
  transform: rotate(180deg);
}

.sub-menu {
  list-style: none;
  padding-left: 15px;
  margin: 0;
  border-left: 2px solid #dee2e6;
  background-color: #f1f3f5;
}

.sub-item .nav-link {
  font-size: 0.9rem;
  color: #495057;
  padding: 10px 15px;
}

/* 프로젝트 정보 스타일 */
.project-info {
  margin: 15px;
  padding: 12px;
  background-color: #f0f0f0;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 프로젝트가 없을 때 스타일 */
.project-info.no-project {
  background-color: #ffe6e6;
  border: 1px dashed #e74c3c;
}

.project-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.project-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 2px;
  font-weight: 500;
}

/* 프로젝트 선택 드롭다운 스타일 */
.project-select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.project-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* 현재 프로젝트 정보 스타일 */
.current-project-info {
  display: flex;
  justify-content: center;
  margin-top: 5px;
}

.project-id {
  font-size: 0.85rem;
  color: #666;
  background-color: #e8e8e8;
  padding: 3px 8px;
  border-radius: 4px;
}

.project-name {
  font-weight: bold;
  color: #333;
  font-size: 0.95rem;
}

.no-project-name {
  font-weight: bold;
  color: #e74c3c;
  font-size: 0.9rem;
  font-style: italic;
}

.logout-button {
  margin: 15px;
  padding: 12px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  width: calc(100% - 30px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logout-button:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logout-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.side-nav li.sub-item .nav-link {
  padding-left: 30px;
}

/* 모바일 최적화 */
@media (max-width: 767px) {
  .side-nav {
    width: 250px;
    background-color: #fff;
  }

  .nav-link {
    padding: 15px 20px;
    font-size: 1rem;
  }

  .sub-item .nav-link {
    padding: 12px 15px;
    font-size: 0.95rem;
  }

  .logout-button {
    padding: 15px;
    font-size: 1rem;
  }
}

</style>
