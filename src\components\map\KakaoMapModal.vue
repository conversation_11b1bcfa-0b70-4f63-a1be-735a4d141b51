<template>
  <div class="kakao-map-modal" v-if="isVisible">
    <div class="modal-overlay" @click="closeModal"></div>
    <div class="modal-container">
      <div class="modal-header">
        <h3>카카오맵에서 위치 선택</h3>
        <button class="close-button" @click="closeModal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="search-container">
          <input
            type="text"
            v-model="searchKeyword"
            placeholder="주소 또는 장소 검색"
            @keyup.enter="searchLocation"
          />
          <button @click="searchLocation">검색</button>
        </div>
        <div id="kakao-map" ref="mapContainer" class="map-container"></div>
        <div class="selected-location" v-if="selectedLocation">
          <p><strong>선택한 위치:</strong> {{ selectedLocation.address }}</p>
          <p><strong>위도:</strong> {{ selectedLocation.lat }}</p>
          <p><strong>경도:</strong> {{ selectedLocation.lng }}</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-button" @click="closeModal">취소</button>
        <button
          class="confirm-button"
          @click="confirmLocation"
          :disabled="!selectedLocation"
        >
          위치 선택 완료
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  initialLocation: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['close', 'select-location']);

const mapContainer = ref(null);
const map = ref(null);
const marker = ref(null);
const searchKeyword = ref('');
const selectedLocation = ref(null);
const kakaoMapLoaded = ref(false);

// 초기 위치 설정 (제주시청)
const defaultCenter = {
  lat: 33.4996,
  lng: 126.5312
};

// 카카오맵 API 스크립트 로드
const loadKakaoMapScript = () => {
  return new Promise((resolve, reject) => {
    // 이미 로드된 경우
    if (window.kakao && window.kakao.maps) {
      kakaoMapLoaded.value = true;
      resolve();
      return;
    }

    // 환경 변수에서 카카오맵 API 키 가져오기
    const kakaoMapApiKey = import.meta.env.VITE_KAKAO_MAP_API_KEY;

    if (!kakaoMapApiKey) {
      console.error('카카오맵 API 키가 설정되지 않았습니다. 환경 변수 VITE_KAKAO_MAP_API_KEY를 확인해주세요.');
      reject(new Error('카카오맵 API 키가 설정되지 않았습니다.'));
      return;
    }

    const script = document.createElement('script');
    script.src = `//dapi.kakao.com/v2/maps/sdk.js?appkey=${kakaoMapApiKey}&libraries=services,clusterer,drawing&autoload=false`;
    script.async = true;
    script.onload = () => {
      window.kakao.maps.load(() => {
        kakaoMapLoaded.value = true;
        resolve();
      });
    };
    script.onerror = (e) => {
      console.error('카카오맵 API 로드 실패:', e);
      reject(new Error('카카오맵 API 로드 실패'));
    };
    document.head.appendChild(script);
  });
};

// 지도 초기화
const initMap = async () => {
  if (!kakaoMapLoaded.value) {
    try {
      await loadKakaoMapScript();
    } catch (error) {
      console.error('카카오맵 API 로드 실패:', error);
      return;
    }
  }

  if (!mapContainer.value) return;

  // 초기 위치 설정 (props로 받은 위치가 있으면 사용, 없으면 기본 위치)
  const center = props.initialLocation
    ? new window.kakao.maps.LatLng(props.initialLocation.lat, props.initialLocation.lng)
    : new window.kakao.maps.LatLng(defaultCenter.lat, defaultCenter.lng);

  // 지도 생성
  const options = {
    center: center,
    level: 3
  };

  map.value = new window.kakao.maps.Map(mapContainer.value, options);

  // 마커 생성
  marker.value = new window.kakao.maps.Marker({
    position: center,
    map: map.value
  });

  // 초기 위치가 있으면 선택된 위치로 설정
  if (props.initialLocation) {
    selectedLocation.value = {
      lat: props.initialLocation.lat,
      lng: props.initialLocation.lng,
      address: props.initialLocation.address || '선택한 위치'
    };
  }

  // 지도 클릭 이벤트 등록
  window.kakao.maps.event.addListener(map.value, 'click', (mouseEvent) => {
    const latlng = mouseEvent.latLng;

    // 마커 위치 변경
    marker.value.setPosition(latlng);

    // 좌표를 주소로 변환
    const geocoder = new window.kakao.maps.services.Geocoder();
    geocoder.coord2Address(latlng.getLng(), latlng.getLat(), (result, status) => {
      if (status === window.kakao.maps.services.Status.OK) {
        const address = result[0].address.address_name || '주소 정보 없음';

        // 선택한 위치 정보 저장
        selectedLocation.value = {
          lat: latlng.getLat(),
          lng: latlng.getLng(),
          address: address
        };
      } else {
        // 주소 변환 실패 시
        selectedLocation.value = {
          lat: latlng.getLat(),
          lng: latlng.getLng(),
          address: '주소 정보를 가져올 수 없습니다.'
        };
      }
    });
  });
};

// 위치 검색
const searchLocation = () => {
  if (!searchKeyword.value.trim() || !map.value) return;

  const places = new window.kakao.maps.services.Places();

  places.keywordSearch(searchKeyword.value, (result, status) => {
    if (status === window.kakao.maps.services.Status.OK) {
      // 첫 번째 검색 결과로 이동
      const firstItem = result[0];
      const moveLatLng = new window.kakao.maps.LatLng(firstItem.y, firstItem.x);

      // 지도 이동
      map.value.setCenter(moveLatLng);

      // 마커 이동
      marker.value.setPosition(moveLatLng);

      // 선택한 위치 정보 저장
      selectedLocation.value = {
        lat: parseFloat(firstItem.y),
        lng: parseFloat(firstItem.x),
        address: firstItem.address_name || firstItem.place_name
      };
    } else {
      alert('검색 결과가 없습니다.');
    }
  });
};

// 위치 선택 완료
const confirmLocation = () => {
  if (selectedLocation.value) {
    emit('select-location', selectedLocation.value);
    closeModal();
  }
};

// 모달 닫기
const closeModal = () => {
  emit('close');
};

// 모달이 열릴 때 지도 초기화
watch(() => props.isVisible, (newValue) => {
  if (newValue) {
    // 모달이 DOM에 추가된 후 지도 초기화
    setTimeout(() => {
      initMap();
    }, 100);
  }
});

onMounted(() => {
  if (props.isVisible) {
    initMap();
  }
});

onUnmounted(() => {
  // 필요한 정리 작업
  if (map.value && window.kakao && window.kakao.maps) {
    // 이벤트 리스너 제거 등의 정리 작업
  }
});
</script>

<style scoped>
.kakao-map-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
  position: relative;
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.search-container {
  display: flex;
  margin-bottom: 16px;
}

.search-container input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 16px;
}

.search-container button {
  padding: 10px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 16px;
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 16px;
}

.selected-location {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.selected-location p {
  margin: 6px 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #eee;
  gap: 10px;
}

.cancel-button, .confirm-button {
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  min-height: 52px;
}

.cancel-button {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  color: #333;
}

.confirm-button {
  background-color: #4CAF50;
  border: none;
  color: white;
}

.confirm-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
</style>
