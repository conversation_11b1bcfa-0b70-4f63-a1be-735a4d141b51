<template>
  <div class="link-properties">
    <h4>링크 속성</h4>

    <div class="property-row">
      <label>링크 텍스트:</label>
      <input
        type="text"
        :value="element.content.text"
        @input="updateProperty('content.text', $event.target.value)"
        placeholder="링크 텍스트"
      />
    </div>

    <div class="property-row">
      <label>URL:</label>
      <div class="input-container">
        <input
          type="text"
          :value="element.content.url"
          @input="updateProperty('content.url', $event.target.value)"
          placeholder="https://example.com"
          :class="{ 'invalid-url': isInvalidUrl }"
        />
        <div v-if="isInvalidUrl" class="url-validation-error">
          URL은 http:// 또는 https://로 시작해야 합니다.
        </div>
      </div>
    </div>

    <div class="property-row">
      <label>새 탭에서 열기:</label>
      <div class="checkbox-container">
        <input
          type="checkbox"
          :checked="element.content.newTab"
          @change="updateProperty('content.newTab', $event.target.checked)"
          id="newTabCheckbox"
        />
        <label for="newTabCheckbox" class="checkbox-label"></label>
      </div>
    </div>

    <div class="property-section">
      <h5>텍스트 스타일</h5>

      <div class="property-row">
        <label>폰트 크기:</label>
        <div class="input-with-unit">
          <input
            type="number"
            :value="element.content.textStyle.fontSize"
            @input="updateProperty('content.textStyle.fontSize', parseInt($event.target.value))"
            min="8"
            max="72"
          />
          <span class="unit">px</span>
        </div>
      </div>

      <div class="property-row">
        <label>폰트:</label>
        <select
          :value="element.content.textStyle.fontFamily"
          @change="updateProperty('content.textStyle.fontFamily', $event.target.value)"
        >
          <option value="Arial, sans-serif">Arial</option>
          <option value="'Noto Sans KR', sans-serif">Noto Sans KR</option>
          <option value="'Malgun Gothic', sans-serif">맑은 고딕</option>
          <option value="'Courier New', monospace">Courier New</option>
          <option value="Georgia, serif">Georgia</option>
          <option value="'Times New Roman', serif">Times New Roman</option>
        </select>
      </div>

      <div class="property-row">
        <label>글자 색상:</label>
        <input
          type="color"
          :value="element.content.textStyle.color"
          @input="updateProperty('content.textStyle.color', $event.target.value)"
        />
      </div>

      <div class="property-row">
        <label>글자 두께:</label>
        <select
          :value="element.content.textStyle.fontWeight"
          @change="updateProperty('content.textStyle.fontWeight', $event.target.value)"
        >
          <option value="normal">보통</option>
          <option value="bold">굵게</option>
          <option value="lighter">얇게</option>
          <option value="100">100</option>
          <option value="200">200</option>
          <option value="300">300</option>
          <option value="400">400</option>
          <option value="500">500</option>
          <option value="600">600</option>
          <option value="700">700</option>
          <option value="800">800</option>
          <option value="900">900</option>
        </select>
      </div>

      <div class="property-row">
        <label>텍스트 장식:</label>
        <select
          :value="element.content.textStyle.textDecoration"
          @change="updateProperty('content.textStyle.textDecoration', $event.target.value)"
        >
          <option value="none">없음</option>
          <option value="underline">밑줄</option>
          <option value="overline">윗줄</option>
          <option value="line-through">취소선</option>
        </select>
      </div>
    </div>

    <div class="property-section">
      <h5>호버 효과</h5>

      <div class="property-row">
        <label>텍스트 색상:</label>
        <input
          type="color"
          :value="element.content.hoverStyle.color"
          @input="updateProperty('content.hoverStyle.color', $event.target.value)"
        />
      </div>

      <div class="property-row">
        <label>텍스트 장식:</label>
        <select
          :value="element.content.hoverStyle.textDecoration"
          @change="updateProperty('content.hoverStyle.textDecoration', $event.target.value)"
        >
          <option value="none">없음</option>
          <option value="underline">밑줄</option>
          <option value="overline">윗줄</option>
          <option value="line-through">취소선</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 프롭스
const props = defineProps({
  element: {
    type: Object,
    required: true
  }
});

// 이벤트
const emit = defineEmits(['update']);

// URL 유효성 검사 함수
const isValidUrl = (url) => {
  return url && (url.startsWith('http://') || url.startsWith('https://'));
};

// URL이 유효하지 않은 경우 확인
const isInvalidUrl = computed(() => {
  return props.element.content.url && !isValidUrl(props.element.content.url);
});

// 속성 업데이트 함수
const updateProperty = (propertyPath, value) => {
  emit('update', propertyPath, value);
};
</script>

<style scoped>
.link-properties {
  margin-bottom: 16px;
}

h4, h5 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  color: #555;
}

h5 {
  font-size: 13px;
  margin-top: 16px;
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.property-section {
  margin-bottom: 16px;
}

.property-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.property-row label {
  flex: 0 0 100px;
  font-size: 13px;
  color: #666;
}

.property-row input,
.property-row select {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.property-row input[type="color"] {
  width: 40px;
  height: 24px;
  padding: 0;
  border: 1px solid #ddd;
}

.input-with-unit {
  display: flex;
  align-items: center;
  flex: 1;
}

.input-with-unit input {
  flex: 1;
  margin-right: 4px;
}

.unit {
  font-size: 12px;
  color: #666;
  width: 20px;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-container input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.checkbox-label {
  font-size: 13px;
  color: #666;
  cursor: pointer;
}

.input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.input-container input.invalid-url {
  border-color: #f44336;
  background-color: #ffebee;
}

.url-validation-error {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
}
</style>
