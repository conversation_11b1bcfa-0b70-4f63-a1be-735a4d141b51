import { ref, onUnmounted } from 'vue';
import { useLandingEditorStore } from '@/stores/landingEditor';

/**
 * 요소 리사이즈 기능을 제공하는 컴포저블 함수
 * @param {Object} props - 컴포넌트 props
 * @param {Function} emit - 컴포넌트 emit 함수
 * @returns {Object} 리사이즈 관련 상태 및 함수
 */
export function useResizable(props, emit) {
  // 랜딩 에디터 스토어
  const landingEditorStore = useLandingEditorStore();

  // 작업 그룹화 함수 안전하게 호출하는 헬퍼 함수
  const safeStartGrouping = () => {
    if (landingEditorStore && typeof landingEditorStore.startGroupingOperations === 'function') {
      landingEditorStore.startGroupingOperations();
    } else {
      console.warn('startGroupingOperations 함수를 찾을 수 없습니다.');
    }
  };

  const safeEndGrouping = () => {
    if (landingEditorStore && typeof landingEditorStore.endGroupingOperations === 'function') {
      landingEditorStore.endGroupingOperations();
    } else {
      console.warn('endGroupingOperations 함수를 찾을 수 없습니다.');
    }
  };
  // 리사이즈 관련 상태
  const isResizing = ref(false);
  const resizeHandle = ref('');
  const startX = ref(0);
  const startY = ref(0);
  const originalWidth = ref(0);
  const originalHeight = ref(0);
  const originalX = ref(0);
  const originalY = ref(0);

  // 리사이즈 시작 핸들러
  const startResize = (event, handle) => {
    // 작업 그룹화 시작 - 리사이즈 시작 시 상태 저장
    safeStartGrouping();

    // 리사이즈 시작 위치 저장
    isResizing.value = true;
    resizeHandle.value = handle;
    startX.value = event.clientX;
    startY.value = event.clientY;

    // 원래 요소 크기 저장
    try {
      // 너비 가져오기
      if (props.element.size.width && typeof props.element.size.width === 'object') {
        originalWidth.value = Number(props.element.size.width.value) || 100;
      } else {
        originalWidth.value = Number(props.element.size.width) || 100;
      }

      // 높이 가져오기
      if (props.element.size.height && typeof props.element.size.height === 'object') {
        originalHeight.value = Number(props.element.size.height.value) || 100;
      } else {
        originalHeight.value = Number(props.element.size.height) || 100;
      }

      // 원래 위치 저장 (리사이즈 시 위치 조정을 위해)
      if (props.element.position.x && typeof props.element.position.x === 'object') {
        originalX.value = Number(props.element.position.x.value) || 0;
      } else {
        originalX.value = Number(props.element.position.x) || 0;
      }

      if (props.element.position.y && typeof props.element.position.y === 'object') {
        originalY.value = Number(props.element.position.y.value) || 0;
      } else {
        originalY.value = Number(props.element.position.y) || 0;
      }

    } catch (error) {
      console.error('크기 가져오기 오류:', error);
      originalWidth.value = 100;
      originalHeight.value = 100;
      originalX.value = 0;
      originalY.value = 0;
    }

    // 전역 이벤트 리스너 추가
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', stopResize);

    // 이벤트 전파 중지 및 기본 동작 방지
    event.stopPropagation();
    event.preventDefault();
  };

  // 리사이즈 중 핸들러
  const handleResize = (event) => {
    if (!isResizing.value) return;

    // 이동 거리 계산
    const deltaX = event.clientX - startX.value;
    const deltaY = event.clientY - startY.value;

    // 새 크기 및 위치 계산
    let newWidth = originalWidth.value;
    let newHeight = originalHeight.value;
    let newX = originalX.value;
    let newY = originalY.value;

    // 핸들에 따라 크기 및 위치 조정
    switch (resizeHandle.value) {
      case 'top-left':
        newWidth = Math.max(1, originalWidth.value - deltaX);
        newHeight = Math.max(1, originalHeight.value - deltaY);
        newX = originalX.value + (originalWidth.value - newWidth);
        newY = originalY.value + (originalHeight.value - newHeight);
        break;
      case 'top':
        newHeight = Math.max(1, originalHeight.value - deltaY);
        newY = originalY.value + (originalHeight.value - newHeight);
        break;
      case 'top-right':
        newWidth = Math.max(1, originalWidth.value + deltaX);
        newHeight = Math.max(1, originalHeight.value - deltaY);
        newY = originalY.value + (originalHeight.value - newHeight);
        break;
      case 'right':
        newWidth = Math.max(1, originalWidth.value + deltaX);
        break;
      case 'bottom-right':
        newWidth = Math.max(1, originalWidth.value + deltaX);
        newHeight = Math.max(1, originalHeight.value + deltaY);
        break;
      case 'bottom':
        newHeight = Math.max(1, originalHeight.value + deltaY);
        break;
      case 'bottom-left':
        newWidth = Math.max(1, originalWidth.value - deltaX);
        newHeight = Math.max(1, originalHeight.value + deltaY);
        newX = originalX.value + (originalWidth.value - newWidth);
        break;
      case 'left':
        newWidth = Math.max(1, originalWidth.value - deltaX);
        newX = originalX.value + (originalWidth.value - newWidth);
        break;
    }

    // 크기 업데이트
    emit('update', props.element.id, 'size.width.value', newWidth);
    emit('update', props.element.id, 'size.height.value', newHeight);

    // 위치 업데이트
    emit('positionUpdate', props.element.id, newX, newY);

    // 이벤트 전파 중지 및 기본 동작 방지
    event.stopPropagation();
    event.preventDefault();
  };

  // 리사이즈 종료 핸들러
  const stopResize = (event) => {
    if (!isResizing.value) return;

    isResizing.value = false;
    resizeHandle.value = '';

    // 작업 그룹화 종료 - 리사이즈 종료 시 상태 저장
    safeEndGrouping();

    // 전역 이벤트 리스너 제거
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);

    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
  };

  // 컴포넌트 마운트 시 이벤트 리스너 정리
  onUnmounted(() => {
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
  });

  return {
    isResizing,
    startResize
  };
}
