<template>
  <div class="force-password-change-page">
    <div class="force-password-change-container">
      <div class="header-section">
        <div class="lock-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
          </svg>
        </div>
        <h1>비밀번호 변경 필요</h1>
        <div class="security-badge">
          <span>보안 조치</span>
        </div>
      </div>

      <div class="step-indicator">
        <div class="step active">
          <div class="step-number">1</div>
          <div class="step-label">로그인</div>
        </div>
        <div class="step-connector active"></div>
        <div class="step active current">
          <div class="step-number">2</div>
          <div class="step-label">비밀번호 변경</div>
        </div>
        <div class="step-connector"></div>
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-label">시스템 사용</div>
        </div>
      </div>

      <!-- 성공 상태가 아닐 때만 정보 박스 표시 -->
      <div v-if="!isSuccess" class="info-box">
        <div class="info-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        </div>
        <div class="info-content">
          <h3>최초 로그인을 환영합니다!</h3>
          <p>{{ message || '보안을 위해 처음 로그인하실 때 비밀번호를 변경해야 합니다. 안전한 비밀번호로 변경해 주세요.' }}</p>
          <p class="user-email"><strong>사용자 이메일:</strong> {{ authStore.tempUserEmail || '이메일 정보 없음' }}</p>
        </div>
      </div>

      <!-- 성공 상태가 아닐 때만 폼 표시 -->
      <form v-if="!isSuccess" @submit.prevent="handlePasswordChange" class="password-change-form">
        <div class="form-group">
          <label for="currentPassword">현재 비밀번호:</label>
          <input
            type="password"
            id="currentPassword"
            v-model="currentPassword"
            required
            placeholder="현재 비밀번호 입력"
          />
        </div>

        <div class="form-group">
          <label for="newPassword">새 비밀번호:</label>
          <input
            type="password"
            id="newPassword"
            v-model="newPassword"
            required
            placeholder="새 비밀번호 입력"
            @input="validatePasswords"
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">새 비밀번호 확인:</label>
          <input
            type="password"
            id="confirmPassword"
            v-model="confirmPassword"
            required
            placeholder="새 비밀번호 다시 입력"
            @input="validatePasswords"
          />
          <p v-if="passwordMismatch" class="error-message">
            비밀번호가 일치하지 않습니다.
          </p>
        </div>

        <div class="button-group">
          <button type="submit" class="primary-button" :disabled="isSubmitting || passwordMismatch">
            {{ isSubmitting ? '처리 중...' : '비밀번호 변경' }}
          </button>
          <button type="button" class="cancel-button" @click="handleCancel" :disabled="isSubmitting">
            로그아웃
          </button>
        </div>

        <div class="message-container">
          <div v-if="errorMessage" class="error-message-box">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <div class="error-content">
              <span>{{ errorMessage }}</span>
              <button v-if="errorMessage.includes('현재 비밀번호')" type="button" class="retry-button" @click="focusCurrentPassword">
                다시 입력하기
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// 상태 변수들
const currentPassword = ref('');
const newPassword = ref('');
const confirmPassword = ref('');
const isSubmitting = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const isSuccess = ref(false);

// 비밀번호 일치 여부 확인
const passwordMismatch = computed(() => {
  if (newPassword.value && confirmPassword.value) {
    return newPassword.value !== confirmPassword.value;
  }
  return false;
});

// 비밀번호 유효성 검사
const validatePasswords = () => {
  if (newPassword.value && confirmPassword.value) {
    if (newPassword.value !== confirmPassword.value) {
      errorMessage.value = '비밀번호가 일치하지 않습니다.';
    } else {
      errorMessage.value = '';
    }
  }
};

// 비밀번호 변경 취소 (로그아웃)
const handleCancel = async () => {
  if (confirm('비밀번호 변경을 취소하고 로그아웃 하시겠습니까?')) {
    await authStore.logout();
    router.push('/login');
  }
};

// 비밀번호 변경 처리
const handlePasswordChange = async () => {
  // 비밀번호 일치 여부 확인
  if (passwordMismatch.value) {
    errorMessage.value = '새 비밀번호가 일치하지 않습니다.';
    return;
  }

  // 현재 비밀번호와 새 비밀번호가 같은지 확인
  if (currentPassword.value === newPassword.value) {
    errorMessage.value = '새 비밀번호는 현재 비밀번호와 달라야 합니다.';
    return;
  }

  isSubmitting.value = true;
  errorMessage.value = '';
  successMessage.value = '';

  try {
    // 비밀번호 변경 API 호출
    const result = await authStore.changeInitialPassword(
      currentPassword.value,
      newPassword.value
    );

    if (result.success) {
      // 성공 메시지를 alert로 표시
      window.alert(result.message || '비밀번호가 성공적으로 변경되었습니다. 다시 로그인해주세요.');

      // 상태 초기화
      authStore.resetPasswordChangeState();

      // 로그인 페이지로 이동
      router.push('/login');
    } else {
      // 에러 메시지 표시
      errorMessage.value = result.error || '비밀번호 변경에 실패했습니다.';
      isSubmitting.value = false;

      // 현재 비밀번호 필드에 포커스
      if (errorMessage.value.includes('현재 비밀번호')) {
        setTimeout(() => {
          document.getElementById('currentPassword')?.focus();
        }, 100);
      }
    }
  } catch (error) {
    console.error('비밀번호 변경 중 오류:', error);

    // 에러 메시지 처리
    errorMessage.value = error.message || '비밀번호 변경 중 오류가 발생했습니다.';

    // 특정 에러에 대한 추가 처리
    if (errorMessage.value.includes('현재 비밀번호')) {
      // 현재 비밀번호 오류의 경우 필드를 비우고 포커스
      currentPassword.value = ''; // 현재 비밀번호 필드 초기화
      setTimeout(() => {
        document.getElementById('currentPassword')?.focus();
      }, 100);
    } else if (errorMessage.value.includes('입력값이 유효하지 않습니다')) {
      // 입력값 유효성 오류의 경우 새 비밀번호 필드에 포커스
      setTimeout(() => {
        document.getElementById('newPassword')?.focus();
      }, 100);
    }
  } finally {
    isSubmitting.value = false; // 성공/실패 여부와 관계없이 API 호출 후 제출 상태 해제
  }
};

// 도움말 보기 함수
const showHelp = () => {
  alert('관리자에게 문의하세요.'); // 간단한 도움말
};

// 현재 비밀번호 필드에 포커스
const focusCurrentPassword = () => {
  currentPassword.value = ''; // 필드 초기화
  setTimeout(() => {
    document.getElementById('currentPassword')?.focus();
  }, 100);
};

// 컴포넌트 언마운트 전 처리
onBeforeUnmount(() => {
});
</script>

<style scoped>
.force-password-change-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  background-image: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
}

.force-password-change-container {
  max-width: 550px;
  width: 100%;
  margin: 30px auto;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  background-color: #ffffff;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25px;
}

.lock-icon {
  color: #4a90e2;
  margin-bottom: 15px;
}

.security-badge {
  background-color: #e8f4ff;
  color: #4a90e2;
  font-size: 0.8rem;
  font-weight: bold;
  padding: 5px 12px;
  border-radius: 20px;
  margin-top: 10px;
  display: inline-block;
}

h1 {
  color: #2c3e50;
  margin: 10px 0;
  font-size: 1.8rem;
}

.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #757575;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-bottom: 8px;
}

.step.active .step-number {
  background-color: #4a90e2;
  color: white;
}

.step.current .step-number {
  box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.2);
}

.step-connector {
  flex-grow: 1;
  height: 2px;
  background-color: #e0e0e0;
  margin: 0 10px;
  width: 60px;
}

.step-connector.active {
  background-color: #4a90e2;
}

.step-label {
  font-size: 0.8rem;
  color: #757575;
}

.step.active .step-label {
  color: #4a90e2;
  font-weight: 500;
}

.info-box {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 25px;
  text-align: left;
  border-left: 4px solid #4a90e2;
}

.info-icon {
  color: #4a90e2;
  margin-right: 15px;
  flex-shrink: 0;
}

.info-content h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.info-content p {
  margin: 0 0 8px 0;
  color: #5d6778;
  font-size: 0.95rem;
  line-height: 1.5;
}

.info-content p:last-child {
  margin-bottom: 0;
}

.user-email {
  background-color: #f0f8ff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.password-change-form {
  text-align: left;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.95rem;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 15px;
  box-sizing: border-box;
  transition: all 0.3s;
}

input:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.15);
}

.button-group {
  display: flex;
  gap: 12px;
  margin-top: 15px;
}

button {
  flex: 1;
  padding: 14px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-button {
  background-color: #4a90e2;
  color: white;
}

.primary-button:hover:not(:disabled) {
  background-color: #3a80d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-button:hover:not(:disabled) {
  background-color: #e5e5e5;
  transform: translateY(-1px);
}

button:disabled {
  background-color: #cccccc;
  color: #888;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.message-container {
  margin-top: 20px;
}

.error-message {
  color: #e74c3c;
  margin-top: 5px;
  font-size: 0.85rem;
}

.error-message-box {
  display: flex;
  align-items: flex-start;
  background-color: #fdf1f0;
  border-radius: 6px;
  padding: 12px;
  margin-top: 15px;
  color: #e74c3c;
}

.error-message-box svg {
  margin-right: 10px;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.retry-button {
  background: none;
  border: none;
  color: #4a90e2;
  text-decoration: underline;
  cursor: pointer;
  padding: 5px 0;
  margin-top: 8px;
  font-size: 0.9rem;
  width: auto;
  text-align: left;
  flex: none;
}

.retry-button:hover {
  color: #3a80d2;
}

.help-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
}

.help-section a {
  color: #4a90e2;
  text-decoration: none;
}

.help-section a:hover {
  text-decoration: underline;
}
</style>
