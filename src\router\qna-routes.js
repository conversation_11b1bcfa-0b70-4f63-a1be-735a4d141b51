// QnA 관련 라우트 정의
export default [
  {
    path: '/qna',
    name: 'qna-list',
    component: () => import('@/views/qna/QnaListView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '문의 목록'
    }
  },
  {
    path: '/qna/:questionId',
    name: 'qna-detail',
    component: () => import('@/views/qna/QnaDetailView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '문의 상세'
    }
  },
  {
    path: '/qna/create',
    name: 'qna-create',
    component: () => import('@/views/qna/QnaFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '문의 등록'
    }
  },
  {
    path: '/qna/edit/:questionId',
    name: 'qna-edit',
    component: () => import('@/views/qna/QnaFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '문의 수정'
    }
  }
]
