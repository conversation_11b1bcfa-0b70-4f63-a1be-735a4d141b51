<template>
  <div class="inquiry-management">
    <h1>{{ isEditMode ? '문의 수정' : '문의 작성' }}</h1>
    
    <div class="inquiry-container">
      <!-- 에러 메시지 -->
      <div v-if="error" class="no-data">
        {{ error }}
      </div>
      
      <!-- 문의 폼 -->
      <div class="form-container" v-if="!error">
        <form @submit.prevent="submitInquiry">
          <div class="form-group">
            <label for="inquiryTitle">제목</label>
            <input
              type="text"
              id="inquiryTitle"
              v-model="inquiryForm.title"
              placeholder="제목을 입력하세요"
              required
              :disabled="isSubmitting || isEditMode"
            />
          </div>
          
          <div class="form-group">
            <label for="inquiryType">문의 유형</label>
            <select
              id="inquiryType"
              v-model="inquiryForm.type"
              required
              :disabled="isSubmitting"
            >
              <option value="">유형 선택</option>
              <option value="ACCOUNT">계정 문의</option>
              <option value="TECHNICAL">기술 문의</option>
              <option value="USAGE">이용 문의</option>
              <option value="ETC">기타 문의</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="inquiryContent">내용</label>
            <textarea
              id="inquiryContent"
              v-model="inquiryForm.content"
              rows="10"
              placeholder="문의 내용을 입력하세요"
              required
              @input="updateContent($event)"
            ></textarea>
          </div>
          
          <!-- 첨부파일 업로드 -->
          <div class="form-group">
            <label for="inquiryFile">파일 첨부 (선택)</label>
            <input
              type="file"
              id="inquiryFile"
              @change="handleFileUpload"
              :disabled="isSubmitting"
              multiple
            />
          </div>
          
          <!-- 첨부된 파일 목록 -->
          <div v-if="attachedFiles.length > 0" class="form-group">
            <h5>첨부된 파일</h5>
            <div class="file-list">
              <div 
                v-for="(file, index) in attachedFiles" 
                :key="index"
                class="file-item"
              >
                <div class="file-info">
                  <span class="file-name">{{ file.name }}</span>
                  <button
                    type="button"
                    class="remove-btn"
                    @click="removeFile(index)"
                    :disabled="isSubmitting"
                  >
                    삭제
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 기존 첨부파일 목록 (수정 모드에서만 표시) -->
          <div v-if="isEditMode && existingAttachments.length > 0" class="form-group">
            <h5>기존 첨부파일</h5>
            <div class="file-list">
              <div 
                v-for="attachment in existingAttachments" 
                :key="attachment.id"
                class="file-item"
              >
                <div class="file-info">
                  <span class="file-name">{{ attachment.name }}</span>
                  <div class="attachment-actions">
                    <button
                      type="button"
                      class="download-btn"
                      @click="downloadAttachment(attachment.id, attachment.name)"
                      :disabled="isSubmitting"
                    >
                      다운로드
                    </button>
                    <button
                      type="button"
                      class="remove-btn"
                      @click="removeExistingAttachment(attachment.id)"
                      :disabled="isSubmitting"
                    >
                      삭제
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 버튼 그룹 -->
          <div class="actions-bar">
            <button
              type="button"
              class="cancel-btn"
              @click.prevent="cancelForm"
              :disabled="isSubmitting"
            >
              취소
            </button>
            <button
              type="submit"
              class="create-btn"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
              {{ isEditMode ? '수정 완료' : '문의 등록' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { saveInquiry, getInquiryById, downloadInquiryAttachment, deleteAttachment } from '@/api/inquiry';
import { useAuthStore } from '@/stores/auth';

export default {
  name: 'InquiryFormView',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const authStore = useAuthStore();
    
    const inquiryId = computed(() => route.params.id);
    const isEditMode = computed(() => !!inquiryId.value);
    
    // authStore에서 현재 projectId 가져오기
    const currentProjectId = computed(() => {
      return authStore.currentProject?.projectId || 0;
    });
    
    const inquiryForm = ref({
      title: '',
      type: '',
      content: ''
      // projectId는 제출 시 currentProjectId.value를 사용할 것이므로 여기서는 생략
    });
    
    const attachedFiles = ref([]);
    const existingAttachments = ref([]);
    const removedAttachmentIds = ref([]);
    const isSubmitting = ref(false);
    const error = ref(null);
    
    // 문의 정보 불러오기 (수정 모드인 경우)
    const loadInquiry = async () => {
      if (isEditMode.value) {
        isSubmitting.value = true;
        
        try {
          // 실제 API 호출 구현
          const response = await getInquiryById(inquiryId.value);
          
          if (response && response.data && response.data.data) {
            const inquiryData = response.data.data;
            
            // 폼 데이터 설정
            inquiryForm.value.title = inquiryData.inquiryTitle;
            inquiryForm.value.type = inquiryData.inquiryType;
            inquiryForm.value.content = inquiryData.inquiryContent;
            
            // 기존 첨부파일 설정
            if (inquiryData.attachments && inquiryData.attachments.length > 0) {
              existingAttachments.value = inquiryData.attachments.map(attachment => ({
                id: attachment.attachmentId,
                name: attachment.originalFileName
              }));
            }
          }
        } catch (err) {
          error.value = '문의 정보를 불러오는 중 오류가 발생했습니다.';
          console.error('문의 로딩 오류:', err);
        } finally {
          isSubmitting.value = false; // 로딩 시도 완료 후 항상 false로 설정
        }
      }
    };
    
    // 파일 업로드 처리
    const handleFileUpload = (event) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          attachedFiles.value.push(files[i]);
        }
      }
    };
    
    // 첨부파일 삭제
    const removeFile = (index) => {
      if (window.confirm('선택한 파일을 삭제하시겠습니까?')) {
        attachedFiles.value.splice(index, 1);
      }
    };
    
    // 기존 첨부파일 삭제
    const removeExistingAttachment = async (attachmentId) => {
      if (window.confirm('이 첨부파일을 정말 삭제하시겠습니까? 서버에서 영구적으로 삭제됩니다.')) {
        try {
          // API를 통해 첨부파일 삭제 요청
          await deleteAttachment(attachmentId);
          
          // 성공 시 UI에서 제거
          const index = existingAttachments.value.findIndex(att => att.id === attachmentId);
          if (index !== -1) {
            removedAttachmentIds.value.push(attachmentId);
            existingAttachments.value.splice(index, 1);
            alert('첨부파일이 삭제되었습니다.');
          }
        } catch (err) {
          error.value = '첨부파일 삭제 중 오류가 발생했습니다.';
          console.error('첨부파일 삭제 오류:', err);
          alert('첨부파일 삭제에 실패했습니다.');
        }
      }
    };
    
    // 첨부파일 다운로드
    const downloadAttachment = async (attachmentId, fileName) => {
      try {
        await downloadInquiryAttachment(attachmentId, fileName);
      } catch (err) {
        error.value = '첨부파일 다운로드 중 오류가 발생했습니다.';
        console.error('첨부파일 다운로드 오류:', err);
      }
    };
    
    // 파일 업로드 (실제 구현에서는 API 호출 필요)
    const uploadFiles = async () => {
      // 실제 구현에서는 파일 업로드 API 호출
      // const uploadedFiles = [];
      // for (const file of attachedFiles.value) {
      //   try {
      //     const result = await inquiryApi.uploadFile(file);
      //     uploadedFiles.push(result);
      //   } catch (error) {
      //     console.error('파일 업로드 오류:', error);
      //   }
      // }
      // return uploadedFiles;
      
      // 임시 구현 - 임의의 첨부파일 ID 생성
      return attachedFiles.value.map((file, index) => ({
        id: `new_attachment_${index + 1}`,
        name: file.name
      }));
    };
    
    // 폼 제출
    const submitInquiry = async () => {
      // 제출 중임을 표시
      isSubmitting.value = true;
      error.value = null;
      
      try {
        // FormData 객체 생성
        const formData = new FormData();
        
        // 1. 개별 필드로 FormData에 추가 (서버의 API 요구사항에 맞게 변경)
        formData.append('projectId', currentProjectId.value);
        formData.append('inquiryTitle', inquiryForm.value.title);
        formData.append('inquiryContent', inquiryForm.value.content);
        formData.append('inquiryType', inquiryForm.value.type);
        
        // 2. 첨부파일 추가
        if (attachedFiles.value.length > 0) {
          for (let i = 0; i < attachedFiles.value.length; i++) {
            formData.append('attachments', attachedFiles.value[i]);
          }
        }
        
        let response;
        
        // 3. 문의 생성 또는 수정 API 호출
        if (isEditMode.value) {
          // 수정 모드일 경우 inquiryId 추가
          formData.append('inquiryId', inquiryId.value);
          
          // 문의 수정 API 호출
          response = await saveInquiry(formData);
          
          if (response && response.data && response.data.success) {
            alert('문의가 수정되었습니다.');
            try {
              router.push(`/inquiries/${inquiryId.value}`);
            } catch (navErr) {
              console.error('라우팅 오류:', navErr);
              window.location.href = `/#/inquiries/${inquiryId.value}`;
            }
          } else {
            throw new Error('서버 응답이 유효하지 않습니다.');
          }
        } else {
          // 새 문의 등록 API 호출
          response = await saveInquiry(formData);
          
          if (response && response.data && response.data.success) {
            alert('문의가 등록되었습니다.');
            try {
              router.push('/inquiries');
            } catch (navErr) {
              console.error('라우팅 오류:', navErr);
              window.location.href = '/#/inquiries';
            }
          } else {
            throw new Error('서버 응답이 유효하지 않습니다.');
          }
        }
      } catch (err) {
        error.value = isEditMode.value
          ? '문의 수정 중 오류가 발생했습니다.'
          : '문의 등록 중 오류가 발생했습니다.';
        console.error('문의 제출 오류:', err);
      } finally {
        // 제출 완료 후 상태 업데이트
        isSubmitting.value = false;
      }
    };
    
    // 폼 취소
    const cancelForm = () => {
      // 현재 제출 중이면 취소 못하게 처리
      if (isSubmitting.value) {
        return;
      }
      
      // 즉시 실행을 위해 nextTick 사용 없이 직접 라우팅
      try {
        if (isEditMode.value) {
          router.push(`/inquiries/${inquiryId.value}`);
        } else {
          router.push('/inquiries');
        }
      } catch (err) {
        console.error('라우팅 오류:', err);
        // 대체 방법으로 window.location 사용
        if (isEditMode.value) {
          window.location.href = `/#/inquiries/${inquiryId.value}`;
        } else {
          window.location.href = '/#/inquiries';
        }
      }
    };
    
    // 내용 직접 업데이트 처리
    const updateContent = (event) => {
      inquiryForm.value.content = event.target.value;
    };
    
    // 컴포넌트 마운트 시 실행
    onMounted(() => {
      // projectId 확인
      if (!currentProjectId.value || currentProjectId.value === 0) {
        error.value = '프로젝트를 선택해주세요.';
        return;
      }
      loadInquiry();
    });
    
    return {
      inquiryForm,
      isEditMode,
      attachedFiles,
      existingAttachments,
      isSubmitting,
      error,
      handleFileUpload,
      removeFile,
      removeExistingAttachment,
      downloadAttachment,
      submitInquiry,
      cancelForm,
      updateContent
    };
  }
};
</script>

<style scoped>
.inquiry-management {
  padding: 20px;
}

.inquiry-container {
  margin-top: 20px;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

.act.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.download-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.download-btn:hover {
  background-color: #45a049;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.create-btn:hover {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn:hover {
  background-color: #d0d0d0;
}

.no-data {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
  color: #d32f2f;
}

.file-list {
  margin-top: 8px;
}

.file-item {
  margin-bottom: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-name {
  font-size: 0.9em;
  color: #666;
  margin-right: 10px;
}

.remove-btn {
  background-color: #F44336;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.remove-btn:hover {
  background-color: #d32f2f;
}
</style>