<template>
  <div class="project-management">
    <h1>프로젝트 관리</h1>

    <!-- 프로젝트 목록 테이블 -->
    <div class="project-list-container">
      <div class="filters">
        <div class="filter-group">
          <button @click="navigateToCreateProject" class="create-btn">새 프로젝트 추가</button>
        </div>
        <div class="filter-group">
          <label for="searchTypeSelect">검색 유형:</label>
          <select id="searchTypeSelect" v-model="searchType">
            <option v-for="type in availableSearchTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </option>
          </select>

          <label for="searchInput">검색어:</label>
          <input
            id="searchInput"
            type="text"
            v-model="searchQuery"
            placeholder="검색어 입력"
            @input="handleSearch"
            @keyup.enter="searchProjects"
          />

          <button @click="searchProjects" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>

      <div class="project-table-container">
        <table v-if="filteredProjects.length > 0" class="project-table">
          <thead>
            <tr>
              <th>번호</th>
              <th @click="sortBy('projectName')">프로젝트명 <span v-if="sortKey === 'projectName'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>설명</th>
              <th @click="sortBy('projectAdminUserEmail')">관리자 <span v-if="sortKey === 'projectAdminUserEmail'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('status')">상태 <span v-if="sortKey === 'status'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>생성자</th>
              <th @click="sortBy('createDate')">생성일 <span v-if="sortKey === 'createDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>최종 수정자</th>
              <th @click="sortBy('lastUpdateDate')">최종 수정일 <span v-if="sortKey === 'lastUpdateDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>작업</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(project, index) in filteredProjects" :key="project.projectId">
              <td>{{ calculateIndex(index) }}</td>
              <td>{{ project.projectName }}</td>
              <td>{{ truncateText(project.description, 30) }}</td>
              <td>{{ project.projectAdminUserEmail }}</td>
              <td>{{ formatStatus(project.status) }}</td>
              <td>{{ project.createUserEmail }}</td>
              <td>{{ formatDate(project.createDate) }}</td>
              <td>{{ project.updateUserEmail }}</td>
              <td>{{ formatDate(project.lastUpdateDate) }}</td>
              <td class="actions">
                <button @click="editProject(project.projectId)" class="action-btn edit-btn">수정</button>
                <button @click="confirmDeleteProject(project)" class="action-btn delete-btn">삭제</button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-else-if="isLoading" class="loading-message">
          <div class="loading-spinner"></div>
          <p>프로젝트 정보를 불러오는 중입니다...</p>
        </div>

        <div v-else-if="error" class="error-message">
          <p>{{ error }}</p>
        </div>

        <div v-else class="no-data-message">
          <p>프로젝트 데이터가 없습니다.</p>
        </div>
      </div>

      <!-- 페이지네이션 -->
      <div class="pagination" v-if="projects.length > 0">
        <button
          @click="goToPage(0)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &laquo;
        </button>
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &lt;
        </button>

        <span class="page-info">{{ displayPage }} / {{ totalPages || 1 }}</span>

        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &gt;
        </button>
        <button
          @click="goToPage(totalPages - 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &raquo;
        </button>
      </div>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay">
      <div class="modal-content">
        <h3>프로젝트 삭제 확인</h3>
        <p><strong>{{ selectedProject?.projectName }}</strong> 프로젝트를 삭제하시겠습니까?</p>
        <p class="warning">이 작업은 되돌릴 수 없습니다.</p>
        <div class="modal-actions">
          <button @click="handleDeleteProject" class="confirm-btn" :disabled="isDeleting">
            {{ isDeleting ? '삭제 중...' : '삭제' }}
          </button>
          <button @click="cancelDelete" class="cancel-btn">취소</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getProjects, deleteProject } from '@/api/project';
import { handleApiError } from '@/utils/errorHandler';

const router = useRouter();
const projects = ref([]);
const isLoading = ref(true);
const error = ref(null);
const searchQuery = ref('');
const appliedSearchQuery = ref(''); // 실제 검색에 적용된 검색어
const searchType = ref('projectName'); // 기본 검색 타입 설정
const appliedSearchType = ref('projectName'); // 실제 검색에 적용된 검색 타입
const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록

// 삭제 관련 상태 변수
const showDeleteModal = ref(false);
const selectedProject = ref(null);
const isDeleting = ref(false);

// 페이지네이션 관련 상태
const currentPage = ref(0); // 서버 페이지네이션은 0부터 시작
const itemsPerPage = 10; // 페이지당 항목 수
const totalElements = ref(0); // 전체 항목 수
const totalPages = ref(0); // 전체 페이지 수

// 페이지 번호 표시용 (1부터 시작하는 페이지 번호)
const displayPage = computed(() => {
  return currentPage.value + 1;
});

// 인덱스 계산 함수
const calculateIndex = (index) => {
  // 마지막 번호부터 시작하도록 변경
  return totalElements.value - (currentPage.value * itemsPerPage + index);
};

// 정렬 관련 상태
const sortKey = ref('projectId');
const sortOrder = ref('desc');

// 정렬 함수
const sortBy = (key) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
};

// 정렬된 프로젝트 목록
const sortedProjects = computed(() => {
  const sortedArray = [...projects.value];

  sortedArray.sort((a, b) => {
    let aValue = a[sortKey.value];
    let bValue = b[sortKey.value];

    // 날짜 형식 처리
    if (sortKey.value.includes('Date') && aValue && bValue) {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // 문자열 처리
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    // null 또는 undefined 처리
    if (aValue === null || aValue === undefined) return sortOrder.value === 'asc' ? -1 : 1;
    if (bValue === null || bValue === undefined) return sortOrder.value === 'asc' ? 1 : -1;

    // 정렬 순서에 따라 비교
    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return sortedArray;
});

// 검색 결과 필터링
const filteredProjects = computed(() => {
  if (!appliedSearchQuery.value) {
    return sortedProjects.value;
  }

  const query = appliedSearchQuery.value.toLowerCase();

  return sortedProjects.value.filter(project => {
    // 검색 타입에 따라 다른 필드 검색
    switch (appliedSearchType.value) {
      case 'projectName':
        return project.projectName && project.projectName.toLowerCase().includes(query);
      case 'description':
        return project.description && project.description.toLowerCase().includes(query);
      case 'projectAdminUserEmail':
        return project.projectAdminUserEmail && project.projectAdminUserEmail.toLowerCase().includes(query);
      case 'createUserEmail':
        return project.createUserEmail && project.createUserEmail.toLowerCase().includes(query);
      case 'createDate':
        return project.createDate && project.createDate.toLowerCase().includes(query);
      case 'lastUpdateDate':
        return project.lastUpdateDate && project.lastUpdateDate.toLowerCase().includes(query);
      default:
        // 기본적으로 프로젝트명과 설명 검색
        return (project.projectName && project.projectName.toLowerCase().includes(query)) ||
               (project.description && project.description.toLowerCase().includes(query));
    }
  });
});

// 프로젝트 생성 페이지로 이동
const navigateToCreateProject = () => {
  router.push({ name: 'project-form' });
};

// 프로젝트 수정 페이지로 이동
const editProject = (projectId) => {
  router.push({
    name: 'project-form',
    params: { projectId: projectId.toString() }
  });
};

// 삭제 확인 모달 표시
const confirmDeleteProject = (project) => {
  selectedProject.value = project;
  showDeleteModal.value = true;
};

// 삭제 취소
const cancelDelete = () => {
  showDeleteModal.value = false;
  selectedProject.value = null;
};

// 프로젝트 삭제 처리
const handleDeleteProject = async () => {
  if (!selectedProject.value) return;

  isDeleting.value = true;

  try {
    // 삭제할 프로젝트 ID 가져오기
    const projectId = selectedProject.value.projectId;

    // 삭제 API 호출
    await deleteProject(projectId);

    // 성공 메시지 표시
    alert(`${selectedProject.value.projectName} 프로젝트가 성공적으로 삭제되었습니다.`);

    // 모달 닫기
    showDeleteModal.value = false;
    selectedProject.value = null;

    // 삭제 후 목록 새로고침
    await fetchProjects();
  } catch (err) {
    // 에러 처리
    error.value = handleApiError(err, '프로젝트 삭제 중 오류가 발생했습니다.');
    console.error('프로젝트 삭제 실패:', err);
  } finally {
    isDeleting.value = false;
  }
};

// 페이지 이동 함수
const goToPage = (page) => {
  // 페이지 범위 검사 (totalPages가 0이면 최소 1페이지로 간주)
  const maxPage = Math.max(totalPages.value - 1, 0);
  if (page < 0 || page > maxPage) {
    console.warn(`페이지 범위 초과: ${page}, 최대 페이지: ${maxPage}`);
    return;
  }

  // 페이지 변경 및 데이터 로드
  currentPage.value = page;
  fetchProjects();

  // 상단으로 스크롤
  window.scrollTo(0, 0);
};

// 날짜 관련 검색 타입인지 확인하는 함수
const isDateSearchType = (type) => {
  // 날짜 관련 필드명 목록
  const dateFields = ['createDate', 'lastUpdateDate'];
  return dateFields.includes(type);
};

// 날짜 형식 변환 함수 (2025. 05. 17. -> 2025-05-17)
const formatDateForSearch = (dateString) => {
  if (!dateString) return '';

  // 정규식을 사용하여 날짜 형식 변환
  // 2025. 05. 17. 또는 2025.05.17. 또는 2025.5.17. 등의 형식을 처리
  const dateMatch = dateString.match(/(\d{4})[.\s-]*(\d{1,2})[.\s-]*(\d{1,2})/);

  if (dateMatch) {
    const year = dateMatch[1];
    // 월과 일이 한 자리 수인 경우 앞에 0을 추가
    const month = dateMatch[2].padStart(2, '0');
    const day = dateMatch[3].padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 매칭되지 않으면 원래 문자열 반환
  return dateString;
};

// 검색 버튼 클릭 시 실행되는 함수
const searchProjects = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동

  // 날짜 관련 검색 타입인 경우 날짜 형식 변환
  if (searchType.value && isDateSearchType(searchType.value) && searchQuery.value) {
    // 원본 검색어 저장
    const originalQuery = searchQuery.value;
    // 변환된 날짜 형식으로 검색어 업데이트
    const formattedDate = formatDateForSearch(originalQuery);

    if (formattedDate !== originalQuery) {
      searchQuery.value = formattedDate;
    }
  }

  // 검색 쿼리와 타입을 적용
  appliedSearchQuery.value = searchQuery.value;
  appliedSearchType.value = searchType.value;

  // 서버에서 데이터 불러오기
  fetchProjects();
};

// 검색 초기화 함수
const resetSearch = () => {
  searchQuery.value = '';
  appliedSearchQuery.value = '';
  // 서버에서 제공하는 검색 유형이 있으면 첫 번째 값을 사용, 없으면 기본값 사용
  if (availableSearchTypes.value.length > 0) {
    searchType.value = availableSearchTypes.value[0].value;
    appliedSearchType.value = availableSearchTypes.value[0].value;
  } else {
    searchType.value = 'projectName';
    appliedSearchType.value = 'projectName';
  }
  currentPage.value = 0;
  fetchProjects();
};

// 검색 처리 (디바운스 적용)
let searchTimeout;
const handleSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // 검색어가 비어있을 때는 아무 동작도 하지 않음
    // 사용자가 직접 검색 버튼을 클릭하거나 Enter 키를 눌러야 데이터가 로드됨
    // 검색어 입력 중에는 화면이 바뀌지 않음
  }, 300);
};

// 프로젝트 목록 불러오기
const fetchProjects = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    // 페이지네이션 파라미터 추가하여 API 호출
    const params = {
      page: currentPage.value,
      size: itemsPerPage,
      sort: `${sortKey.value},${sortOrder.value}`,
      searchType: appliedSearchType.value,
      searchKeyword: appliedSearchQuery.value
    };
    
    // 날짜 검색 유형인 경우, 날짜 형식을 'YYYY-MM-DD'로 변환
    if (appliedSearchQuery.value && isDateSearchType(appliedSearchType.value)) {
      params.searchKeyword = formatDateForSearch(appliedSearchQuery.value);
    }
    
    const response = await getProjects(params);

    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.success && response.data) {
      const responseData = response.data;

      // 페이지네이션 정보 업데이트
      if (responseData.totalPages !== undefined) {
        totalPages.value = responseData.totalPages;
      } else {
        // 서버에서 totalPages가 없는 경우 계산
        const total = responseData.totalElements || (responseData.content?.length || 0);
        totalPages.value = Math.ceil(total / itemsPerPage) || 1;
      }

      totalElements.value = responseData.totalElements || 0;

      // 검색 유형 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        availableSearchTypes.value = responseData.availableSearchTypes;

        // 검색 유형이 없는 경우 기본값 설정
        if (availableSearchTypes.value.length > 0 && !searchType.value) {
          searchType.value = availableSearchTypes.value[0].value;
          appliedSearchType.value = availableSearchTypes.value[0].value;
        }
      }

      // 프로젝트 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        projects.value = responseData.content;

        // 데이터가 있지만 totalPages가 0인 경우 최소 1로 설정
        if (projects.value.length > 0 && totalPages.value === 0) {
          totalPages.value = 1;
        }
      } else if (Array.isArray(responseData)) {
        // 이전 응답 구조 처리 (호환성 유지)
        projects.value = responseData;
        totalPages.value = 1;
        totalElements.value = responseData.length;
      } else {
        console.warn('프로젝트 데이터가 없거나 배열이 아닙니다.');
        projects.value = [];
      }
    } else {
      // 기존 응답 구조 처리 (호환성 유지)
      projects.value = response.data || [];
      totalPages.value = 1;
      totalElements.value = projects.value.length;
    }
  } catch (err) {
    error.value = handleApiError(err, '프로젝트 목록을 불러오는 중 오류가 발생했습니다.');
    console.error('프로젝트 목록 조회 실패:', err);
    projects.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 상태 포맷팅
const formatStatus = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'INACTIVE': '비활성',
    'DELETED': '삭제됨'
  };
  return statusMap[status] || status;
};

const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return new Intl.DateTimeFormat('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// 텍스트 길이 제한 함수
const truncateText = (text, maxLength) => {
  if (!text) return '-';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// 정렬 또는 페이지 변경 시 데이터 다시 로드
watch([sortKey, sortOrder], () => {
  // 정렬 변경 시 첫 페이지로 이동
  currentPage.value = 0;
  fetchProjects();
});

// 컴포넌트 마운트 시 프로젝트 목록 로드
onMounted(() => {
  fetchProjects();
});
</script>

<style scoped>
.project-management {
  padding: 20px;
}

.project-list-container {
  margin-top: 20px;
}

/* 필터 스타일 */
.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group input, .filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background-color: #45a049;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: #0b7dda;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

.project-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.project-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
}

.project-table th {
  background-color: #f2f2f2;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  cursor: pointer;
  user-select: none;
}

.project-table th:hover {
  background-color: #e6e6e6;
}

.project-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #ddd;
}

.project-table tr:hover {
  background-color: #f5f5f5;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  min-width: 120px;
}

/* 액션 버튼 스타일 */
.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.edit-btn {
  background-color: #fff8e1;
  color: #ffa000;
}

.edit-btn:hover {
  background-color: #ffecb3;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

.delete-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 로딩 및 에러 메시지 스타일 */
.loading-message,
.no-data-message,
.no-projects-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  text-align: center;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin: 20px 0;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

/* 디버깅 정보 스타일 */
.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning {
  color: #f44336;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #F44336;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #da190b;
}

.confirm-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-btn:hover {
  background-color: #d0d0d0;
}
</style>
