<template>
  <div class="qna-detail-container">
    <!-- 로딩 및 에러 처리 -->
    <div v-if="qnaStore.loading" class="loading">
      <div class="spinner"></div>
      <p>데이터를 불러오는 중...</p>
    </div>
    
    <div v-else-if="qnaStore.error" class="error">
      <p>{{ qnaStore.error }}</p>
      <button @click="fetchQuestion" class="retry-button">다시 시도</button>
    </div>

    <div v-else-if="!qnaStore.currentQuestion" class="empty-state">
      <p>질문을 찾을 수 없습니다.</p>
      <button @click="goBack" class="back-button">목록으로 돌아가기</button>
    </div>

    <div v-else class="question-detail">
      <!-- 질문 상세 정보 -->
      <div class="question-header">
        <h2 class="question-title">{{ qnaStore.currentQuestion.title }}</h2>
        <div class="question-meta">
          <span class="author">작성자: {{ qnaStore.currentQuestion.createUserEmail }}</span>
          <span class="date">등록일: {{ formatDate(qnaStore.currentQuestion.createDate) }}</span>
        </div>
      </div>

      <div class="question-content">
        <h3>질문 내용</h3>
        <div class="content-body">{{ qnaStore.currentQuestion.content }}</div>
      </div>

      <!-- 답변 정보 (있는 경우) -->
      <div v-if="qnaStore.currentQuestion.answer && qnaStore.currentQuestion.answer.answerContent" class="answer-container">
        <h3>답변</h3>
        <div class="answer-meta">
          <span class="author">답변자: {{ qnaStore.currentQuestion.answer.createUserEmail || '관리자' }}</span>
          <span class="date">답변일: {{ formatDate(qnaStore.currentQuestion.answer.createDate) }}</span>
        </div>
        <div class="answer-content">
          {{ qnaStore.currentQuestion.answer.answerContent }}
        </div>
      </div>

      <!-- 액션 버튼 -->
      <div class="action-buttons">
        <button @click="goBack" class="back-button">목록</button>
        <button @click="editQuestion" class="edit-button">수정</button>
        <button @click="confirmDeleteQuestion" class="delete-button">삭제</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQnaStore } from '@/stores/qnaStore';

const route = useRoute();
const router = useRouter();
const qnaStore = useQnaStore();

// 컴포넌트 마운트 시 질문 상세 데이터 조회
onMounted(() => {
  fetchQuestion();
});

// 질문 데이터 조회
const fetchQuestion = async () => {
  const questionId = route.params.questionId;
  if (questionId) {
    await qnaStore.fetchQuestionByIdAction(questionId);
  }
};

// 목록 화면으로 이동
const goBack = () => {
  router.push({ name: 'qna-management' });
};

// 질문 수정 화면으로 이동
const editQuestion = () => {
  router.push({ 
    name: 'qna-edit', 
    params: { questionId: qnaStore.currentQuestion.qnaQuestionId } 
  });
};

// 질문 삭제 확인
const confirmDeleteQuestion = () => {
  if (confirm('정말로 이 질문을 삭제하시겠습니까?')) {
    deleteQuestion();
  }
};

// 질문 삭제 처리
const deleteQuestion = async () => {
  const questionId = qnaStore.currentQuestion.qnaQuestionId;
  const result = await qnaStore.deleteQuestionAction(questionId);
  
  if (result.success) {
    alert('질문이 삭제되었습니다.');
    router.push({ name: 'qna-management' });
  } else {
    alert(`삭제 실패: ${result.error || '알 수 없는 오류가 발생했습니다.'}`);
  }
};

// 날짜 포맷팅 함수
const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};
</script>

<style scoped>
.qna-detail-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.question-header {
  padding: 20px;
  border-bottom: 1px solid #ddd;
  background-color: #f8f9fa;
  border-radius: 4px 4px 0 0;
}

.question-title {
  font-size: 1.5rem;
  margin: 0 0 10px 0;
  color: #333;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  color: #6c757d;
  font-size: 0.9rem;
}

.question-content {
  padding: 20px;
  border-bottom: 1px solid #ddd;
}

.question-content h3 {
  font-size: 1.2rem;
  margin: 0 0 10px 0;
  color: #495057;
}

.content-body {
  white-space: pre-line;
  line-height: 1.6;
}

.answer-container {
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.answer-container h3 {
  font-size: 1.2rem;
  margin: 0 0 10px 0;
  color: #4a6cf7;
}

.answer-meta {
  display: flex;
  justify-content: space-between;
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.answer-content {
  white-space: pre-line;
  line-height: 1.6;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
}

.back-button, .edit-button, .delete-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.back-button {
  background-color: #6c757d;
  color: white;
}

.edit-button {
  background-color: #28a745;
  color: white;
}

.delete-button {
  background-color: #dc3545;
  color: white;
}

.loading, .error, .empty-state {
  padding: 40px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-top: 20px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a6cf7;
  width: 30px;
  height: 30px;
  margin: 0 auto 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  padding: 8px 16px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}
</style>
